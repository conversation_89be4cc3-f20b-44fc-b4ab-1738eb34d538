==================================0==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
var RES_PATH;
var CARD_RES_PATCH;
var XYSCALE;
var beginoffset;
var cb;
var startPosSet;
var movetPosSet;
var previouPosSet;
function initCuo_SG( __ARGV_1__ ){ __FUNC_1__ }
function beginRubCardAction( __ARGV_2__ ){ __FUNC_2__ }
function update( __ARGV_4__ ){ __FUNC_4__ }
function updateX( __ARGV_5__ ){ __FUNC_5__ }
function updateXX( __ARGV_6__ ){ __FUNC_6__ }
function angle( __ARGV_7__ ){ __FUNC_7__ }
function registerTouchEvend( __ARGV_8__ ){ __FUNC_8__ }
function setRubEnd( __ARGV_12__ ){ __FUNC_12__ }
function RubCardAction( __ARGV_13__ ){ __FUNC_13__ }
function spineEnd( __ARGV_14__ ){ __FUNC_14__ }
var CuoLayer_sangong;
RES_PATH = "poker/";
CARD_RES_PATCH = (RES_PATH + "bigcard/");
XYSCALE = 1.1;
beginoffset = 70;
cb = [];
CuoLayer_sangong = cc.Layer.extend({ctor:function () { __FUNC_17__ }});
---------------------------------------
==========================================================================E
==================================1==================================S
------------------Argv------------------
node,data,cb,nodeBase,self,cardType,flowerType,clipper,szCardName,faceNode,cardImg,writeImg,writeImg2,pbImg2,stencil,stencil2,stencil3,stencil4,stencil5
---------------------------------------
----------------Content----------------
data = Number(data);
if(isNaN(data)){
return undefined;
}
_local0=this;
_local0.widget={};
print(("" + data));
_local1=Math.ceil((data / 4));
_local2=(data % 4);
if((_local2 == 0)){
_local2=4;
}
_local0.m_endRub=false;
_local3=cc.ClippingNode.create();
_local3.setPosition(0,50);
_local0.widget.Panel_out=_local3;
_local0.widget.Panel_out.m_srcPosition=cc.p(0,50);
(_local1 >= 10)?_local4=(((CARD_RES_PATCH + _local2) + _local1) + ".png"):_local4=(((CARD_RES_PATCH + _local2) + ("0" + _local1)) + ".png");
print(("++++++++++++++++++++= szCardName = " + _local4));
_local5=new cc.Node();
_local6=ccui.ImageView.create();
_local6.loadTexture(_local4);
_local6.setPosition(cc.p(340,-268));
_local6.setRotation(90);
_local5.addChild(_local6,10);
_local6.setScaleY(XYSCALE);
_local6.setScaleX(XYSCALE);
_local0.widget.Panel_out.Image_card=_local6;
_local0.widget.Panel_out.Image_card.m_srcPositionY=-268;
_local0.widget.Panel_out.Image_card.m_srcPositionX=340;
_local7=ccui.ImageView.create();
_local7.loadTexture((RES_PATH + "bigcard/zhedang2.png"),0);
_local7.setAnchorPoint(cc.p(0,1));
_local7.setPosition(cc.p(0,1880));
_local6.addChild(_local7,11);
_local0.widget.Panel_out.Image_card.writeImg1=_local7;
_local8=ccui.ImageView.create();
_local8.loadTexture((RES_PATH + "bigcard/zhedang2.png"),0);
_local8.setAnchorPoint(cc.p(0,1));
_local8.setPosition(cc.p(446,0));
_local8.setRotation(180);
_local6.addChild(_local8,11);
_local0.widget.Panel_out.Image_card.writeImg2=_local8;
_local7.setVisible(false);
_local8.setVisible(false);
_local9=ccui.ImageView.create();
_local9.loadTexture((CARD_RES_PATCH + "cardBack.png"),0);
_local9.setPosition(cc.p(340,224));
_local9.setRotation(90);
_local9.setScaleY(XYSCALE);
_local9.setScaleX(XYSCALE);
_local5.addChild(_local9);
_local0.widget.Panel_out.Image_card_0=_local9;
_local0.widget.Panel_out.Image_card_0.m_srcPositionY=224;
_local0.widget.Panel_out.Image_card_0.m_srcPositionX=340;
_local10=cc.Sprite.create((RES_PATH + "bigcard/background.png"));
_local10.setAnchorPoint(cc.p(0.5,1));
_local10.setPosition(cc.p(340,0));
_local11=cc.Sprite.create((RES_PATH + "bigcard/zhezhao.png"));
_local11.setAnchorPoint(cc.p(0,0));
_local11.setPosition(cc.p(318.84,720));
_local10.addChild(_local11);
_local11.setScaleX(0.2);
_local12=cc.Sprite.create((RES_PATH + "bigcard/zhezhao.png"));
_local12.setAnchorPoint(cc.p(0,0));
_local12.setPosition(cc.p(1015.16,720));
_local10.addChild(_local12);
_local12.setScaleX(-0.2);
_local13=cc.Sprite.create((RES_PATH + "bigcard/zhezhao.png"));
_local13.setAnchorPoint(cc.p(0,0));
_local13.setPosition(cc.p(0,330.88));
_local10.addChild(_local13);
_local13.setScaleY(0.2);
_local14=cc.Sprite.create((RES_PATH + "bigcard/zhezhao.png"));
_local14.setAnchorPoint(cc.p(0,0));
_local14.setPosition(cc.p(0,1109.12));
_local10.addChild(_local14);
_local14.setScaleY(-0.2);
_local0.widget.Panel_out.m_stencil5=_local14;
_local0.widget.Panel_out.m_stencil4=_local13;
_local0.widget.Panel_out.m_stencil2=_local11;
_local0.widget.Panel_out.m_stencil3=_local12;
_local0.widget.Panel_out.m_stencil=_local10;
_local3.setStencil(_local10);
_local3.setInverted(true);
_local3.setAlphaThreshold(0.1);
_local3.addChild(_local5);
node.addChild(_local3,100);
registerTouchEvend(node,cb,nodeBase);
beginRubCardAction(node);
---------------------------------------
==========================================================================E
==================================2==================================S
------------------Argv------------------
node,updateCall
---------------------------------------
----------------Content----------------
_local0=function () { __FUNC_3__ };
node.schedule(_local0,0);
---------------------------------------
==========================================================================E
==================================3==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
update();
---------------------------------------
==========================================================================E
==================================4==================================S
------------------Argv------------------
self,diffy
---------------------------------------
----------------Content----------------
if(((movetPosSet && ((angle(startPosSet,movetPosSet) >= -90) && (angle(startPosSet,movetPosSet) <= -45))) || (movetPosSet && ((angle(startPosSet,movetPosSet) >= 45) && (angle(startPosSet,movetPosSet) <= 90))))&&((movetPosSet.y - startPosSet.y) > 0)){
_local0=this;
_local0.widget.Panel_out.Image_card.m_srcPositionY=-268;
_local0.widget.Panel_out.Image_card.m_srcPositionX=340;
_local0.widget.Panel_out.m_stencil.setAnchorPoint(cc.p(0.5,1));
_local0.widget.Panel_out.m_stencil.setPosition(cc.p(340,0));
_local1=(_local0.widget.Panel_out.getPositionY() - _local0.widget.Panel_out.m_srcPosition.y);
_local0.widget.Panel_out.Image_card.setPositionY((_local0.widget.Panel_out.Image_card.m_srcPositionY + _local1));
_local0.widget.Panel_out.Image_card_0.setPositionY((_local0.widget.Panel_out.Image_card_0.m_srcPositionY - _local1));
_local0.widget.Panel_out.Image_card.setPositionX(_local0.widget.Panel_out.Image_card.m_srcPositionX);
_local0.widget.Panel_out.Image_card_0.setPositionX(_local0.widget.Panel_out.Image_card_0.m_srcPositionX);
}
updateX();
updateXX();
---------------------------------------
==========================================================================E
==================================5==================================S
------------------Argv------------------
self,diffy
---------------------------------------
----------------Content----------------
if(movetPosSet && (angle(startPosSet,movetPosSet) > -45) && (angle(startPosSet,movetPosSet) < 45) && ((movetPosSet.x - startPosSet.x) > 0)){
_local0=this;
_local0.widget.Panel_out.Image_card.m_srcPositionY=224;
_local0.widget.Panel_out.Image_card.m_srcPositionX=-340;
_local0.widget.Panel_out.m_stencil.setAnchorPoint(cc.p(1,0.5));
_local0.widget.Panel_out.m_stencil.setPosition(cc.p(0,224));
_local1=(_local0.widget.Panel_out.getPositionX() - _local0.widget.Panel_out.m_srcPosition.x);
_local0.widget.Panel_out.Image_card.setPositionX((_local0.widget.Panel_out.Image_card.m_srcPositionX + _local1));
_local0.widget.Panel_out.Image_card_0.setPositionX((_local0.widget.Panel_out.Image_card_0.m_srcPositionX - _local1));
_local0.widget.Panel_out.Image_card.setPositionY(_local0.widget.Panel_out.Image_card.m_srcPositionY);
_local0.widget.Panel_out.Image_card_0.setPositionY(_local0.widget.Panel_out.Image_card_0.m_srcPositionY);
cc.log("",_local1);
}
---------------------------------------
==========================================================================E
==================================6==================================S
------------------Argv------------------
self,diffy
---------------------------------------
----------------Content----------------
if(movetPosSet && (angle(startPosSet,movetPosSet) > -45) && (angle(startPosSet,movetPosSet) < 45) && ((movetPosSet.x - startPosSet.x) < 0)){
_local0=this;
_local0.widget.Panel_out.Image_card.m_srcPositionY=224;
_local0.widget.Panel_out.Image_card.m_srcPositionX=940;
_local0.widget.Panel_out.m_stencil.setAnchorPoint(cc.p(0,0.5));
_local0.widget.Panel_out.m_stencil.setPosition(cc.p(634,224));
_local1=(_local0.widget.Panel_out.getPositionX() - _local0.widget.Panel_out.m_srcPosition.x);
_local0.widget.Panel_out.Image_card.setPositionX((_local0.widget.Panel_out.Image_card.m_srcPositionX + _local1));
_local0.widget.Panel_out.Image_card_0.setPositionX((_local0.widget.Panel_out.Image_card_0.m_srcPositionX - _local1));
_local0.widget.Panel_out.Image_card.setPositionY(_local0.widget.Panel_out.Image_card.m_srcPositionY);
_local0.widget.Panel_out.Image_card_0.setPositionY(_local0.widget.Panel_out.Image_card_0.m_srcPositionY);
}
---------------------------------------
==========================================================================E
==================================7==================================S
------------------Argv------------------
start,end,diff_x,diff_y
---------------------------------------
----------------Content----------------
_local0=(end.x - start.x);
_local1=(end.y - start.y);
return ((360 * Math.atan((_local1 / _local0))) / (2 * Math.PI));
---------------------------------------
==========================================================================E
==================================8==================================S
------------------Argv------------------
node,cb,nodeBase,self
---------------------------------------
----------------Content----------------
_aliased6535=this;
cc.eventManager.addListener({event:cc.EventListener.TOUCH_ONE_BY_ONE,onTouchBegan:function () { __FUNC_9__ },onTouchMoved:function () { __FUNC_10__ },onTouchEnded:function () { __FUNC_11__ }},undefined);
---------------------------------------
==========================================================================E
==================================9==================================S
------------------Argv------------------
touch,event
---------------------------------------
----------------Content----------------
if((_aliased6535.m_endRub == true)){
return true;
}
_aliased6535.widget.Panel_out.stopAllActions();
_aliased6535.widget.Panel_out.m_touchSrcPosition=cc.p(_aliased6535.widget.Panel_out.getPosition());
return true;
---------------------------------------
==========================================================================E
==================================10==================================S
------------------Argv------------------
touch,event,startPos,movetPos,diffy,diffx
---------------------------------------
----------------Content----------------
if(_aliased6535.m_endRub){
return undefined;
}
_local0=touch.getStartLocation();
_local1=touch.getLocation();
startPosSet = _local0;
movetPosSet = _local1;
if(!(_aliased6535.widget.Panel_out.m_touchSrcPosition)){
_aliased6535.widget.Panel_out.m_touchSrcPosition=cc.p(_aliased6535.widget.Panel_out.getPosition());
}
_local2=(_aliased6535.widget.Panel_out.m_touchSrcPosition.y + (_local1.y - _local0.y));
_local3=(_aliased6535.widget.Panel_out.m_touchSrcPosition.x + (_local1.x - _local0.x));
if((((angle(_local0,_local1) >= -90) && (angle(_local0,_local1) <= -45)) || ((angle(_local0,_local1) >= 45) && (angle(_local0,_local1) <= 90)))&&((movetPosSet.y - startPosSet.y) > 0)){
_aliased6535.widget.Panel_out.setPositionX(_aliased6535.widget.Panel_out.m_touchSrcPosition.x);
if(((_local2 - _aliased6535.widget.Panel_out.m_srcPosition.y) < beginoffset)){
_aliased6535.widget.Panel_out.setPositionY((_aliased6535.widget.Panel_out.m_srcPosition.y + beginoffset));
} else if(((_local2 - _aliased6535.widget.Panel_out.m_srcPosition.y) > 268)){
_aliased6535.widget.Panel_out.setPositionY((_aliased6535.widget.Panel_out.m_srcPosition.y + 268));
setRubEnd(undefined,undefined,undefined);
} else {
}
_aliased6535.widget.Panel_out.setPositionY(_local2);
} else if((angle(_local0,_local1) > -45) && (angle(_local0,_local1) < 45) && ((movetPosSet.x - startPosSet.x) > 0)){
_aliased6535.widget.Panel_out.setPositionY(_aliased6535.widget.Panel_out.m_touchSrcPosition.y);
if(((_local3 - _aliased6535.widget.Panel_out.m_srcPosition.x) < beginoffset)){
_aliased6535.widget.Panel_out.setPositionX((_aliased6535.widget.Panel_out.m_srcPosition.x + beginoffset));
} else if(((_local3 - _aliased6535.widget.Panel_out.m_srcPosition.x) > 368)){
_aliased6535.widget.Panel_out.setPositionX((_aliased6535.widget.Panel_out.m_srcPosition.x + 368));
setRubEnd(undefined,undefined,undefined);
} else {
}
_aliased6535.widget.Panel_out.setPositionX(_local3);
} else if((angle(_local0,_local1) > -45) && (angle(_local0,_local1) < 45) && ((movetPosSet.x - startPosSet.x) < 0)){
_aliased6535.widget.Panel_out.setPositionY(_aliased6535.widget.Panel_out.m_touchSrcPosition.y);
if(((_aliased6535.widget.Panel_out.m_srcPosition.x - _local3) < beginoffset)){
_aliased6535.widget.Panel_out.setPositionX((_aliased6535.widget.Panel_out.m_srcPosition.x - beginoffset));
} else if(((_aliased6535.widget.Panel_out.m_srcPosition.x - _local3) > 368)){
_aliased6535.widget.Panel_out.setPositionX((_aliased6535.widget.Panel_out.m_srcPosition.x - 368));
setRubEnd(undefined,undefined,undefined);
} else {
}
_aliased6535.widget.Panel_out.setPositionX(_local3);
}
---------------------------------------
==========================================================================E
==================================11==================================S
------------------Argv------------------
touch,event
---------------------------------------
----------------Content----------------
if((_aliased6535.m_endRub == false)){
_aliased6535.widget.Panel_out.runAction(cc.MoveTo.create(0.1,_aliased6535.widget.Panel_out.m_srcPosition));
}
---------------------------------------
==========================================================================E
==================================12==================================S
------------------Argv------------------
node,cb,nodeBase
---------------------------------------
----------------Content----------------
this.m_endRub=true;
RubCardAction(node,cb,nodeBase);
---------------------------------------
==========================================================================E
==================================13==================================S
------------------Argv------------------
node,cb,nodeBase
---------------------------------------
----------------Content----------------
this.widget.Panel_out.Image_card_0.setVisible(false);
this.widget.Panel_out.Image_card.runAction(cc.ScaleTo.create(0.4,(XYSCALE - 0.1),(XYSCALE - 0.1)));
spineEnd(node,cb,nodeBase);
---------------------------------------
==========================================================================E
==================================14==================================S
------------------Argv------------------
node,cb,nodeBase,self,updateCall,act
---------------------------------------
----------------Content----------------
_local0=this;
_local1=function () { __FUNC_15__ };
node.unscheduleAllCallbacks();
_local0.widget.Panel_out.setPosition(_local0.widget.Panel_out.m_srcPosition);
_local0.widget.Panel_out.Image_card.setPositionY(_local0.widget.Panel_out.Image_card_0.m_srcPositionY);
_local0.widget.Panel_out.Image_card.setPositionX(_local0.widget.Panel_out.Image_card_0.m_srcPositionX);
_local0.widget.Panel_out.Image_card_0.setVisible(false);
_local2=cc.Sequence.create(cc.FadeOut.create(0.5),cc.DelayTime.create(0.5),cc.CallFunc.create(function () { __FUNC_16__ }));
_local0.widget.Panel_out.Image_card.writeImg1.runAction(_local2);
_local0.widget.Panel_out.Image_card.writeImg2.runAction(cc.FadeOut.create(0.5));
---------------------------------------
==========================================================================E
==================================15==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
update();
---------------------------------------
==========================================================================E
==================================16==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
undefined.removeFromParent();
if(undefined){
(undefined)();
}
---------------------------------------
==========================================================================E
==================================17==================================S
------------------Argv------------------
cb,UI,block,node,pl
---------------------------------------
----------------Content----------------
this._super();
_local0=ccs.load("CuoLayer_sangong.json");
this.addChild(_local0.node,50);
_local1=_local0.node.getChildByName("block");
setWgtLayout(_local1,[1,1],[0.5,0.5],[0,0],true);
_local2=_local1.getChildByName("node");
_local2.setPosition(cc.p(300,150));
_local3=getUIPlayer(0);
print(("" + _local3.mjhand[2]));
initCuo_SG(_local2,_local3.mjhand[2],cb,this);
---------------------------------------
==========================================================================E
