==================================0==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
var timeTag;
var SSS_GX_MAX_HAND_COUNT;
var nDaoArr;
var nDunArr;
function SelfUidSSS( __ARGV_1__ ){ __FUNC_1__ }
function isLookOnSSS( __ARGV_2__ ){ __FUNC_2__ }
function getOffByIndex_thirteenGX( __ARGV_3__ ){ __FUNC_3__ }
function getUiOffByUid_thirteenGX( __ARGV_4__ ){ __FUNC_4__ }
function getPlayerIndex_thirteenGX( __ARGV_5__ ){ __FUNC_5__ }
function getNode_thirteenGX( __ARGV_6__ ){ __FUNC_6__ }
function getUIPlayer_thirteenGX( __ARGV_7__ ){ __FUNC_7__ }
function getUIHeadByOff_thirteenGX( __ARGV_8__ ){ __FUNC_8__ }
function setWxHead_thirteenGX( __ARGV_9__ ){ __FUNC_9__ }
function setTuoGuanCountDown_thirteenGX( __ARGV_10__ ){ __FUNC_10__ }
function setTuoGuanCountDownCallback_thirteenGX( __ARGV_11__ ){ __FUNC_11__ }
function SetUserVisible_thirteenGX( __ARGV_12__ ){ __FUNC_12__ }
function InitUserHandUI_thirteenGX( __ARGV_13__ ){ __FUNC_13__ }
function InitUserCoinAndName_thirteenGX( __ARGV_14__ ){ __FUNC_14__ }
function getUserNameText_thirteenGX( __ARGV_15__ ){ __FUNC_15__ }
function getCoinVisible_thirteenGX( __ARGV_16__ ){ __FUNC_16__ }
function updateCoinDisplay_thirteenGX( __ARGV_17__ ){ __FUNC_17__ }
function getScoreVisible_thirteenGX( __ARGV_18__ ){ __FUNC_18__ }
function updateScoreDisplay_thirteenGX( __ARGV_19__ ){ __FUNC_19__ }
function ShowAllCard_thirteenGX( __ARGV_20__ ){ __FUNC_20__ }
function TxtScoreFormat_thirteenGX( __ARGV_21__ ){ __FUNC_21__ }
function ShowFourCard_thirteenGX( __ARGV_22__ ){ __FUNC_22__ }
function sendAniInit_thirteenGX( __ARGV_23__ ){ __FUNC_23__ }
function initCard_thirteenGX( __ARGV_24__ ){ __FUNC_24__ }
function getCardName_thirteenGX( __ARGV_25__ ){ __FUNC_25__ }
function createNewCard_thirteenGX( __ARGV_26__ ){ __FUNC_26__ }
function isCenterJoin_thirteenGX( __ARGV_27__ ){ __FUNC_27__ }
function getGameBgFile_thirteenGX( __ARGV_28__ ){ __FUNC_28__ }
function showCardType_thirteenGX( __ARGV_29__ ){ __FUNC_29__ }
function showCoins_thirteenGX( __ARGV_30__ ){ __FUNC_30__ }
function showScores_thirteenGX( __ARGV_31__ ){ __FUNC_31__ }
function hideScoreCallback_thirteenGX( __ARGV_32__ ){ __FUNC_32__ }
function showZhuangAni_thirteenGX( __ARGV_33__ ){ __FUNC_33__ }
function hideBankerCallback_thirteenGX( __ARGV_34__ ){ __FUNC_34__ }
function playEndLoseAni_thirteenGX( __ARGV_35__ ){ __FUNC_35__ }
function removeLoseAniCallback_thirteenGX( __ARGV_36__ ){ __FUNC_36__ }
function playEndPingAni_thirteenGX( __ARGV_37__ ){ __FUNC_37__ }
function removePingAniCallback_thirteenGX( __ARGV_38__ ){ __FUNC_38__ }
function playEndWinAni_thirteenGX( __ARGV_39__ ){ __FUNC_39__ }
function removeWinAniCallback_thirteenGX( __ARGV_40__ ){ __FUNC_40__ }
function playEndWinAni2_thirteenGX( __ARGV_41__ ){ __FUNC_41__ }
function removeWinAni2Callback_thirteenGX( __ARGV_42__ ){ __FUNC_42__ }
function playStartAni_thirteenGX( __ARGV_43__ ){ __FUNC_43__ }
function playCompareAni_thirteenGX( __ARGV_44__ ){ __FUNC_44__ }
function playQuanLeiDaAni_thirteenGX( __ARGV_46__ ){ __FUNC_46__ }
function removeQuanLeiDaAniCallback_thirteenGX( __ARGV_47__ ){ __FUNC_47__ }
function playGunAni_thirteenGX( __ARGV_48__ ){ __FUNC_48__ }
function playGunSound1_thirteenGX( __ARGV_49__ ){ __FUNC_49__ }
function playGunSound2_thirteenGX( __ARGV_50__ ){ __FUNC_50__ }
function playGunSound3_thirteenGX( __ARGV_51__ ){ __FUNC_51__ }
function removeGunAni_thirteenGX( __ARGV_52__ ){ __FUNC_52__ }
function removeDanKongAni_thirteenGX( __ARGV_53__ ){ __FUNC_53__ }
function playDanKongAni_thirteenGX( __ARGV_54__ ){ __FUNC_54__ }
var isInitVolume;
function playEffectInPlay_thirteenGX( __ARGV_55__ ){ __FUNC_55__ }
function reallyPlayEffect_thirteenGX( __ARGV_56__ ){ __FUNC_56__ }
function playMusic_thirteenGX( __ARGV_57__ ){ __FUNC_57__ }
function doLiPaiAni_thirteenGX( __ARGV_58__ ){ __FUNC_58__ }
function doPaiXing_thirteenGX( __ARGV_59__ ){ __FUNC_59__ }
function removeLiPaiAni_thirteenGX( __ARGV_60__ ){ __FUNC_60__ }
function exchangeTo16Data_thirteenGX( __ARGV_61__ ){ __FUNC_61__ }
function exchangeTo10Data_thirteenGX( __ARGV_62__ ){ __FUNC_62__ }
MjClient.SSS_GX_MaxPlayerNum=8;
MjClient.SSS_GX_MaPai=-1;
MjClient.selfMjhand=null;
timeTag = 0;
SSS_GX_MAX_HAND_COUNT = 13;
nDaoArr = {0:1,1:1,2:1,3:2,4:2,5:2,6:2,7:2,8:3,9:3,10:3,11:3,12:3};
nDunArr = {0:1,1:2,2:3,3:1,4:2,5:3,6:4,7:5,8:1,9:2,10:3,11:4,12:5};
isInitVolume = false;
---------------------------------------
==========================================================================E
==================================1==================================S
------------------Argv------------------
sData,tData,nSelfUid,player,i
---------------------------------------
----------------Content----------------
_local0=MjClient.data.sData;
_local1=_local0.tData;
_local2=SelfUid();
if((_local1.uids.indexOf(SelfUid()) == -1)){
_local3=_local0.players[SelfUid()];
_local2=_local3.otherReplayUid;
}
if((_local2 == -1)){
_local4=0;
while((_local4 < 20)){
print("");
_local4=(_local4 + 1);
_local4=0;
}
}
return _local2;
---------------------------------------
==========================================================================E
==================================2==================================S
------------------Argv------------------
sData,tData,nSelfUid
---------------------------------------
----------------Content----------------
_local0=MjClient.data.sData;
_local1=_local0.tData;
_local2=SelfUid();
if((_local1.uids.indexOf(SelfUid()) == -1)){
return true;
}
return false;
---------------------------------------
==========================================================================E
==================================3==================================S
------------------Argv------------------
index,selfIndex,off
---------------------------------------
----------------Content----------------
if((selfIndex == null)){
selfIndex = MjClient.data.sData.tData.uids.indexOf(SelfUidSSS());
}
_local0=(((index - selfIndex) + MjClient.SSS_GX_MaxPlayerNum) % MjClient.SSS_GX_MaxPlayerNum);
return getOffForPlayerNum(_local0);
---------------------------------------
==========================================================================E
==================================4==================================S
------------------Argv------------------
uid,sData,tData,uids,targetIndex
---------------------------------------
----------------Content----------------
_local0=MjClient.data.sData;
_local1=_local0.tData;
_local2=_local1.uids;
_local3=_local2.indexOf(uid);
return getOffByIndex_thirteenGX(_local3);
---------------------------------------
==========================================================================E
==================================5==================================S
------------------Argv------------------
off,sData,tData
---------------------------------------
----------------Content----------------
_local0=MjClient.data.sData;
_local1=_local0.tData;
return ((_local1.uids.indexOf(SelfUidSSS()) + off) % MjClient.SSS_GX_MaxPlayerNum);
---------------------------------------
==========================================================================E
==================================6==================================S
------------------Argv------------------
off,tbNodeArr
---------------------------------------
----------------Content----------------
_local0=[MjClient.playui._Node_player1,MjClient.playui._Node_player2,MjClient.playui._Node_player3,MjClient.playui._Node_player4,MjClient.playui._Node_player5,MjClient.playui._Node_player6,MjClient.playui._Node_player7,MjClient.playui._Node_player8,MjClient.playui._Node_player9,MjClient.playui._Node_player10];
return _local0[(off % _local0.length)];
---------------------------------------
==========================================================================E
==================================7==================================S
------------------Argv------------------
off,sData,tData,uids,index
---------------------------------------
----------------Content----------------
_local0=MjClient.data.sData;
if(!(_local0)){
return null;
}
_local1=_local0.tData;
_local2=_local1.uids;
_local3=getPlayerIndex_thirteenGX(off);
if((_local3 < _local2.length)){
return _local0.players[_local2[_local3]];
}
return null;
---------------------------------------
==========================================================================E
==================================8==================================S
------------------Argv------------------
off,pl
---------------------------------------
----------------Content----------------
_local0=getUIPlayer_thirteenGX(off);
if(!(_local0)){
return {};
}
return {uid:_local0.info.uid,url:_local0.info.headimgurl};
---------------------------------------
==========================================================================E
==================================9==================================S
------------------Argv------------------
node,d,off,nobody,headFrame,clippingNode,mask,img,WxHead
---------------------------------------
----------------Content----------------
if((d.uid == getUIHeadByOff_thirteenGX(off).uid)){
_local0=node.getChildByName("nobody");
_local1=node.getChildByName("headFrame");
_local1.visible=true;
_local2=new cc.ClippingNode();
_local3=new cc.Sprite("poker/13zhang/img_zz.png");
_local2.setAlphaThreshold(0);
_local2.setStencil(_local3);
_local4=new cc.Sprite(d.img);
_local4.setScale((_local3.getContentSize().width / _local4.getContentSize().width));
_local2.addChild(_local4);
_local2.setPosition((_local0.getContentSize().width / 2),(_local0.getContentSize().height / 2));
_local2.setName("WxHead");
_local5=_local0.getChildByName("WxHead");
if(_local5){
_local5.removeFromParent();
}
_local2.setScale((_local0.getContentSize().width / _local3.getContentSize().width));
_local0.addChild(_local2);
}
---------------------------------------
==========================================================================E
==================================10==================================S
------------------Argv------------------
msg,countDownNode,szTips,tipCountDown
---------------------------------------
----------------Content----------------
if((undefined <= 0)){
return undefined;
}
_aliased3483="";
undefined.unscheduleAllCallbacks();
undefined.getParent().visible=true;
undefined.visible=true;
_aliased4566=msg.tipCountDown;
undefined.setString((_aliased3483 + _aliased4566));
undefined.schedule(function () { __FUNC_11__ },1,cc.REPEAT_FOREVER,0);
---------------------------------------
==========================================================================E
==================================11==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
if((_aliased4566 > 0)){
_aliased4793=(_aliased4566 - 1);
}
if((_aliased4793 <= 0)){
undefined.getParent().visible=false;
undefined.setVisible(false);
undefined.unscheduleAllCallbacks();
} else {
}
undefined.setString((_aliased3483 + _aliased4793));
---------------------------------------
==========================================================================E
==================================12==================================S
------------------Argv------------------
node,off,pl,head,name,nobody,coin,offline,sData,tData,WxHead
---------------------------------------
----------------Content----------------
_local0=getUIPlayer_thirteenGX(off);
_local1=node.getChildByName("head");
_local2=_local1.getChildByName("name");
_local3=_local1.getChildByName("nobody");
_local4=_local1.getChildByName("coin");
_local5=_local1.getChildByName("offline");
if(_local0){
_local1.visible=true;
_local2.visible=true;
_local4.visible=true;
_local5.visible=false;
_local4.visible=true;
MjClient.loadWxHead(_local0.info.uid,_local0.info.headimgurl);
setUserOffline(node,off);
_local6=MjClient.data.sData;
_local7=_local6.tData;
InitUserHandUI_thirteenGX(node,off);
_local1.visible=false;
_local2.visible=false;
_local4.visible=false;
_local5.visible=false;
_local4.visible=false;
_local8=_local3.getChildByName("WxHead");
} else if(_local8){
_local8.removeFromParent(true);
}
---------------------------------------
==========================================================================E
==================================13==================================S
------------------Argv------------------
node,off,sData,tData,pl
---------------------------------------
----------------Content----------------
_local0=MjClient.data.sData;
_local1=_local0.tData;
_local2=getUIPlayer_thirteenGX(off);
if(!(_local2)){
return undefined;
}
InitUserCoinAndName_thirteenGX(node,off);
---------------------------------------
==========================================================================E
==================================14==================================S
------------------Argv------------------
node,off,pl,tData,bind,name
---------------------------------------
----------------Content----------------
_aliased4534=getUIPlayer_thirteenGX(off);
if(!(_aliased4534)){
return undefined;
}
_aliased7913=MjClient.data.sData.tData;
_local2={head:{name:{_text:function () { __FUNC_15__ }},coin:{_visible:function () { __FUNC_16__ },_run:function () { __FUNC_17__ }},atlas_score:{_visible:function () { __FUNC_18__ },_run:function () { __FUNC_19__ }}}};
_local3=node.getChildByName("head").getChildByName("name");
_local3.ignoreContentAdaptWithSize(true);
BindUiAndLogic(node,_local2);
---------------------------------------
==========================================================================E
==================================15==================================S
------------------Argv------------------
_nameStr
---------------------------------------
----------------Content----------------
_local0=unescape(_aliased4534.info.nickname);
return getNewName(_local0,5);
---------------------------------------
==========================================================================E
==================================16==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
return true;
---------------------------------------
==========================================================================E
==================================17==================================S
------------------Argv------------------
winall,coin
---------------------------------------
----------------Content----------------
if(getClubInfoInTable()){
_local0=_aliased4534.winall||0;
_aliased4534.info.powerScore?_local0=(_local0 + _aliased4534.info.powerScore):_local0=(_local0 + 0);
this.setString(revise(_local0));
return undefined;
}
_local1=_aliased7913.initCoin;
this.setString(revise((_local1 + _aliased4534.winall)));
---------------------------------------
==========================================================================E
==================================18==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
return true;
---------------------------------------
==========================================================================E
==================================19==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
changeAtalsForLabel(this,revise(_aliased4534.winall));
---------------------------------------
==========================================================================E
==================================20==================================S
------------------Argv------------------
node,off,sData,tData,pl,CardArr,i,nDaoIdx,nDunIdx
---------------------------------------
----------------Content----------------
_local0=MjClient.data.sData;
_local1=_local0.tData;
_local2=getUIPlayer_thirteenGX(off);
print("");
if(!(_local2)){
return undefined;
}
if(!(_local2.mjhand)){
return undefined;
}
print("");
if((_local1.tState == TableState.waitJoin)){
return undefined;
}
print("");
if(isCenterJoin_thirteenGX(off)){
return undefined;
}
print("");
if((_local2.mjhand.length < SSS_GX_MAX_HAND_COUNT)){
return undefined;
}
print("");
_local3=_local2.mjhand.concat();
print(("++++++++++ uid = " + _local2.info.uid));
print(("++++++++++ CardArr = " + JSON.stringify(_local3)));
sendAniInit_thirteenGX();
_local4=0;
while((_local4 < SSS_GX_MAX_HAND_COUNT)){
_local5=nDaoArr[_local4];
_local6=nDunArr[_local4];
createNewCard_thirteenGX(node,((("hcard" + _local5) + "") + _local6),_local3[_local4]);
_local4=(_local4 + 1);
_local4=0;
}
---------------------------------------
==========================================================================E
==================================21==================================S
------------------Argv------------------
score,str
---------------------------------------
----------------Content----------------
_local0="";
if((score == 0)){
_local0="0";
} else if((score > 0)){
_local0=("+" + score)||"";
} else {
}
_local0=score||"";
return _local0;
---------------------------------------
==========================================================================E
==================================22==================================S
------------------Argv------------------
node,off,sData,tData,pl,CardArr,i,nDaoIdx,nDunIdx
---------------------------------------
----------------Content----------------
return undefined;
_local0=MjClient.data.sData;
_local1=_local0.tData;
_local2=getUIPlayer_thirteenGX(off);
print("");
if(!(_local2)){
return undefined;
}
if(!(_local2.mjhand)){
return undefined;
}
print("");
if((_local1.tState == TableState.waitJoin)){
return undefined;
}
print("");
if(isCenterJoin_thirteenGX(off)){
return undefined;
}
print("");
if((_local2.mjhand.length < SSS_GX_MAX_HAND_COUNT)){
return undefined;
}
print("");
_local3=_local2.mjhand.concat();
print(("++++++++++ uid = " + _local2.info.uid));
print(("++++++++++ CardArr = " + JSON.stringify(_local3)));
sendAniInit_thirteenGX();
_local4=0;
while((_local4 < SSS_GX_MAX_HAND_COUNT)){
_local5=nDaoArr[_local4];
_local6=nDunArr[_local4];
createNewCard_thirteenGX(node,((("hcard" + _local5) + "") + _local6),_local3[_local4]);
_local4=(_local4 + 1);
_local4=0;
}
---------------------------------------
==========================================================================E
==================================23==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
timeTag = 0;
---------------------------------------
==========================================================================E
==================================24==================================S
------------------Argv------------------
node,nodeStand,i,nDaoIdx,nDunIdx,szCardName,card,strList,txt
---------------------------------------
----------------Content----------------
_local0=node.getChildByName("panel_Cards");
_local0.visible=true;
node.removeChildByName("LiPaiAni");
_local1=0;
while((_local1 < SSS_GX_MAX_HAND_COUNT)){
_local2=nDaoArr[_local1];
_local3=nDunArr[_local1];
_local4=((("hcard" + _local2) + "") + _local3);
_local5=_local0.getChildByName(_local4);
_local5.removeAllChildren();
_local5.setColor(cc.color(255,255,255));
_local5.visible=true;
_local5.loadTexture("poker/card/poker_back.png");
_local1=(_local1 + 1);
_local1=0;
}
_local6=["td","zd","wd","zf"];
_local1=0;
while((_local1 < _local6.length)){
_local7=_local0.getChildByName(("num_" + _local6[_local1]));
_local7.visible=false;
_local1=(_local1 + 1);
_local1=0;
}
_local0.getChildByName("img_td").visible=false;
_local0.getChildByName("img_zd").visible=false;
_local0.getChildByName("img_wd").visible=false;
---------------------------------------
==========================================================================E
==================================25==================================S
------------------Argv------------------
tag,cardType,flowerType,szName
---------------------------------------
----------------Content----------------
if((tag > 0)){
if((tag == 53)){
return "poker/card/515.png";
} else if((tag == 54)){
return "poker/card/615.png";
_local0=Math.ceil((tag / 4));
_local1=(tag % 4);
} else if((_local1 == 0)){
_local1=4;
}
(_local0 >= 10)?_local2=((("poker/card/" + _local1) + _local0) + ".png"):_local2=((("poker/card/" + _local1) + ("0" + _local0)) + ".png");
return _local2;
} else {
}
return "poker/card/poker_back.png";
---------------------------------------
==========================================================================E
==================================26==================================S
------------------Argv------------------
node,name,tag,card,cardType,flowerType,maIcon
---------------------------------------
----------------Content----------------
_local0=node.getChildByName(name);
_local0.removeAllChildren();
_local0.setColor(cc.color(255,255,255));
_local0.visible=true;
if((tag == null)){
assert("");
}
_local0.tag=tag;
if((tag > 0)){
if((tag == 53)){
_local0.loadTexture("poker/card/515.png");
} else if((tag == 54)){
_local0.loadTexture("poker/card/615.png");
_local1=Math.ceil((tag / 4));
_local2=(tag % 4);
} else if((_local2 == 0)){
_local2=4;
}
(_local1 >= 10)?_local0.loadTexture(((("poker/card/" + _local2) + _local1) + ".png")):_local0.loadTexture(((("poker/card/" + _local2) + ("0" + _local1)) + ".png"));
cc.log(((MjClient.SSS_GX_MaPai + "") + tag));
if((tag == MjClient.SSS_GX_MaPai)){
_local3=new cc.Sprite("poker/shisanzhang/ma_image.png");
_local0.addChild(_local3);
_local3.setPosition(cc.p(24.65,159.74));
_local0.setColor(cc.color(255,255,63));
} else {
}
}
_local0.loadTexture("poker/card/poker_back.png");
return cp;
---------------------------------------
==========================================================================E
==================================27==================================S
------------------Argv------------------
off,sData,pl,bCenterJoin,id
---------------------------------------
----------------Content----------------
_local0=MjClient.data.sData;
_local1=getUIPlayer_thirteenGX(off);
if(!(_local1)){
return false;
}
_local2=true;
while(_local0.tData.rungingUids has _iternext){
_local3=_iternext;
if((_local0.tData.rungingUids[_local3] == _local1.info.uid)){
_local2=false;
}
}
return _local2;
---------------------------------------
==========================================================================E
==================================28==================================S
------------------Argv------------------
type,path
---------------------------------------
----------------Content----------------
print(("type = " + type));
_local0="poker/table/beijing_6.jpg";
if((type == 0)){
_local0="poker/table/beijing_6.jpg";
} else if((type == 1)){
_local0="poker/table/beijing_7.jpg";
} else if((type == 2)){
_local0="poker/table/beijing_8.jpg";
} else if((type == 3)){
_local0="poker/table/beijing_9.jpg";
}
if(jsb.fileUtils.isFileExist(_local0)){
return _local0;
}
return "";
---------------------------------------
==========================================================================E
==================================29==================================S
------------------Argv------------------
off,pl,offNode,play_tips,CardArr,szCardType,ccText,oldScale,nCardType
---------------------------------------
----------------Content----------------
_local0=getUIPlayer_thirteenGX(off);
_local1=getNode_thirteenGX(off);
if((off == 0)){
_local2=_local1.getChildByName("play_tip_thirteenGX");
} else {
}
_local2=_local1.getChildByName("head").getChildByName("play_tip_thirteenGX");
if(!(_local0)){
_local2.visible=false;
return undefined;
}
_local2.visible=true;
_local3=_local0.mjhand.concat();
_local4=MjClient.majiang.getCardTypeStr(_local3);
_local5=_local2.getChildByName("Text");
_local5.ignoreContentAdaptWithSize(true);
_local5.setString(_local4);
_local6=_local2.getScale();
_local2.setScale(0);
_local2.runAction(cc.scaleTo(0.5,_local6).easing(cc.easeBackOut()));
_local7=MjClient.majiang.getCardType(_local3);
if((_local7 >= 0)&&(_local7 <= 9)){
playEffectInPlay_thirteenGX(("sg_px_" + _local7));
} else {
}
playEffectInPlay_thirteenGX("sg_px_sanGong");
---------------------------------------
==========================================================================E
==================================30==================================S
------------------Argv------------------
startOff,endOff,StarNode,EndNode,startPos,endPos,distance,costTime,midX,midY,move,i,goldIcon,action
---------------------------------------
----------------Content----------------
_local0=getNode_thirteenGX(startOff).getChildByName("head").getChildByName("AtlasLabel_Score");
_local1=getNode_thirteenGX(endOff).getChildByName("head").getChildByName("AtlasLabel_Score");
_local2=_local0.convertToWorldSpace(_local0.getAnchorPointInPoints());
_local3=_local1.convertToWorldSpace(_local1.getAnchorPointInPoints());
_local4=cc.pDistance(_local2,_local3);
_local5=(_local4 / 1000);
if((_local5 > 0.5)){
_local5=0.5;
} else if((_local5 < 0.3)){
_local5=0.3;
}
_local6=(((_local3.x - _local2.x) / 2) + _local2.x);
if((Math.abs((_local3.x - _local2.x)) < 10)){
_local6=(_local6 + (_local4 / 5));
}
_local7=Math.max(_local2.y,_local3.y);
if((Math.abs((_local3.y - _local2.y)) < 10)){
_local7=(_local7 + (_local4 / 5));
}
_local8=cc.bezierTo(_local5,[_local2,cc.p(_local6,_local7),_local3]);
_local9=0;
while((_local9 < 10)){
_local10=new cc.Sprite("poker/niuniu/img_gold.png");
_local11=cc.sequence(cc.delayTime(((_local9 * _local5) / 10)),_local8.clone(),cc.removeSelf());
_local10.runAction(_local11);
setWgtLayout(_local10,[0.05,0.05],[0,0],[0,0]);
_local10.setPosition(_local2);
MjClient.playui.addChild(_local10,100);
_local9=(_local9 + 1);
_local9=0;
}
playEffectInPlay("flyMoney");
---------------------------------------
==========================================================================E
==================================31==================================S
------------------Argv------------------
off,score,ccAtlasLabelScore
---------------------------------------
----------------Content----------------
_aliased7311=getNode_thirteenGX(off).getChildByName("head").getChildByName("AtlasLabel_Score");
_aliased7311.ignoreContentAdaptWithSize(true);
if((score < 0)){
_aliased7311.setProperty(("/" + Math.abs(score)),"poker/common/score_lose.png",31,41,".");
} else {
}
_aliased7311.setProperty(("/" + Math.abs(score)),"poker/common/score_win.png",31,41,".");
_aliased7311.setPosition(138.6,102.5);
_aliased7311.visible=true;
_aliased7311.runAction(cc.sequence(cc.moveBy(1,0,40),cc.delayTime(3),cc.callFunc(function () { __FUNC_32__ })));
---------------------------------------
==========================================================================E
==================================32==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
_aliased7311.visible=false;
---------------------------------------
==========================================================================E
==================================33==================================S
------------------Argv------------------
off,tragetNode,tragetPos,ccImageBanker
---------------------------------------
----------------Content----------------
_local0=getNode_thirteenGX(off).getChildByName("head").getChildByName("AtlasLabel_Score");
_local1=_local0.convertToWorldSpace(_local0.getAnchorPointInPoints());
_aliased3297=MjClient.playui._Image_banker;
setWgtLayout(_aliased3297,[0.055,0.09375],[0.5,0.5],[0,0]);
_aliased3297.visible=true;
_aliased3297.runAction(cc.sequence(cc.delayTime(0.25),cc.moveTo(0.25,_local1),cc.delayTime(0.25),cc.callFunc(function () { __FUNC_34__ })));
---------------------------------------
==========================================================================E
==================================34==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
_aliased3297.visible=false;
---------------------------------------
==========================================================================E
==================================35==================================S
------------------Argv------------------
_node,szPng,szPlist,szJson,szAni,_armature
---------------------------------------
----------------Content----------------
_local0="poker/ani/woshul/woshulmaa0.png";
_local1="poker/ani/woshul/woshulmaa0.plist";
_local2="poker/ani/woshul/woshulmaa.ExportJson";
_local3="woshulmaa";
ccs.armatureDataManager.addArmatureFileInfo(_local0,_local1,_local2);
_aliased4004=new ccs.Armature(_local3);
_aliased4004.setPosition((cc.winSize.width / 2),((cc.winSize.height / 2) + 10));
_aliased4004.setScale((MjClient.size.width / 1280));
_aliased4004.getAnimation().playWithIndex(0,-1,0);
_node.addChild(_aliased4004,9999);
_aliased4004.runAction(cc.sequence(cc.delayTime(1),cc.callFunc(function () { __FUNC_36__ })));
playEffectInPlay_thirteenGX("lose");
---------------------------------------
==========================================================================E
==================================36==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
_aliased4004.removeFromParent();
---------------------------------------
==========================================================================E
==================================37==================================S
------------------Argv------------------
_node,szPng,szPlist,szJson,szAni,_armature
---------------------------------------
----------------Content----------------
_local0="poker/ani/woshul/woshulmaa0.png";
_local1="poker/ani/woshul/woshulmaa0.plist";
_local2="poker/ani/woshul/woshulmaa.ExportJson";
_local3="woshulmaa";
ccs.armatureDataManager.addArmatureFileInfo(_local0,_local1,_local2);
_aliased1442=new ccs.Armature(_local3);
_aliased1442.setPosition((cc.winSize.width / 2),((cc.winSize.height / 2) + 10));
_aliased1442.setScale((MjClient.size.width / 1280));
_aliased1442.getAnimation().playWithIndex(1,-1,0);
_node.addChild(_aliased1442,9999);
_aliased1442.runAction(cc.sequence(cc.delayTime(1),cc.callFunc(function () { __FUNC_38__ })));
playEffectInPlay_thirteenGX("win");
---------------------------------------
==========================================================================E
==================================38==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
_aliased1442.removeFromParent();
---------------------------------------
==========================================================================E
==================================39==================================S
------------------Argv------------------
_node,szPng,szPlist,szJson,szAni,_armature
---------------------------------------
----------------Content----------------
_local0="poker/ani/woyingl/woyingl0.png";
_local1="poker/ani/woyingl/woyingl0.plist";
_local2="poker/ani/woyingl/woyingl.ExportJson";
_local3="woyingl";
ccs.armatureDataManager.addArmatureFileInfo(_local0,_local1,_local2);
_aliased1001=new ccs.Armature(_local3);
_aliased1001.setPosition((cc.winSize.width / 2),((cc.winSize.height / 2) + 10));
_aliased1001.setScale((MjClient.size.width / 1280));
_aliased1001.getAnimation().playWithIndex(0,-1,0);
_node.addChild(_aliased1001,9999);
_aliased1001.runAction(cc.sequence(cc.delayTime(1),cc.callFunc(function () { __FUNC_40__ })));
playEffectInPlay_thirteenGX("win");
---------------------------------------
==========================================================================E
==================================40==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
_aliased1001.removeFromParent();
---------------------------------------
==========================================================================E
==================================41==================================S
------------------Argv------------------
_node,szPng,szPlist,szJson,szAni,_armature
---------------------------------------
----------------Content----------------
_local0="poker/ani/woyingl/woyingl20.png";
_local1="poker/ani/woyingl/woyingl20.plist";
_local2="poker/ani/woyingl/woyingl2.ExportJson";
_local3="woyingl2";
ccs.armatureDataManager.addArmatureFileInfo(_local0,_local1,_local2);
_aliased4044=new ccs.Armature(_local3);
_aliased4044.setPosition((cc.winSize.width / 2),((cc.winSize.height / 2) + 10));
_aliased4044.setScale((MjClient.size.width / 1280));
_aliased4044.getAnimation().playWithIndex(0,-1,0);
_node.addChild(_aliased4044,9999);
_aliased4044.runAction(cc.sequence(cc.delayTime(1),cc.callFunc(function () { __FUNC_42__ })));
playEffectInPlay_thirteenGX("win");
---------------------------------------
==========================================================================E
==================================42==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
_aliased4044.removeFromParent();
---------------------------------------
==========================================================================E
==================================43==================================S
------------------Argv------------------
_node,szAniName,_animNode
---------------------------------------
----------------Content----------------
_node.removeAllChildren();
_local0="poker/13zhang/ani/sss_kaishiyouxi";
_local1=createSpine((_local0 + ".json"),(_local0 + ".atlas"));
_local1.setAnimation(0,"animation",false);
_node.addChild(_local1);
playEffectInPlay_thirteenGX("start");
---------------------------------------
==========================================================================E
==================================44==================================S
------------------Argv------------------
_node,szAniName,_animNode
---------------------------------------
----------------Content----------------
_aliased4044.removeAllChildren();
_local0="poker/13zhang/ani/kashibipai";
_local1=createSpine((_local0 + ".json"),(_local0 + ".atlas"));
_local1.setAnimation(0,"ksbp",false);
_aliased4044.addChild(_local1);
_aliased4044.runAction(cc.sequence(cc.delayTime(1.6),cc.callFunc(function () { __FUNC_45__ })));
playEffectInPlay_thirteenGX("start_compare");
---------------------------------------
==========================================================================E
==================================45==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
_aliased4044.removeAllChildren();
---------------------------------------
==========================================================================E
==================================46==================================S
------------------Argv------------------
_node,szAniName,_animNode
---------------------------------------
----------------Content----------------
_aliased4044.removeAllChildren();
_local0="poker/13zhang/ani/quanleida";
_local1=createSpine((_local0 + ".json"),(_local0 + ".atlas"));
_local1.setAnimation(0,"animation",false);
_aliased4044.addChild(_local1);
_aliased4044.runAction(cc.sequence(cc.delayTime(3),cc.callFunc(function () { __FUNC_47__ })));
playEffectInPlay_thirteenGX("special1");
---------------------------------------
==========================================================================E
==================================47==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
_aliased4044.removeAllChildren();
---------------------------------------
==========================================================================E
==================================48==================================S
------------------Argv------------------
nInitUserId,nDesUserId,callback,nInitOff,ccInitNode,initPanel,nDesOff,ccDesNode,ccDesPanel,startPos,endPos,dx,dy,angleRadians,angleDegrees,szAniName,_animNode,szDesAniName,_desAnimNode
---------------------------------------
----------------Content----------------
_local0=getUiOffByUid_thirteenGX(nInitUserId);
_local1=getNode_thirteenGX(_local0);
_aliased7837=_local1.getChildByName("panel_Cards");
_aliased7837.removeChildByName("GUN");
_local3=getUiOffByUid_thirteenGX(nDesUserId);
_local4=getNode_thirteenGX(_local3);
_aliased2094=_local4.getChildByName("panel_Cards");
_local6=_aliased7837.convertToWorldSpace(_aliased7837.getAnchorPointInPoints());
_local7=_aliased2094.convertToWorldSpace(_aliased2094.getAnchorPointInPoints());
_local8=(_local7.x - _local6.x);
_local9=(_local7.y - _local6.y);
_local10=Math.atan2(_local9,_local8);
_local11=(_local10 * (180 / Math.PI));
_local12="poker/13zhang/ani/gun";
_local13=createSpine((_local12 + ".json"),(_local12 + ".atlas"));
_local13.setName("GUN");
_local13.setAnimation(0,"fire",true);
_local13.setRotation((90 - _local11));
_aliased7837.addChild(_local13);
_local13.setPosition((_aliased7837.getContentSize().width / 2),(_aliased7837.getContentSize().height / 2));
_local14="poker/13zhang/ani/dankong";
_local15=createSpine((_local14 + ".json"),(_local14 + ".atlas"));
_local15.setName("DAN_KONG");
_local15.setAnimation(0,"dankong",true);
_aliased2094.addChild(_local15);
_local15.setPosition((_aliased2094.getContentSize().width / 2),(_aliased2094.getContentSize().height / 2));
_aliased7837.runAction(cc.sequence(cc.callFunc(function () { __FUNC_49__ }),cc.delayTime(0.5),cc.callFunc(function () { __FUNC_50__ }),cc.delayTime(0.5),cc.callFunc(function () { __FUNC_51__ }),cc.delayTime(0.5),cc.callFunc(function () { __FUNC_52__ }),cc.delayTime(0.5),cc.callFunc(function () { __FUNC_53__ })));
---------------------------------------
==========================================================================E
==================================49==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
playEffectInPlay_thirteenGX("daqiang3");
---------------------------------------
==========================================================================E
==================================50==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
playEffectInPlay_thirteenGX("daqiang3");
---------------------------------------
==========================================================================E
==================================51==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
playEffectInPlay_thirteenGX("daqiang3");
---------------------------------------
==========================================================================E
==================================52==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
_aliased7837.removeChildByName("GUN");
---------------------------------------
==========================================================================E
==================================53==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
_aliased2094.removeChildByName("DAN_KONG");
if(_aliased4044){
_aliased4044();
}
---------------------------------------
==========================================================================E
==================================54==================================S
------------------Argv------------------
nDesUserId,nDesOff,ccDesNode,ccDesPanel,szAniName,_animNode
---------------------------------------
----------------Content----------------
_local0=getUiOffByUid_thirteenGX(nDesUserId);
_local1=getNode_thirteenGX(_local0);
_local2=_local1.getChildByName("panel_Cards");
_local3="poker/13zhang/ani/dankong";
_local4=createSpine((_local3 + ".json"),(_local3 + ".atlas"));
_local4.setName("DAN_KONG");
_local4.setAnimation(0,"dankong",true);
_local2.addChild(_local4);
_local4.setPosition((_local2.getContentSize().width / 2),(_local2.getContentSize().height / 2));
---------------------------------------
==========================================================================E
==================================55==================================S
------------------Argv------------------
szSoundName,nSoundType,isLoop,szSoundFile
---------------------------------------
----------------Content----------------
_local0=(("sound/13zhang/" + szSoundName) + ".mp3");
if((nSoundType == 1)){
_local0=(("sound/13zhang/" + szSoundName) + ".wav");
}
if(jsb.fileUtils.isFileExist(_local0)){
reallyPlayEffect_thirteenGX(_local0,isLoop);
}
---------------------------------------
==========================================================================E
==================================56==================================S
------------------Argv------------------
str,isLoop,vol,ret
---------------------------------------
----------------Content----------------
if(!(isInitVolume)){
isInitVolume = true;
cc.audioEngine.setEffectsVolume(1);
}
_local0=util.localStorageEncrypt.getNumberItem("EffectVolume",0.5);
if(MjClient.atRecord&&(isLoop !== true)){
return 0;
}
if(MjClient.isPlayRecord&&(isLoop !== true)){
return 0;
}
_local1=cc.audioEngine.playEffect(str,(isLoop === true),1,0,(_local0 + 0.0001));
if(_local1 && MjClient.atRecord || MjClient.isPlayRecord){
cc.audioEngine.pauseEffect(_local1);
}
return _local1;
---------------------------------------
==========================================================================E
==================================57==================================S
------------------Argv------------------
sd,str
---------------------------------------
----------------Content----------------
cc.audioEngine.stopMusic();
_local0=(("sound/13zhang/" + sd) + ".mp3");
cc.audioEngine.playMusic(_local0,true);
---------------------------------------
==========================================================================E
==================================58==================================S
------------------Argv------------------
node,posAni,szAniName,_animNode
---------------------------------------
----------------Content----------------
node.removeChildByName("LiPaiAni");
_local0=node.getChildByName("panel_Cards").getPosition();
_local1="poker/13zhang/ani/lipai";
_local2=createSpine((_local1 + ".json"),(_local1 + ".atlas"));
_local2.setName("LiPaiAni");
_local2.setPosition(_local0);
_local2.setAnimation(0,"pai1",true);
node.addChild(_local2);
---------------------------------------
==========================================================================E
==================================59==================================S
------------------Argv------------------
node,szOneAniName,szAniName,_animNode
---------------------------------------
----------------Content----------------
node.removeChildByName("PaiXingAni");
szOneAniName = "1wulong";
_local0=szOneAniName||"poker/13zhang/ani/13shui_paixing";
_local1=createSpine((_local0 + ".json"),(_local0 + ".atlas"));
_local1.setName("PaiXingAni");
_local1.setPosition((node.getContentSize().width / 2),(node.getContentSize().height / 2));
_local1.setAnimation(0,szOneAniName,false);
node.addChild(_local1);
---------------------------------------
==========================================================================E
==================================60==================================S
------------------Argv------------------
node
---------------------------------------
----------------Content----------------
node.removeChildByName("LiPaiAni");
---------------------------------------
==========================================================================E
==================================61==================================S
------------------Argv------------------
mjhand,nSmallGuiIdx,nBigGuiIdx,smallGuiArr,bigGuiArr,tbData,newMjHand,i,newData
---------------------------------------
----------------Content----------------
if((mjhand.length == 0)){
return undefined;
}
_local0=0;
_local1=0;
_local2=[65,66,67,68];
_local3=[81,82,83,84];
_local4={1:14,2:30,3:46,4:62,5:2,6:18,7:34,8:50,9:3,10:19,11:35,12:51,13:4,14:20,15:36,16:52,17:5,18:21,19:37,20:53,21:6,22:22,23:38,24:54,25:7,26:23,27:39,28:55,29:8,30:24,31:40,32:56,33:9,34:25,35:41,36:57,37:10,38:26,39:42,40:58,41:11,42:27,43:43,44:59,45:12,46:28,47:44,48:60,49:13,50:29,51:45,52:61};
_local5=[];
_local6=0;
while((_local6 < mjhand.length)){
if((mjhand[_local6] == 53)){
_local5.push(_local2[_local0]);
_local0=(_local0 + 1);
_local0=0;
} else if((mjhand[_local6] == 54)){
_local5.push(_local3[_local1]);
_local1=(_local1 + 1);
_local1=0;
} else {
}
_local7=_local4[mjhand[_local6]];
_local5.push(_local7);
_local6=(_local6 + 1);
_local6=0;
}
return _local5;
---------------------------------------
==========================================================================E
==================================62==================================S
------------------Argv------------------
mjhand,smallGuiArr,bigGuiArr,tbData,newMjHand,i,newData
---------------------------------------
----------------Content----------------
_local0=[65,66,67,68];
_local1=[81,82,83,84];
_local2={14:1,30:2,46:3,62:4,2:5,18:6,34:7,50:8,3:9,19:10,35:11,51:12,4:13,20:14,36:15,52:16,5:17,21:18,37:19,53:20,6:21,22:22,38:23,54:24,7:25,23:26,39:27,55:28,8:29,24:30,40:31,56:32,9:33,25:34,41:35,57:36,10:37,26:38,42:39,58:40,11:41,27:42,43:43,59:44,12:45,28:46,44:47,60:48,13:49,29:50,45:51,61:52};
_local3=[];
_local4=0;
while((_local4 < mjhand.length)){
if((_local0.indexOf(mjhand[_local4]) != -1)){
_local3.push(53);
} else if((_local1.indexOf(mjhand[_local4]) != -1)){
_local3.push(54);
} else {
}
_local5=_local2[mjhand[_local4]];
_local3.push(_local5);
_local4=(_local4 + 1);
_local4=0;
}
return _local3;
---------------------------------------
==========================================================================E
