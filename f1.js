==================================0==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
var actionZindex;
var PlayLayer_thirteenGX;
actionZindex = 1000;
PlayLayer_thirteenGX = cc.Layer.extend({jsBind:{_event:{startJiazhu:function () { __FUNC_1__ },MJShuffle:function () { __FUNC_2__ },LeaveGame:function () { __FUNC_3__ },endRoom:function () { __FUNC_4__ },roundEnd:function () { __FUNC_6__ },clearClientUI:function () { __FUNC_8__ },logout:function () { __FUNC_9__ },DelRoom:function () { __FUNC_10__ },PlayerIsAuth:function () { __FUNC_11__ },initSceneData:function () { __FUNC_12__ },NNSendFourCard:function () { __FUNC_14__ },doWaitBegin:function () { __FUNC_16__ },doCompareCard:function () { __FUNC_17__ }},back:{back:{_run:function () { __FUNC_20__ },_event:{changeGameBgEvent:function () { __FUNC_21__ }},_layout:[[1,1],[0.5,0.5],[0,0],true]}},Image_fapai:{_layout:[[0.065,0.065],[0.5,0.5],[0,0],true],_run:function () { __FUNC_22__ }},Image_banker:{_layout:[[0.055,0.09375],[0.5,0.5],[0,0],true],_run:function () { __FUNC_23__ }},gameName:{_layout:[[0.2336,0.1014],[0.5,0.65],[0,0]],_run:function () { __FUNC_24__ }},Image_point:{_layout:[[0.********,0.****************],[0.5,0.5],[0,0]]},tableid:{_layout:[[0.15,0.15],[0.01,0.961],[0,0]],_run:function () { __FUNC_25__ },_event:{initSceneData:function () { __FUNC_26__ }}},jvshu:{_layout:[[0.1,0.1],[0.01,0.9],[0,0]],_run:function () { __FUNC_27__ },_event:{initSceneData:function () { __FUNC_28__ },startGame:function () { __FUNC_29__ }}},btn_menu:{_layout:[[0.0547,0.1028],[0.9691,0.9401],[0,0]],_run:function () { __FUNC_30__ },_click:function () { __FUNC_31__ }},layout_menu:{_layout:[[1,0],[0.5,0],[0,0],true],_run:function () { __FUNC_32__ },_touch:function () { __FUNC_33__ }},node_menu:{_layout:[[0.1625,0.5597],[1,1],[0,0]],_run:function () { __FUNC_34__ },bt_set:{_click:function () { __FUNC_35__ }},bt_rule:{_click:function () { __FUNC_36__ }},bt_backhall:{_click:function () { __FUNC_37__ }},bt_restart:{_click:function () { __FUNC_40__ }},bt_exit:{_click:function () { __FUNC_43__ }}},btn_gameRule:{_layout:[[0.096,0.096],[0.9,0.94],[0,0]],_run:function () { __FUNC_44__ },_click:function () { __FUNC_45__ },img_ruleBg:{_visible:true,_run:function () { __FUNC_48__ },scrollView_ruleList:{_visible:true,_run:function () { __FUNC_50__ }}}},optime_bg:{_run:function () { __FUNC_51__ },_event:{trustTip:function () { __FUNC_52__ }}},BtnReady:{_run:function () { __FUNC_53__ },_click:function () { __FUNC_54__ },_event:{clearCardUI:function () { __FUNC_55__ },startGame:function () { __FUNC_56__ },waitReady:function () { __FUNC_57__ },roundEnd:function () { __FUNC_58__ },initSceneData:function () { __FUNC_59__ }}},BtnXiPai:{_run:function () { __FUNC_60__ },_click:function () { __FUNC_61__ },_event:{clearCardUI:function () { __FUNC_63__ },startGame:function () { __FUNC_64__ },waitReady:function () { __FUNC_65__ },roundEnd:function () { __FUNC_66__ },initSceneData:function () { __FUNC_67__ }}},BtnChaPai:{_run:function () { __FUNC_68__ },_click:function () { __FUNC_69__ },_event:{clearCardUI:function () { __FUNC_70__ },startGame:function () { __FUNC_71__ },waitReady:function () { __FUNC_72__ },roundEnd:function () { __FUNC_73__ },notifyUserReset:function () { __FUNC_74__ },doCompareCard:function () { __FUNC_75__ },initSceneData:function () { __FUNC_76__ }}},BtnReset:{_run:function () { __FUNC_77__ },_click:function () { __FUNC_78__ },_event:{clearCardUI:function () { __FUNC_81__ },startGame:function () { __FUNC_82__ },waitReady:function () { __FUNC_83__ },roundEnd:function () { __FUNC_84__ },notifyUserReset:function () { __FUNC_85__ },doCompareCard:function () { __FUNC_86__ },initSceneData:function () { __FUNC_87__ }}},sp_clock:{_run:function () { __FUNC_88__ },_event:{clearCardUI:function () { __FUNC_89__ },startGame:function () { __FUNC_90__ },waitReady:function () { __FUNC_91__ },roundEnd:function () { __FUNC_92__ },doCompareCard:function () { __FUNC_93__ },initSceneData:function () { __FUNC_94__ },NNSendFourCard:function () { __FUNC_95__ }}},wait:{delroom:{_run:function () { __FUNC_96__ },_click:function () { __FUNC_97__ },_event:{waitReady:function () { __FUNC_98__ },waitReadyNew:function () { __FUNC_99__ }}},backHomebtn:{_run:function () { __FUNC_100__ },_click:function () { __FUNC_101__ },_event:{waitReady:function () { __FUNC_106__ }}},_event:{initSceneData:function () { __FUNC_107__ },addPlayer:function () { __FUNC_108__ },removePlayer:function () { __FUNC_109__ },NNSendFourCard:function () { __FUNC_110__ },waitJiazhu:function () { __FUNC_111__ },startCallBank:function () { __FUNC_112__ },roundEnd:function () { __FUNC_113__ }}},Node_player1:{_layout:[[0.2031,0.3153],[0.5,0.161],[0,0]],head:{AtlasLabel_Score:{_run:function () { __FUNC_114__ }},tuoguan:{_run:function () { __FUNC_115__ },_event:{beTrust:function () { __FUNC_116__ },cancelTrust:function () { __FUNC_117__ },initSceneData:function () { __FUNC_118__ }}},chatbg:{_run:function () { __FUNC_119__ },chattext:{_event:{MJChat:function () { __FUNC_120__ },playVoice:function () { __FUNC_121__ }}}},_click:function () { __FUNC_122__ },_run:function () { __FUNC_123__ },_event:{loadWxHead:function () { __FUNC_124__ },addPlayer:function () { __FUNC_125__ },removePlayer:function () { __FUNC_126__ }}},panel_Cards:{img_td:{_visible:false},img_zd:{_visible:false},img_wd:{_visible:false},num_td:{_visible:false},num_zd:{_visible:false},num_wd:{_visible:false},num_zf:{_visible:false},_run:function () { __FUNC_127__ },_event:{NNSendFourCard:function () { __FUNC_128__ },initSceneData:function () { __FUNC_129__ },doCompareCard:function () { __FUNC_130__ }}},ready:{_run:function () { __FUNC_131__ },_event:{moveHead:function () { __FUNC_132__ },addPlayer:function () { __FUNC_133__ },removePlayer:function () { __FUNC_134__ },onlinePlayer:function () { __FUNC_135__ },NNSendFourCard:function () { __FUNC_136__ },initSceneData:function () { __FUNC_137__ }}},_event:{initSceneData:function () { __FUNC_138__ },addPlayer:function () { __FUNC_139__ },removePlayer:function () { __FUNC_140__ },roundEnd:function () { __FUNC_141__ },onlinePlayer:function () { __FUNC_142__ }}},Node_player2:{_layout:[[0.2031,0.3153],[0.9156,0.15],[0,0]],head:{AtlasLabel_Score:{_run:function () { __FUNC_143__ }},ready:{_run:function () { __FUNC_144__ },_event:{addPlayer:function () { __FUNC_145__ },removePlayer:function () { __FUNC_146__ },NNSendFourCard:function () { __FUNC_147__ },onlinePlayer:function () { __FUNC_148__ }}},tuoguan:{_run:function () { __FUNC_149__ },_event:{beTrust:function () { __FUNC_150__ },cancelTrust:function () { __FUNC_151__ },initSceneData:function () { __FUNC_152__ }}},chatbg:{_run:function () { __FUNC_153__ },chattext:{_event:{MJChat:function () { __FUNC_154__ },playVoice:function () { __FUNC_155__ }}}},_click:function () { __FUNC_156__ },_event:{loadWxHead:function () { __FUNC_157__ },addPlayer:function () { __FUNC_158__ },removePlayer:function () { __FUNC_159__ }},_run:function () { __FUNC_160__ }},panel_Cards:{img_td:{_visible:false},img_zd:{_visible:false},img_wd:{_visible:false},num_td:{_visible:false},num_zd:{_visible:false},num_wd:{_visible:false},num_zf:{_visible:false},_run:function () { __FUNC_161__ },_event:{NNSendFourCard:function () { __FUNC_162__ },initSceneData:function () { __FUNC_163__ },doCompareCard:function () { __FUNC_164__ }}},_event:{clearCardUI:function () { __FUNC_165__ },initSceneData:function () { __FUNC_166__ },addPlayer:function () { __FUNC_167__ },removePlayer:function () { __FUNC_168__ },roundEnd:function () { __FUNC_169__ },onlinePlayer:function () { __FUNC_170__ }}},Node_player3:{_layout:[[0.2031,0.3153],[0.9,0.475],[0,0]],head:{AtlasLabel_Score:{_run:function () { __FUNC_171__ }},ready:{_run:function () { __FUNC_172__ },_event:{addPlayer:function () { __FUNC_173__ },removePlayer:function () { __FUNC_174__ },NNSendFourCard:function () { __FUNC_175__ },onlinePlayer:function () { __FUNC_176__ }}},tuoguan:{_run:function () { __FUNC_177__ },_event:{beTrust:function () { __FUNC_178__ },cancelTrust:function () { __FUNC_179__ },initSceneData:function () { __FUNC_180__ }}},chatbg:{_run:function () { __FUNC_181__ },chattext:{_event:{MJChat:function () { __FUNC_182__ },playVoice:function () { __FUNC_183__ }}}},_click:function () { __FUNC_184__ },_event:{loadWxHead:function () { __FUNC_185__ },addPlayer:function () { __FUNC_186__ },removePlayer:function () { __FUNC_187__ }},_run:function () { __FUNC_188__ }},panel_Cards:{img_td:{_visible:false},img_zd:{_visible:false},img_wd:{_visible:false},num_td:{_visible:false},num_zd:{_visible:false},num_wd:{_visible:false},num_zf:{_visible:false},_run:function () { __FUNC_189__ },_event:{NNSendFourCard:function () { __FUNC_190__ },initSceneData:function () { __FUNC_191__ },doCompareCard:function () { __FUNC_192__ }}},_event:{initSceneData:function () { __FUNC_193__ },addPlayer:function () { __FUNC_194__ },removePlayer:function () { __FUNC_195__ },roundEnd:function () { __FUNC_196__ },onlinePlayer:function () { __FUNC_197__ }}},Node_player4:{_layout:[[0.2031,0.3153],[0.85,0.8],[0,0]],head:{AtlasLabel_Score:{_run:function () { __FUNC_198__ }},ready:{_run:function () { __FUNC_199__ },_event:{addPlayer:function () { __FUNC_200__ },removePlayer:function () { __FUNC_201__ },NNSendFourCard:function () { __FUNC_202__ },onlinePlayer:function () { __FUNC_203__ }}},tuoguan:{_run:function () { __FUNC_204__ },_event:{beTrust:function () { __FUNC_205__ },cancelTrust:function () { __FUNC_206__ },initSceneData:function () { __FUNC_207__ }}},chatbg:{_run:function () { __FUNC_208__ },chattext:{_event:{MJChat:function () { __FUNC_209__ },playVoice:function () { __FUNC_210__ }}}},_click:function () { __FUNC_211__ },_event:{loadWxHead:function () { __FUNC_212__ },addPlayer:function () { __FUNC_213__ },removePlayer:function () { __FUNC_214__ }},_run:function () { __FUNC_215__ }},panel_Cards:{img_td:{_visible:false},img_zd:{_visible:false},img_wd:{_visible:false},num_td:{_visible:false},num_zd:{_visible:false},num_wd:{_visible:false},num_zf:{_visible:false},_run:function () { __FUNC_216__ },_event:{NNSendFourCard:function () { __FUNC_217__ },initSceneData:function () { __FUNC_218__ },doCompareCard:function () { __FUNC_219__ }}},_event:{clearCardUI:function () { __FUNC_220__ },initSceneData:function () { __FUNC_221__ },addPlayer:function () { __FUNC_222__ },removePlayer:function () { __FUNC_223__ },roundEnd:function () { __FUNC_224__ },onlinePlayer:function () { __FUNC_225__ }}},Node_player5:{_layout:[[0.2031,0.3153],[0.44,0.85],[0,0]],head:{AtlasLabel_Score:{_run:function () { __FUNC_226__ }},ready:{_run:function () { __FUNC_227__ },_event:{addPlayer:function () { __FUNC_228__ },removePlayer:function () { __FUNC_229__ },NNSendFourCard:function () { __FUNC_230__ },onlinePlayer:function () { __FUNC_231__ }}},tuoguan:{_run:function () { __FUNC_232__ },_event:{beTrust:function () { __FUNC_233__ },cancelTrust:function () { __FUNC_234__ },initSceneData:function () { __FUNC_235__ }}},chatbg:{_run:function () { __FUNC_236__ },chattext:{_event:{MJChat:function () { __FUNC_237__ },playVoice:function () { __FUNC_238__ }}}},_click:function () { __FUNC_239__ },_event:{loadWxHead:function () { __FUNC_240__ },addPlayer:function () { __FUNC_241__ },removePlayer:function () { __FUNC_242__ }},_run:function () { __FUNC_243__ }},panel_Cards:{img_td:{_visible:false},img_zd:{_visible:false},img_wd:{_visible:false},num_td:{_visible:false},num_zd:{_visible:false},num_wd:{_visible:false},num_zf:{_visible:false},_run:function () { __FUNC_244__ },_event:{NNSendFourCard:function () { __FUNC_245__ },initSceneData:function () { __FUNC_246__ },doCompareCard:function () { __FUNC_247__ }}},_event:{clearCardUI:function () { __FUNC_248__ },initSceneData:function () { __FUNC_249__ },addPlayer:function () { __FUNC_250__ },removePlayer:function () { __FUNC_251__ },roundEnd:function () { __FUNC_252__ },onlinePlayer:function () { __FUNC_253__ }}},Node_player6:{_layout:[[0.2031,0.3153],[0.15,0.8],[0,0]],head:{AtlasLabel_Score:{_run:function () { __FUNC_254__ }},ready:{_run:function () { __FUNC_255__ },_event:{addPlayer:function () { __FUNC_256__ },removePlayer:function () { __FUNC_257__ },NNSendFourCard:function () { __FUNC_258__ },onlinePlayer:function () { __FUNC_259__ }}},tuoguan:{_run:function () { __FUNC_260__ },_event:{beTrust:function () { __FUNC_261__ },cancelTrust:function () { __FUNC_262__ },initSceneData:function () { __FUNC_263__ }}},chatbg:{_run:function () { __FUNC_264__ },chattext:{_event:{MJChat:function () { __FUNC_265__ },playVoice:function () { __FUNC_266__ }}}},_click:function () { __FUNC_267__ },_run:function () { __FUNC_268__ },_event:{loadWxHead:function () { __FUNC_269__ },addPlayer:function () { __FUNC_270__ },removePlayer:function () { __FUNC_271__ }}},panel_Cards:{img_td:{_visible:false},img_zd:{_visible:false},img_wd:{_visible:false},num_td:{_visible:false},num_zd:{_visible:false},num_wd:{_visible:false},num_zf:{_visible:false},_run:function () { __FUNC_272__ },_event:{NNSendFourCard:function () { __FUNC_273__ },initSceneData:function () { __FUNC_274__ },doCompareCard:function () { __FUNC_275__ }}},_event:{initSceneData:function () { __FUNC_276__ },addPlayer:function () { __FUNC_277__ },removePlayer:function () { __FUNC_278__ },roundEnd:function () { __FUNC_279__ },onlinePlayer:function () { __FUNC_280__ }}},Node_player7:{_layout:[[0.2031,0.3153],[0.1,0.475],[0,0]],head:{AtlasLabel_Score:{_run:function () { __FUNC_281__ }},ready:{_run:function () { __FUNC_282__ },_event:{addPlayer:function () { __FUNC_283__ },removePlayer:function () { __FUNC_284__ },NNSendFourCard:function () { __FUNC_285__ },onlinePlayer:function () { __FUNC_286__ }}},tuoguan:{_run:function () { __FUNC_287__ },_event:{beTrust:function () { __FUNC_288__ },cancelTrust:function () { __FUNC_289__ },initSceneData:function () { __FUNC_290__ }}},chatbg:{_run:function () { __FUNC_291__ },chattext:{_event:{MJChat:function () { __FUNC_292__ },playVoice:function () { __FUNC_293__ }}}},_click:function () { __FUNC_294__ },_event:{loadWxHead:function () { __FUNC_295__ },addPlayer:function () { __FUNC_296__ },removePlayer:function () { __FUNC_297__ }},_run:function () { __FUNC_298__ }},panel_Cards:{img_td:{_visible:false},img_zd:{_visible:false},img_wd:{_visible:false},num_td:{_visible:false},num_zd:{_visible:false},num_wd:{_visible:false},num_zf:{_visible:false},_run:function () { __FUNC_299__ },_event:{NNSendFourCard:function () { __FUNC_300__ },initSceneData:function () { __FUNC_301__ },doCompareCard:function () { __FUNC_302__ }}},_event:{clearCardUI:function () { __FUNC_303__ },initSceneData:function () { __FUNC_304__ },addPlayer:function () { __FUNC_305__ },removePlayer:function () { __FUNC_306__ },roundEnd:function () { __FUNC_307__ },onlinePlayer:function () { __FUNC_308__ }}},Node_player8:{_layout:[[0.2031,0.3153],[0.1,0.15],[0,0]],head:{AtlasLabel_Score:{_run:function () { __FUNC_309__ }},ready:{_run:function () { __FUNC_310__ },_event:{addPlayer:function () { __FUNC_311__ },removePlayer:function () { __FUNC_312__ },NNSendFourCard:function () { __FUNC_313__ },onlinePlayer:function () { __FUNC_314__ }}},tuoguan:{_run:function () { __FUNC_315__ },_event:{beTrust:function () { __FUNC_316__ },cancelTrust:function () { __FUNC_317__ },initSceneData:function () { __FUNC_318__ }}},chatbg:{_run:function () { __FUNC_319__ },chattext:{_event:{MJChat:function () { __FUNC_320__ },playVoice:function () { __FUNC_321__ }}}},_click:function () { __FUNC_322__ },_event:{loadWxHead:function () { __FUNC_323__ },addPlayer:function () { __FUNC_324__ },removePlayer:function () { __FUNC_325__ }},_run:function () { __FUNC_326__ }},panel_Cards:{img_td:{_visible:false},img_zd:{_visible:false},img_wd:{_visible:false},num_td:{_visible:false},num_zd:{_visible:false},num_wd:{_visible:false},num_zf:{_visible:false},_run:function () { __FUNC_327__ },_event:{NNSendFourCard:function () { __FUNC_328__ },initSceneData:function () { __FUNC_329__ },doCompareCard:function () { __FUNC_330__ }}},_event:{clearCardUI:function () { __FUNC_331__ },initSceneData:function () { __FUNC_332__ },addPlayer:function () { __FUNC_333__ },removePlayer:function () { __FUNC_334__ },roundEnd:function () { __FUNC_335__ },onlinePlayer:function () { __FUNC_336__ }}},chat_btn:{_layout:[[0.09,0.09],[0.97,0.0658],[0,0]],_run:function () { __FUNC_337__ },_click:function () { __FUNC_338__ }},voice_btn:{_layout:[[0.09,0.09],[0.97,0.1965],[0,0]],_run:function () { __FUNC_339__ },_touch:function () { __FUNC_340__ },_event:{cancelRecord:function () { __FUNC_341__ },uploadRecord:function () { __FUNC_342__ },sendVoice:function () { __FUNC_343__ },downAndPlayVoice:function () { __FUNC_344__ }}},block_tuoguan:{_layout:[[1,1],[0.5,0.5],[0,0],true],_run:function () { __FUNC_345__ },btn_tuoguan:{_touch:function () { __FUNC_346__ }},_event:{beTrust:function () { __FUNC_348__ },cancelTrust:function () { __FUNC_349__ },initSceneData:function () { __FUNC_350__ }}},layout_guanzhan:{_layout:[[1,1],[0.5,0.5],[0,0],true],_run:function () { __FUNC_351__ },btn_leave:{_touch:function () { __FUNC_352__ }},btn_sitdown:{_touch:function () { __FUNC_353__ }}},Image_guanzhan:{_layout:[[0.1767,0.12],[0.88,0.1],[0,0]],_run:function () { __FUNC_355__ }}},_Node_player1:null,_Node_player2:null,_Node_player3:null,_Node_player4:null,_Node_player5:null,_Node_player6:null,_Node_player7:null,_Node_player8:null,_cuoPaiWait:null,ctor:function () { __FUNC_356__ }});
PlayLayer_thirteenGX.prototype.playShuffleEffect=function () { __FUNC_358__ };
PlayLayer_thirteenGX.prototype.dealShowHandCards=function () { __FUNC_360__ };
PlayLayer_thirteenGX.prototype.dealGunAnimation=function () { __FUNC_362__ };
PlayLayer_thirteenGX.prototype.playGunEffect=function () { __FUNC_363__ };
PlayLayer_thirteenGX.prototype.showHandCard=function () { __FUNC_365__ };
PlayLayer_thirteenGX.prototype.createHandCard=function () { __FUNC_366__ };
PlayLayer_thirteenGX.prototype.showGameRule=function () { __FUNC_367__ };
PlayLayer_thirteenGX.prototype.playChatAni=function () { __FUNC_368__ };
PlayLayer_thirteenGX.prototype.addRestCardLayer=function () { __FUNC_370__ };
---------------------------------------
==========================================================================E
==================================1==================================S
------------------Argv------------------
d
---------------------------------------
----------------Content----------------
playEffectInPlay_thirteenGX("qianghongbao");
---------------------------------------
==========================================================================E
==================================2==================================S
------------------Argv------------------
eD
---------------------------------------
----------------Content----------------
if((MjClient.rePlayVideo != -1)){
return undefined;
}
MjClient.playui.shuffleList.push(eD.uid);
MjClient.playui.playShuffleEffect();
---------------------------------------
==========================================================================E
==================================3==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
MjClient.addHomeView();
MjClient.playui.removeFromParent(true);
delete MjClient.playui;
delete MjClient.endoneui;
delete MjClient.endallui;
cc.audioEngine.stopAllEffects();
playMusic("bgMain");
---------------------------------------
==========================================================================E
==================================4==================================S
------------------Argv------------------
msg,seqAction
---------------------------------------
----------------Content----------------
if(undefined.showEnd){
_local0=[];
_local0.push(cc.delayTime(1));
_local0.push(cc.callFunc(function () { __FUNC_5__ }));
this.runAction(cc.sequence(_local0));
} else {
}
MjClient.Scene.addChild(new StopRoomView());
---------------------------------------
==========================================================================E
==================================5==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
print("");
MjClient.playui.addChild(new GameOver_thirteenGX(undefined),500);
---------------------------------------
==========================================================================E
==================================6==================================S
------------------Argv------------------
msg,pl,seqAction,sData,nPlayerCnt,self
---------------------------------------
----------------Content----------------
if(cc.sys.isObjectValid(MjClient.rankLayer)){
MjClient.rankLayer.removeFromParent();
}
MjClient.SSS_GX_MaPai=undefined.tData.chickenCard;
print(("" + MjClient.SSS_GX_MaPai));
if((MjClient.rePlayVideo == -1)){
_local0=getUIPlayer_thirteenGX(0);
if((_local0.winone > 0)){
playEndWinAni_thirteenGX(MjClient.playui);
} else if((_local0.winone < 0)){
playEndLoseAni_thirteenGX(MjClient.playui);
} else {
}
playEndPingAni_thirteenGX(MjClient.playui);
}
_local1=[];
_aliased1245=MjClient.data.sData;
if((_aliased1245.tData.roundNum <= 0)){
_local1.push(cc.delayTime(2));
}
if((MjClient.rePlayVideo != -1)&&MjClient.replayui){
_local3=_aliased1245.tData.uidsQueue.length;
_local1.push(cc.delayTime((_local3 * 5)));
print(("" + JSON.stringify(_aliased1245.tData)));
}
_aliased7136=this;
_local1.push(cc.callFunc(function () { __FUNC_7__ }));
this.runAction(cc.sequence(_local1));
---------------------------------------
==========================================================================E
==================================7==================================S
------------------Argv------------------
_lay
---------------------------------------
----------------Content----------------
if((_aliased1245.tData.roundNum <= 0)){
print("");
MjClient.playui.addChild(new GameOver_thirteenGX(),500);
}
if(_aliased7136.getChildByTag(650)){
_local0=_aliased7136.getChildByTag(650);
if(_local0){
_local0.removeFromParent(true);
_local0=null;
}}
if(!(isLookOnSSS())){
MjClient.playui.addChild(new EndOneView_thirteenGX(undefined),500,650);
}
---------------------------------------
==========================================================================E
==================================8==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
postEvent("clearCardUI");
---------------------------------------
==========================================================================E
==================================9==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
if(MjClient.playui){
MjClient.addHomeView();
MjClient.playui.removeFromParent(true);
delete MjClient.playui;
delete MjClient.endoneui;
delete MjClient.endallui;
}
---------------------------------------
==========================================================================E
==================================10==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
CheckRoomUiDelete();
---------------------------------------
==========================================================================E
==================================11==================================S
------------------Argv------------------
data
---------------------------------------
----------------Content----------------
print("");
if((MjClient.getCardLayer != null)){
print("");
MjClient.getCardLayer.setData(data);
}
---------------------------------------
==========================================================================E
==================================12==================================S
------------------Argv------------------
msg,sData,tData,i,off,_node,pl,self,mjhand,ccCardNode,num_td,num_zd,num_wd,num_zf
---------------------------------------
----------------Content----------------
CheckRoomUiDelete();
_local0=MjClient.data.sData;
_local1=_local0.tData;
MjClient.SSS_GX_MaPai=_local1.chickenCard;
print(("" + _local1.chickenCard));
if((_local1.tState == TableState.afterReady)){
if((MjClient.rePlayVideo != -1)){
return undefined;
}
_local2=0;
while((_local2 < MjClient.SSS_GX_MaxPlayerNum)){
_local3=_local2;
_local4=getNode_thirteenGX(_local3);
_local5=getUIPlayer_thirteenGX(_local3);
if(_local5&&(_local5.cardStatus == 0)){
doLiPaiAni_thirteenGX(_local4);
} else if(_local5&&(_local5.cardStatus == 1)){
initCard_thirteenGX(_local4);
}
_local2=(_local2 + 1);
_local2=0;
}
_local6=_local0.players[SelfUidSSS()];
if((_local6.cardStatus == 0) && !(isLookOnSSS())){
_aliased7660=_local6.mjhand.concat();
MjClient.selfMjhand=_aliased7660;
MjClient.playui.runAction(cc.sequence(cc.delayTime(1.5),cc.callFunc(function () { __FUNC_13__ })));
break;
}
if((_local1.tState == TableState.roundFinish)){
if((_local1.roundNum < _local1.roundAll)&&(_local1.roundNum >= 0)){
_local2=0;
while((_local2 < MjClient.SSS_GX_MaxPlayerNum)){
_local3=_local2;
_local4=getNode_thirteenGX(_local3);
_local5=getUIPlayer_thirteenGX(_local3);
} else 
_local5.mjhand=_local5.fristCard.concat(_local5.secendCard).concat(_local5.lastCard);
print(("" + JSON.stringify(_local5.mjhand)));
_local8=_local4.getChildByName("panel_Cards");
_local8.visible=true;
ShowAllCard_thirteenGX(_local8,_local3);
_local9=_local8.getChildByName("num_td");
_local9.visible=true;
_local9.setString(TxtScoreFormat_thirteenGX(_local5.fristScore));
_local10=_local8.getChildByName("num_zd");
_local10.visible=true;
_local10.setString(TxtScoreFormat_thirteenGX(_local5.secendScore));
_local11=_local8.getChildByName("num_wd");
_local11.visible=true;
_local11.setString(TxtScoreFormat_thirteenGX(_local5.lastScore));
_local12=_local8.getChildByName("num_zf");
_local12.visible=true;
_local12.setString(TxtScoreFormat_thirteenGX(_local5.winone));
}
_local2=(_local2 + 1);
_local2=0;
}
}}
if((MjClient.rePlayVideo != -1)&&MjClient.replayui){
_local2=0;
while((_local2 < MjClient.SSS_GX_MaxPlayerNum)){
_local3=_local2;
_local4=getNode_thirteenGX(_local3);
initCard_thirteenGX(_local4);
_local2=(_local2 + 1);
_local2=0;
}
}
---------------------------------------
==========================================================================E
==================================13==================================S
------------------Argv------------------
shouCardList,rankLayer
---------------------------------------
----------------Content----------------
_local0=exchangeTo16Data_thirteenGX(_aliased7660);
_local1=new RankLayer_thirteenGX(0);
_local1.OnShow(_local0,60);
MjClient.Scene.addChild(_local1);
---------------------------------------
==========================================================================E
==================================14==================================S
------------------Argv------------------
eD,i,off,_node,pl,mjhand
---------------------------------------
----------------Content----------------
print("");
if(cc.sys.isObjectValid(MjClient.rankLayer)){
print("");
MjClient.rankLayer.removeFromParent();
}
if((MjClient.rePlayVideo != -1)&&MjClient.replayui){
_local0=0;
while((_local0 < MjClient.SSS_GX_MaxPlayerNum)){
_local1=_local0;
_local2=getNode_thirteenGX(_local1);
initCard_thirteenGX(_local2);
_local0=(_local0 + 1);
_local0=0;
}
return undefined;
}
playStartAni_thirteenGX(MjClient.playui._imgPoint);
_local0=0;
while((_local0 < MjClient.SSS_GX_MaxPlayerNum)){
_local1=_local0;
_local3=getUIPlayer_thirteenGX(_local1);
if(_local3&&_aliased7660.mjhandMsg[_local3.info.uid]){
_local3.mjhand=_aliased7660.mjhandMsg[_local3.info.uid];
_local2=getNode_thirteenGX(_local1);
doLiPaiAni_thirteenGX(_local2);
if((_local3.info.uid == SelfUidSSS()) && !(isLookOnSSS())){
_aliased6212=_local3.mjhand.concat();
MjClient.selfMjhand=_aliased6212;
MjClient.playui.runAction(cc.sequence(cc.delayTime(1.5),cc.callFunc(function () { __FUNC_15__ })));
}}
_local0=(_local0 + 1);
_local0=0;
}
---------------------------------------
==========================================================================E
==================================15==================================S
------------------Argv------------------
shouCardList,rankLayer
---------------------------------------
----------------Content----------------
_local0=exchangeTo16Data_thirteenGX(_aliased6212);
_local1=new RankLayer_thirteenGX(_aliased7660.specialMsg[SelfUidSSS()]);
_local1.OnShow(_local0,_aliased7660.trustTime);
MjClient.Scene.addChild(_local1);
---------------------------------------
==========================================================================E
==================================16==================================S
------------------Argv------------------
eD,off,pl,_node
---------------------------------------
----------------Content----------------
_local0=getUiOffByUid_thirteenGX(eD.uid);
_local1=getUIPlayer_thirteenGX(_local0);
if(_local1){
_local2=getNode_thirteenGX(_local0);
initCard_thirteenGX(_local2);
}
---------------------------------------
==========================================================================E
==================================17==================================S
------------------Argv------------------
eD,playerdataArr,i,playdata,maxPlayerNum,j,newCards,time,off
---------------------------------------
----------------Content----------------
if(cc.sys.isObjectValid(MjClient.rankLayer)){
print("");
MjClient.rankLayer.removeFromParent();
}
MjClient.SSS_GX_MaPai=_aliased7660.chickenCard;
print(("" + _aliased7660.chickenCard));
playCompareAni_thirteenGX(MjClient.playui._imgPoint);
_local0=[];
_local1=0;
while((_local1 < _aliased7660.uids.length)){
if(_aliased7660.uids[_local1]){
_local2=_aliased7660.data.players[_aliased7660.uids[_local1]];
if(_local2){
_local0.push(_local2);
}}
_local1=(_local1 + 1);
_local1=0;
}
_local3=_local0.length;
_local1=0;
break;
while((_local1 < 3)){
_local4=0;
while((_local4 < _local0.length)){
_local5=exchangeTo16Data_thirteenGX(_local0[_local4].Card[_local1]);
_local0[_local4].handScore=MjClient.poker_thirteenGX.gatHandScore(_local5);
print(((((_local0[_local4].uid + "") + _local1) + "") + _local0[_local4].handScore));
_local4=(_local4 + 1);
_local4=0;
}
_local0.sort(function () { __FUNC_18__ });
_local4=0;
while((_local4 < _local0.length)){
_local2=_local0[_local4];
print(("" + _local2.uid));
_local6=((((_local1 * _local3) + _local4) * 1) + 2);
_local7=getUiOffByUid_thirteenGX(_local2.uid);
MjClient.playui.dealShowHandCards(_local7,(_local1 + 1),_local2.Card[_local1],_local2.Score[_local1],_local4,_local6,_local3,_local2.winone,_local2.specialType,_local2.isGun,function () { __FUNC_19__ });
_local4=(_local4 + 1);
_local4=0;
}
_local1=(_local1 + 1);
_local1=0;
}
---------------------------------------
==========================================================================E
==================================18==================================S
------------------Argv------------------
a,b
---------------------------------------
----------------Content----------------
return (a.handScore - b.handScore);
---------------------------------------
==========================================================================E
==================================19==================================S
------------------Argv------------------
tData
---------------------------------------
----------------Content----------------
print("");
_local0=_aliased7660.data.tData;
print(("" + _local0.allKillUser));
if((_local0.allKillUser != -1)){
playQuanLeiDaAni_thirteenGX(MjClient.playui._imgPoint);
} else {
}
MjClient.playui.dealGunAnimation(_aliased7660);
---------------------------------------
==========================================================================E
==================================20==================================S
------------------Argv------------------
type,file
---------------------------------------
----------------Content----------------
_local0=util.localStorageEncrypt.getNumberItem(("GameBgType" + MjClient.gameType),0);
_local1=getGameBgFile_thirteenGX(_local0);
this.loadTexture(_local1);
---------------------------------------
==========================================================================E
==================================21==================================S
------------------Argv------------------
type,file
---------------------------------------
----------------Content----------------
_local0=util.localStorageEncrypt.getNumberItem(("GameBgType" + MjClient.gameType),0);
_local1=getGameBgFile_thirteenGX(_local0);
this.loadTexture(_local1);
---------------------------------------
==========================================================================E
==================================22==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
this.visible=false;
---------------------------------------
==========================================================================E
==================================23==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
this.visible=false;
---------------------------------------
==========================================================================E
==================================24==================================S
------------------Argv------------------
tData,tempSize
---------------------------------------
----------------Content----------------
this.visible=true;
_local0=MjClient.data.sData.tData;
_local1=getImageRealSize("poker/table/game_13zhang.png");
this.setContentSize(_local1.w,_local1.h);
this.loadTexture("poker/table/game_13zhang.png");
---------------------------------------
==========================================================================E
==================================25==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
this.ignoreContentAdaptWithSize(true);
---------------------------------------
==========================================================================E
==================================26==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
this.ignoreContentAdaptWithSize(true);
this.setString(("房号：" + MjClient.data.sData.tData.tableid));
---------------------------------------
==========================================================================E
==================================27==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
this.ignoreContentAdaptWithSize(true);
---------------------------------------
==========================================================================E
==================================28==================================S
------------------Argv------------------
sData,tData
---------------------------------------
----------------Content----------------
this.ignoreContentAdaptWithSize(true);
_local0=MjClient.data.sData;
_local1=_local0.tData;
this.setString(((("局数：" + ((_local1.roundAll - _local1.roundNum) + 1)) + "/") + _local1.roundAll));
---------------------------------------
==========================================================================E
==================================29==================================S
------------------Argv------------------
sData,tData
---------------------------------------
----------------Content----------------
this.ignoreContentAdaptWithSize(true);
_local0=MjClient.data.sData;
_local1=_local0.tData;
this.setString(((("局数：" + ((_local1.roundAll - _local1.roundNum) + 1)) + "/") + _local1.roundAll));
---------------------------------------
==========================================================================E
==================================30==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
this.visible=true;
---------------------------------------
==========================================================================E
==================================31==================================S
------------------Argv------------------
btn,et,layout_menu,node_menu
---------------------------------------
----------------Content----------------
_local0=MjClient.playnode.getChildByName("layout_menu");
_local0.visible=true;
_local1=MjClient.playnode.getChildByName("node_menu");
_local1.visible=true;
playEffectInPlay_thirteenGX("sound-button");
---------------------------------------
==========================================================================E
==================================32==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
this.visible=false;
---------------------------------------
==========================================================================E
==================================33==================================S
------------------Argv------------------
btn,eT,node_menu
---------------------------------------
----------------Content----------------
if((eT == 2)){
this.visible=false;
_local0=MjClient.playnode.getChildByName("node_menu");
_local0.visible=false;
playEffectInPlay_thirteenGX("sound-button");
}
---------------------------------------
==========================================================================E
==================================34==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
this.visible=false;
---------------------------------------
==========================================================================E
==================================35==================================S
------------------Argv------------------
settringLayer,shouCardList7,shouCardList8,rankLayer
---------------------------------------
----------------Content----------------
_local0=new Setting_thirteenGX();
_local0.setName("PlayLayerClick");
MjClient.Scene.addChild(_local0);
playEffectInPlay_thirteenGX("sound-button");
return undefined;
_local1=[44,45,36,24,25,26,22,65,6,7,8,9,10];
_local2=[65,81,58,2,3,4,14,81,51,52,53,54,55];
MjClient.SSS_GX_MaPai=18;
_local3=new RankLayer_thirteenGX();
_local3.OnShow(_local1,500);
MjClient.Scene.addChild(_local3);
return undefined;
---------------------------------------
==========================================================================E
==================================36==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
MjClient.showMsg(("" + MjClient.playui.showGameRule()));
playEffectInPlay_thirteenGX("sound-button");
---------------------------------------
==========================================================================E
==================================37==================================S
------------------Argv------------------
sendMsg,tData,content,sData
---------------------------------------
----------------Content----------------
if(!(getClubInfoInTable())){
_local0={uid:SelfUidSSS(),gameType:MjClient.gameType};
_local1=MjClient.data.sData.tData;
if((_local1.owner == SelfUidSSS())){
_local0={uid:SelfUidSSS(),gameType:MjClient.gameType};
MjClient.delRoom(true);
} else {
}
_local2="";
MjClient.showMsg(_local2,function () { __FUNC_38__ },function () { __FUNC_39__ });
_local3=MjClient.data.sData;
} else if(_local3){
onClickBackHallBtn();
}
playEffectInPlay_thirteenGX("sound-button");
---------------------------------------
==========================================================================E
==================================38==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
MjClient.leaveGame();
---------------------------------------
==========================================================================E
==================================39==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
---------------------------------------
==========================================================================E
==================================40==================================S
------------------Argv------------------
sendMsg,content,tData
---------------------------------------
----------------Content----------------
_local0={uid:SelfUidSSS(),gameType:MjClient.gameType};
_local1="";
_local2=MjClient.data.sData.tData;
if((_local2.owner == SelfUidSSS())){
_local1="";
}
MjClient.showMsg(_local1,function () { __FUNC_41__ },function () { __FUNC_42__ });
playEffectInPlay_thirteenGX("sound-button");
---------------------------------------
==========================================================================E
==================================41==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
MjClient.leaveGame();
---------------------------------------
==========================================================================E
==================================42==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
---------------------------------------
==========================================================================E
==================================43==================================S
------------------Argv------------------
sendMsg
---------------------------------------
----------------Content----------------
_local0={uid:SelfUidSSS(),gameType:MjClient.gameType};
MjClient.delRoom(true);
playEffectInPlay_thirteenGX("sound-button");
---------------------------------------
==========================================================================E
==================================44==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
this.visible=false;
---------------------------------------
==========================================================================E
==================================45==================================S
------------------Argv------------------
btn,et,that
---------------------------------------
----------------Content----------------
_aliased7490=btn.getChildByName("img_ruleBg");
if(_aliased7490.visible){
_aliased7490.stopAllActions();
_aliased7490.runAction(cc.sequence(cc.scaleTo(0.2,0).easing(cc.easeBackIn()),cc.callFunc(function () { __FUNC_46__ })));
} else {
}
_aliased7490.visible=true;
_aliased7490.runAction(cc.sequence(cc.scaleTo(0.2,1.7).easing(cc.easeBackOut()),cc.delayTime(5),cc.scaleTo(0.2,0).easing(cc.easeBackIn()),cc.callFunc(function () { __FUNC_47__ })));
MjClient.native.umengEvent4CountWithProperty("Fangjiannei_Wanfa",{uid:SelfUidSSS()});
---------------------------------------
==========================================================================E
==================================46==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
_aliased7490.visible=false;
---------------------------------------
==========================================================================E
==================================47==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
_aliased7490.visible=false;
---------------------------------------
==========================================================================E
==================================48==================================S
------------------Argv------------------
that
---------------------------------------
----------------Content----------------
_aliased1746=this;
this.scale=0;
_aliased1746.runAction(cc.sequence(cc.scaleTo(0.2,1.7).easing(cc.easeBackOut()),cc.delayTime(5),cc.scaleTo(0.2,0).easing(cc.easeBackIn()),cc.callFunc(function () { __FUNC_49__ })).repeat(1));
---------------------------------------
==========================================================================E
==================================49==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
_aliased1746.visible=false;
---------------------------------------
==========================================================================E
==================================50==================================S
------------------Argv------------------
text_rule,img_ruleBg,maxWidth,ruleGap,ruleInfo,fangkaRoomLevelName,i,tempStr,ruleInfoNum,size,ruleArr,text_ruleCopy,innerHeight,maxHeight
---------------------------------------
----------------Content----------------
_local0=this.getChildByName("text_rule");
_local1=this.getParent();
_local2=_local1.width;
_local3=(_local0.height / 2);
_local4=MjClient.playui.showGameRule().split(",");
cc.log(("sssddd text_rule:" + MjClient.playui.showGameRule()));
_local5=FriendCard_Common.getFangkaRoomLevelName();
if(_local5){
_local4.push((_local5 + "房间"));
}
_local6=0;
while((_local6 < _local4.length)){
if((_local4[_local6].length <= 12)){
} else {
}
_local7=_local4[_local6];
_local4[_local6]=_local7.substr(0,12);
_local4.splice(_local6,0,_local7.substr(12));
_local6=(_local6 + 1);
_local6=0;
}
_local8=_local4.length;
(_local8 > 19)?_local9=14:_local9=_local0.getFontSize();
_local10=[];
_local6=0;
while((_local6 < _local4.length)){
_local11=_local0.clone();
_local11.setFontSize(_local9);
_local11.setVisible(true);
_local11.ignoreContentAdaptWithSize(true);
_local11.setString(_local4[_local6]);
_local10.push(_local11);
(_local11.getContentSize().width > _local2)?_local2=_local11.getContentSize().width:_local2=_local2;
_local6=(_local6 + 1);
_local6=0;
}
_local1.width=(_local2 * 1.05);
_local12=((_local11.getContentSize().height * (_local8 + 1)) * 1.25);
_local13=(_local0.height * 24);
if((_local12 < _local13)){
_local13=_local12;
}
_local1.height=_local13;
this.setScrollBarOpacity(0);
this.setPosition((_local1.width / 2),(_local13 - 16));
this.setContentSize(_local1.width,(_local13 - 16));
this.setInnerContainerSize(cc.size(_local1.width,(_local12 - 16)));
_local6=0;
while((_local6 < _local10.length)){
_local10[_local6].x=(this.width / 2);
_local10[_local6].y=(_local3 + ((_local10[_local6].getContentSize().height * _local6) * 1.25));
this.addChild(_local10[_local6]);
_local6=(_local6 + 1);
_local6=0;
}
---------------------------------------
==========================================================================E
==================================51==================================S
------------------Argv------------------
ccNumber
---------------------------------------
----------------Content----------------
setWgtLayout(this,[0.4,0.064],[0.5,0.5],[0,0]);
_local0=this.getChildByName("number");
_local0.setString("0");
_local0.ignoreContentAdaptWithSize(true);
this.visible=false;
---------------------------------------
==========================================================================E
==================================52==================================S
------------------Argv------------------
msg,ccNumber
---------------------------------------
----------------Content----------------
this.visible=false;
_local0=this.getChildByName("number");
setTuoGuanCountDown_thirteenGX(msg,_local0);
---------------------------------------
==========================================================================E
==================================53==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
this.visible=isLookOnSSS();
setWgtLayout(this,[0.13888,0.10402],[0.42,0.47],[0,0]);
---------------------------------------
==========================================================================E
==================================54==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
MjClient.playui._BtnReady.visible=false;
MjClient.playui._BtnXiPai.visible=false;
MjClient.playui._WaitNode.visible=false;
if((MjClient.rePlayVideo != -1)){
return undefined;
}
MjClient.gamenet.request("pkroom.handler.tableMsg",{cmd:"MJPass"});
playEffectInPlay_thirteenGX("sound-button");
---------------------------------------
==========================================================================E
==================================55==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
this.visible=false;
---------------------------------------
==========================================================================E
==================================56==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
this.visible=false;
---------------------------------------
==========================================================================E
==================================57==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
this.visible=!(isLookOnSSS());
---------------------------------------
==========================================================================E
==================================58==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
this.visible=!(isLookOnSSS());
---------------------------------------
==========================================================================E
==================================59==================================S
------------------Argv------------------
eD,sData,tData,pl
---------------------------------------
----------------Content----------------
this.visible=false;
_local0=MjClient.data.sData;
_local1=_local0.tData;
_local2=_local0.players[SelfUidSSS()];
if((_local1.tState == TableState.waitReady)&&(_local2.mjState == TableState.waitReady)){
this.visible=!(isLookOnSSS());
}
if((_local1.tState == TableState.roundFinish) && (_local2.mjState == TableState.roundFinish) || (_local2.mjState == TableState.waitReady)){
this.visible=!(isLookOnSSS());
}
---------------------------------------
==========================================================================E
==================================60==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
this.visible=isLookOnSSS();
setWgtLayout(this,[0.13888,0.10402],[0.58,0.47],[0,0]);
---------------------------------------
==========================================================================E
==================================61==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
MjClient.playui._BtnReady.visible=false;
MjClient.playui._BtnXiPai.visible=false;
MjClient.playui._WaitNode.visible=false;
MjClient.gamenet.request("pkroom.handler.tableMsg",{cmd:"MJShuffle"},function () { __FUNC_62__ });
playEffectInPlay_thirteenGX("sound-button");
---------------------------------------
==========================================================================E
==================================62==================================S
------------------Argv------------------
data
---------------------------------------
----------------Content----------------
if(data&&(data.code == -1)){
MjClient.showToast(data.message);
return undefined;
}
MjClient.gamenet.request("pkroom.handler.tableMsg",{cmd:"MJPass"});
---------------------------------------
==========================================================================E
==================================63==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
this.visible=false;
---------------------------------------
==========================================================================E
==================================64==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
this.visible=false;
---------------------------------------
==========================================================================E
==================================65==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
this.visible=!(isLookOnSSS());
---------------------------------------
==========================================================================E
==================================66==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
this.visible=!(isLookOnSSS());
---------------------------------------
==========================================================================E
==================================67==================================S
------------------Argv------------------
eD,sData,tData,pl
---------------------------------------
----------------Content----------------
this.visible=false;
_local0=MjClient.data.sData;
_local1=_local0.tData;
_local2=_local0.players[SelfUidSSS()];
if((_local1.tState == TableState.waitReady)&&(_local2.mjState == TableState.waitReady)){
this.visible=!(isLookOnSSS());
}
if((_local1.tState == TableState.roundFinish) && (_local2.mjState == TableState.roundFinish) || (_local2.mjState == TableState.waitReady)){
this.visible=!(isLookOnSSS());
}
---------------------------------------
==========================================================================E
==================================68==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
this.visible=false;
setWgtLayout(this,[0.13888,0.10402],[0.42,0.47],[0,0]);
---------------------------------------
==========================================================================E
==================================69==================================S
------------------Argv------------------
shouCardList,showCardLayer
---------------------------------------
----------------Content----------------
_local0=exchangeTo16Data_thirteenGX(MjClient.selfMjhand);
_local1=new ShowCardLayer_thirteenGX(_local0);
MjClient.Scene.addChild(_local1);
playEffectInPlay_thirteenGX("sound-button");
---------------------------------------
==========================================================================E
==================================70==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
this.visible=false;
---------------------------------------
==========================================================================E
==================================71==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
this.visible=false;
---------------------------------------
==========================================================================E
==================================72==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
this.visible=false;
---------------------------------------
==========================================================================E
==================================73==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
this.visible=false;
---------------------------------------
==========================================================================E
==================================74==================================S
------------------Argv------------------
eD
---------------------------------------
----------------Content----------------
this.visible=true;
---------------------------------------
==========================================================================E
==================================75==================================S
------------------Argv------------------
eD
---------------------------------------
----------------Content----------------
this.visible=false;
---------------------------------------
==========================================================================E
==================================76==================================S
------------------Argv------------------
eD
---------------------------------------
----------------Content----------------
this.visible=false;
---------------------------------------
==========================================================================E
==================================77==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
this.visible=false;
setWgtLayout(this,[0.13888,0.10402],[0.58,0.47],[0,0]);
---------------------------------------
==========================================================================E
==================================78==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
MjClient.gamenet.request("pkroom.handler.tableMsg",{cmd:"onUserReset"},function () { __FUNC_79__ });
playEffectInPlay_thirteenGX("sound-button");
---------------------------------------
==========================================================================E
==================================79==================================S
------------------Argv------------------
data,selfUid,off,node,ccCardNode
---------------------------------------
----------------Content----------------
_local0=SelfUidSSS();
_local1=getUiOffByUid_thirteenGX(_local0);
_local2=getNode_thirteenGX(_local1);
_local3=_local2.getChildByName("panel_Cards");
_local3.visible=false;
doLiPaiAni_thirteenGX(_local2);
MjClient.playui.runAction(cc.sequence(cc.delayTime(1.5),cc.callFunc(function () { __FUNC_80__ })));
---------------------------------------
==========================================================================E
==================================80==================================S
------------------Argv------------------
shouCardList,rankLayer
---------------------------------------
----------------Content----------------
_local0=exchangeTo16Data_thirteenGX(MjClient.selfMjhand);
_local1=new RankLayer_thirteenGX(0);
_local1.OnShow(_local0,_aliased1746.trustTime);
MjClient.Scene.addChild(_local1);
---------------------------------------
==========================================================================E
==================================81==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
this.visible=false;
---------------------------------------
==========================================================================E
==================================82==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
this.visible=false;
---------------------------------------
==========================================================================E
==================================83==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
this.visible=false;
---------------------------------------
==========================================================================E
==================================84==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
this.visible=false;
---------------------------------------
==========================================================================E
==================================85==================================S
------------------Argv------------------
eD
---------------------------------------
----------------Content----------------
this.visible=true;
---------------------------------------
==========================================================================E
==================================86==================================S
------------------Argv------------------
eD
---------------------------------------
----------------Content----------------
this.visible=false;
---------------------------------------
==========================================================================E
==================================87==================================S
------------------Argv------------------
eD
---------------------------------------
----------------Content----------------
this.visible=false;
---------------------------------------
==========================================================================E
==================================88==================================S
------------------Argv------------------
ccNumber
---------------------------------------
----------------Content----------------
this.visible=false;
setWgtLayout(this,[0.0727,0.1361],[0.5,0.5703],[0,0]);
_local0=this.getChildByName("lbl_count");
_local0.setString("0");
_local0.ignoreContentAdaptWithSize(true);
---------------------------------------
==========================================================================E
==================================89==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
this.visible=false;
---------------------------------------
==========================================================================E
==================================90==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
this.visible=true;
---------------------------------------
==========================================================================E
==================================91==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
this.visible=false;
---------------------------------------
==========================================================================E
==================================92==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
this.visible=false;
---------------------------------------
==========================================================================E
==================================93==================================S
------------------Argv------------------
eD
---------------------------------------
----------------Content----------------
this.visible=false;
---------------------------------------
==========================================================================E
==================================94==================================S
------------------Argv------------------
eD
---------------------------------------
----------------Content----------------
this.visible=false;
---------------------------------------
==========================================================================E
==================================95==================================S
------------------Argv------------------
eD,ccNumber
---------------------------------------
----------------Content----------------
_local0=this.getChildByName("lbl_count");
eD.trustTime||setTuoGuanCountDown_thirteenGX({tipCountDown:60},_local0);
---------------------------------------
==========================================================================E
==================================96==================================S
------------------Argv------------------
tempSize
---------------------------------------
----------------Content----------------
setWgtLayout(this,[0.11,0.11],[0.05,0.45],[0,0]);
_local0=getImageRealSize("playing/gameTable/yaoqing_14.png");
this.setContentSize(_local0.w,_local0.h);
---------------------------------------
==========================================================================E
==================================97==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
MjClient.delRoom(true);
---------------------------------------
==========================================================================E
==================================98==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
this.visible=false;
---------------------------------------
==========================================================================E
==================================99==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
this.visible=false;
---------------------------------------
==========================================================================E
==================================100==================================S
------------------Argv------------------
tempSize
---------------------------------------
----------------Content----------------
setWgtLayout(this,[0.11,0.11],[0.05,0.6],[0,0]);
this.loadTextures("playing/gameTable/yaoqing_18.png","playing/gameTable/yaoqing_18.png","");
_local0=getImageRealSize("playing/gameTable/yaoqing_18.png");
this.setContentSize(_local0.w,_local0.h);
---------------------------------------
==========================================================================E
==================================101==================================S
------------------Argv------------------
btn,sData
---------------------------------------
----------------Content----------------
_local0=MjClient.data.sData;
if(_local0){
if(IsRoomCreator()){
MjClient.showMsg("",function () { __FUNC_102__ },function () { __FUNC_103__ });
} else {
}
MjClient.showMsg("",function () { __FUNC_104__ },function () { __FUNC_105__ });
}
---------------------------------------
==========================================================================E
==================================102==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
MjClient.leaveGame();
---------------------------------------
==========================================================================E
==================================103==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
---------------------------------------
==========================================================================E
==================================104==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
MjClient.leaveGame();
---------------------------------------
==========================================================================E
==================================105==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
---------------------------------------
==========================================================================E
==================================106==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
this.visible=false;
---------------------------------------
==========================================================================E
==================================107==================================S
------------------Argv------------------
eD,sData,tData,pl
---------------------------------------
----------------Content----------------
this.visible=IsInviteVisible();
this.getChildByName("delroom").visible=IsInviteVisible();
this.getChildByName("backHomebtn").visible=IsInviteVisible() && !(getClubInfoInTable());
_local0=MjClient.data.sData;
_local1=_local0.tData;
_local2=_local0.players[SelfUidSSS()];
if((_local1.roundNum == _local1.roundAll)){
if((_local1.tState == TableState.waitReady)&&(_local2.mjState == TableState.waitReady)){
this.visible=true;
this.getChildByName("delroom").visible=false;
this.getChildByName("backHomebtn").visible=false;
}
} else 
this.visible=true;
this.getChildByName("delroom").visible=false;
this.getChildByName("backHomebtn").visible=false;
}
this.getChildByName("delroom").visible=false;
this.getChildByName("backHomebtn").visible=false;
---------------------------------------
==========================================================================E
==================================108==================================S
------------------Argv------------------
eD
---------------------------------------
----------------Content----------------
console.log(">>>>>> play add player >>>>");
this.visible=IsInviteVisible();
this.getChildByName("delroom").visible=IsInviteVisible();
this.getChildByName("backHomebtn").visible=IsInviteVisible() && !(getClubInfoInTable());
this.getChildByName("delroom").visible=false;
this.getChildByName("backHomebtn").visible=false;
---------------------------------------
==========================================================================E
==================================109==================================S
------------------Argv------------------
eD
---------------------------------------
----------------Content----------------
this.visible=IsInviteVisible();
this.getChildByName("delroom").visible=IsInviteVisible();
this.getChildByName("backHomebtn").visible=IsInviteVisible() && !(getClubInfoInTable());
this.getChildByName("delroom").visible=false;
this.getChildByName("backHomebtn").visible=false;
---------------------------------------
==========================================================================E
==================================110==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
this.visible=false;
---------------------------------------
==========================================================================E
==================================111==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
this.visible=false;
---------------------------------------
==========================================================================E
==================================112==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
this.visible=false;
---------------------------------------
==========================================================================E
==================================113==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
this.visible=true;
this.getChildByName("delroom").visible=false;
this.getChildByName("backHomebtn").visible=false;
---------------------------------------
==========================================================================E
==================================114==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
this.visible=false;
---------------------------------------
==========================================================================E
==================================115==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
this.visible=false;
---------------------------------------
==========================================================================E
==================================116==================================S
------------------Argv------------------
msg
---------------------------------------
----------------Content----------------
if(getUIPlayer_thirteenGX(0)&&(getUIPlayer_thirteenGX(0).info.uid == msg.uid)){
this.visible=true;
}
---------------------------------------
==========================================================================E
==================================117==================================S
------------------Argv------------------
msg
---------------------------------------
----------------Content----------------
if(getUIPlayer_thirteenGX(0)&&(getUIPlayer_thirteenGX(0).info.uid == msg.uid)){
this.visible=false;
}
---------------------------------------
==========================================================================E
==================================118==================================S
------------------Argv------------------
msg,pl
---------------------------------------
----------------Content----------------
_local0=getUIPlayer_thirteenGX(0);
if(_local0&&_local0.trust){
this.visible=true;
} else {
}
this.visible=false;
---------------------------------------
==========================================================================E
==================================119==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
this.getParent().zIndex=600;
---------------------------------------
==========================================================================E
==================================120==================================S
------------------Argv------------------
msg
---------------------------------------
----------------Content----------------
showUserChat(this,0,msg);
---------------------------------------
==========================================================================E
==================================121==================================S
------------------Argv------------------
voicePath
---------------------------------------
----------------Content----------------
MjClient.data._tempMessage.msg=voicePath;
showUserChat(this,0,MjClient.data._tempMessage);
---------------------------------------
==========================================================================E
==================================122==================================S
------------------Argv------------------
btn
---------------------------------------
----------------Content----------------
MjClient.playui.getCardLayer.getData();
---------------------------------------
==========================================================================E
==================================123==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
---------------------------------------
==========================================================================E
==================================124==================================S
------------------Argv------------------
d
---------------------------------------
----------------Content----------------
setWxHead_thirteenGX(this,d,0);
---------------------------------------
==========================================================================E
==================================125==================================S
------------------Argv------------------
eD
---------------------------------------
----------------Content----------------
---------------------------------------
==========================================================================E
==================================126==================================S
------------------Argv------------------
eD
---------------------------------------
----------------Content----------------
---------------------------------------
==========================================================================E
==================================127==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
this.visible=false;
---------------------------------------
==========================================================================E
==================================128==================================S
------------------Argv------------------
mjHandMsg
---------------------------------------
----------------Content----------------
print(("" + JSON.stringify(mjHandMsg)));
this.visible=false;
---------------------------------------
==========================================================================E
==================================129==================================S
------------------Argv------------------
msg,pl,tData
---------------------------------------
----------------Content----------------
_local0=getUIPlayer_thirteenGX(0);
if(_local0 && msg.players[_local0.info.uid] && (isCenterJoin_thirteenGX(0) == false)){
_local1=MjClient.data.sData.tData;
if((_local0.mjState == TableState.waitJiazhu)||(_local0.mjState == TableState.waitTanPai)){
this.visible=true;
} else if((_local0.mjState == TableState.roundFinish)){
this.visible=true;
} else {
}
this.visible=false;
} else {
}
this.visible=false;
---------------------------------------
==========================================================================E
==================================130==================================S
------------------Argv------------------
msg,off,pl,data
---------------------------------------
----------------Content----------------
_local0=0;
_local1=getUIPlayer_thirteenGX(_local0);
if(_local1&&(msg.uids.indexOf(_local1.info.uid) != -1)){
_local2=msg.data.players[_local1.info.uid];
if(_local2){
this.visible=true;
_local1.mjhand=_local2.Card[0].concat(_local2.Card[1]).concat(_local2.Card[2]);
}}
---------------------------------------
==========================================================================E
==================================131==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
GetReadyVisible(this,0);
---------------------------------------
==========================================================================E
==================================132==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
GetReadyVisible(this,-1);
---------------------------------------
==========================================================================E
==================================133==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
GetReadyVisible(this,0);
---------------------------------------
==========================================================================E
==================================134==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
GetReadyVisible(this,0);
---------------------------------------
==========================================================================E
==================================135==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
GetReadyVisible(this,0);
---------------------------------------
==========================================================================E
==================================136==================================S
------------------Argv------------------
eD
---------------------------------------
----------------Content----------------
this.visible=false;
---------------------------------------
==========================================================================E
==================================137==================================S
------------------Argv------------------
eD
---------------------------------------
----------------Content----------------
GetReadyVisible(this,0);
---------------------------------------
==========================================================================E
==================================138==================================S
------------------Argv------------------
msg
---------------------------------------
----------------Content----------------
SetUserVisible_thirteenGX(this,0);
---------------------------------------
==========================================================================E
==================================139==================================S
------------------Argv------------------
eD
---------------------------------------
----------------Content----------------
SetUserVisible_thirteenGX(this,0);
---------------------------------------
==========================================================================E
==================================140==================================S
------------------Argv------------------
eD
---------------------------------------
----------------Content----------------
SetUserVisible_thirteenGX(this,0);
---------------------------------------
==========================================================================E
==================================141==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
InitUserCoinAndName_thirteenGX(this,0);
---------------------------------------
==========================================================================E
==================================142==================================S
------------------Argv------------------
msg,pl
---------------------------------------
----------------Content----------------
_local0=getUIPlayer_thirteenGX(0);
if(_local0&&(_local0.info.uid == msg.uid)){
_local0.onLine=msg.onLine;
setUserOffline(this,0);
}
---------------------------------------
==========================================================================E
==================================143==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
this.visible=false;
---------------------------------------
==========================================================================E
==================================144==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
GetReadyVisible(this,1);
---------------------------------------
==========================================================================E
==================================145==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
GetReadyVisible(this,1);
---------------------------------------
==========================================================================E
==================================146==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
GetReadyVisible(this,1);
---------------------------------------
==========================================================================E
==================================147==================================S
------------------Argv------------------
eD
---------------------------------------
----------------Content----------------
this.visible=false;
---------------------------------------
==========================================================================E
==================================148==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
GetReadyVisible(this,1);
---------------------------------------
==========================================================================E
==================================149==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
this.visible=false;
---------------------------------------
==========================================================================E
==================================150==================================S
------------------Argv------------------
msg
---------------------------------------
----------------Content----------------
if(getUIPlayer_thirteenGX(1)&&(getUIPlayer_thirteenGX(1).info.uid == msg.uid)){
this.visible=true;
}
---------------------------------------
==========================================================================E
==================================151==================================S
------------------Argv------------------
msg
---------------------------------------
----------------Content----------------
if(getUIPlayer_thirteenGX(1)&&(getUIPlayer_thirteenGX(1).info.uid == msg.uid)){
this.visible=false;
}
---------------------------------------
==========================================================================E
==================================152==================================S
------------------Argv------------------
msg,pl
---------------------------------------
----------------Content----------------
_local0=getUIPlayer_thirteenGX(1);
if(_local0&&_local0.trust){
this.visible=true;
} else {
}
this.visible=false;
---------------------------------------
==========================================================================E
==================================153==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
this.getParent().zIndex=500;
---------------------------------------
==========================================================================E
==================================154==================================S
------------------Argv------------------
msg
---------------------------------------
----------------Content----------------
showUserChat(this,1,msg);
---------------------------------------
==========================================================================E
==================================155==================================S
------------------Argv------------------
voicePath
---------------------------------------
----------------Content----------------
MjClient.data._tempMessage.msg=voicePath;
showUserChat(this,1,MjClient.data._tempMessage);
---------------------------------------
==========================================================================E
==================================156==================================S
------------------Argv------------------
btn
---------------------------------------
----------------Content----------------
showPlayerInfo(1,btn);
---------------------------------------
==========================================================================E
==================================157==================================S
------------------Argv------------------
d
---------------------------------------
----------------Content----------------
setWxHead_thirteenGX(this,d,1);
---------------------------------------
==========================================================================E
==================================158==================================S
------------------Argv------------------
eD
---------------------------------------
----------------Content----------------
---------------------------------------
==========================================================================E
==================================159==================================S
------------------Argv------------------
eD
---------------------------------------
----------------Content----------------
---------------------------------------
==========================================================================E
==================================160==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
---------------------------------------
==========================================================================E
==================================161==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
this.visible=false;
---------------------------------------
==========================================================================E
==================================162==================================S
------------------Argv------------------
mjHandMsg
---------------------------------------
----------------Content----------------
this.visible=false;
---------------------------------------
==========================================================================E
==================================163==================================S
------------------Argv------------------
msg,pl,tData
---------------------------------------
----------------Content----------------
_local0=getUIPlayer_thirteenGX(1);
if(_local0&&msg.players[_local0.info.uid]){
_local1=MjClient.data.sData.tData;
if((_local1.tState == TableState.waitJiazhu) || (_local1.tState == TableState.waitTanPai) || (_local1.tState == TableState.waitCard)){
this.visible=true;
ShowFourCard_thirteenGX(this,1,false);
} else {
}
this.visible=false;
} else {
}
this.visible=false;
---------------------------------------
==========================================================================E
==================================164==================================S
------------------Argv------------------
msg,off,pl,data
---------------------------------------
----------------Content----------------
_local0=1;
_local1=getUIPlayer_thirteenGX(_local0);
if(_local1&&(msg.uids.indexOf(_local1.info.uid) != -1)){
_local2=msg.data.players[_local1.info.uid];
if(_local2){
this.visible=true;
_local1.mjhand=_local2.Card[0].concat(_local2.Card[1]).concat(_local2.Card[2]);
ShowFourCard_thirteenGX(this,_local0,true);
}}
---------------------------------------
==========================================================================E
==================================165==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
---------------------------------------
==========================================================================E
==================================166==================================S
------------------Argv------------------
eD
---------------------------------------
----------------Content----------------
SetUserVisible_thirteenGX(this,1);
---------------------------------------
==========================================================================E
==================================167==================================S
------------------Argv------------------
eD
---------------------------------------
----------------Content----------------
SetUserVisible_thirteenGX(this,1);
---------------------------------------
==========================================================================E
==================================168==================================S
------------------Argv------------------
eD
---------------------------------------
----------------Content----------------
SetUserVisible_thirteenGX(this,1);
---------------------------------------
==========================================================================E
==================================169==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
InitUserCoinAndName_thirteenGX(this,1);
---------------------------------------
==========================================================================E
==================================170==================================S
------------------Argv------------------
msg,pl
---------------------------------------
----------------Content----------------
_local0=getUIPlayer_thirteenGX(1);
if(_local0&&(_local0.info.uid == msg.uid)){
_local0.onLine=msg.onLine;
setUserOffline(this,1);
}
---------------------------------------
==========================================================================E
==================================171==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
this.visible=false;
---------------------------------------
==========================================================================E
==================================172==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
GetReadyVisible(this,2);
---------------------------------------
==========================================================================E
==================================173==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
GetReadyVisible(this,2);
---------------------------------------
==========================================================================E
==================================174==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
GetReadyVisible(this,2);
---------------------------------------
==========================================================================E
==================================175==================================S
------------------Argv------------------
eD
---------------------------------------
----------------Content----------------
this.visible=false;
---------------------------------------
==========================================================================E
==================================176==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
GetReadyVisible(this,2);
---------------------------------------
==========================================================================E
==================================177==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
this.visible=false;
---------------------------------------
==========================================================================E
==================================178==================================S
------------------Argv------------------
msg
---------------------------------------
----------------Content----------------
if(getUIPlayer_thirteenGX(2)&&(getUIPlayer_thirteenGX(2).info.uid == msg.uid)){
this.visible=true;
}
---------------------------------------
==========================================================================E
==================================179==================================S
------------------Argv------------------
msg
---------------------------------------
----------------Content----------------
if(getUIPlayer_thirteenGX(2)&&(getUIPlayer_thirteenGX(2).info.uid == msg.uid)){
this.visible=false;
}
---------------------------------------
==========================================================================E
==================================180==================================S
------------------Argv------------------
msg,pl
---------------------------------------
----------------Content----------------
_local0=getUIPlayer_thirteenGX(2);
if(_local0&&_local0.trust){
this.visible=true;
} else {
}
this.visible=false;
---------------------------------------
==========================================================================E
==================================181==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
this.getParent().zIndex=500;
---------------------------------------
==========================================================================E
==================================182==================================S
------------------Argv------------------
msg
---------------------------------------
----------------Content----------------
showUserChat(this,2,msg);
---------------------------------------
==========================================================================E
==================================183==================================S
------------------Argv------------------
voicePath
---------------------------------------
----------------Content----------------
MjClient.data._tempMessage.msg=voicePath;
showUserChat(this,2,MjClient.data._tempMessage);
---------------------------------------
==========================================================================E
==================================184==================================S
------------------Argv------------------
btn
---------------------------------------
----------------Content----------------
showPlayerInfo(2,btn);
---------------------------------------
==========================================================================E
==================================185==================================S
------------------Argv------------------
d
---------------------------------------
----------------Content----------------
setWxHead_thirteenGX(this,d,2);
---------------------------------------
==========================================================================E
==================================186==================================S
------------------Argv------------------
eD
---------------------------------------
----------------Content----------------
---------------------------------------
==========================================================================E
==================================187==================================S
------------------Argv------------------
eD
---------------------------------------
----------------Content----------------
---------------------------------------
==========================================================================E
==================================188==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
---------------------------------------
==========================================================================E
==================================189==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
this.visible=false;
---------------------------------------
==========================================================================E
==================================190==================================S
------------------Argv------------------
mjHandMsg
---------------------------------------
----------------Content----------------
this.visible=false;
---------------------------------------
==========================================================================E
==================================191==================================S
------------------Argv------------------
msg,pl,tData
---------------------------------------
----------------Content----------------
_local0=getUIPlayer_thirteenGX(2);
if(_local0&&msg.players[_local0.info.uid]){
_local1=MjClient.data.sData.tData;
if((_local1.tState == TableState.waitJiazhu) || (_local1.tState == TableState.waitTanPai) || (_local1.tState == TableState.waitCard)){
this.visible=true;
ShowFourCard_thirteenGX(this,2,false);
} else {
}
this.visible=false;
} else {
}
this.visible=false;
---------------------------------------
==========================================================================E
==================================192==================================S
------------------Argv------------------
msg,off,pl,data
---------------------------------------
----------------Content----------------
_local0=2;
_local1=getUIPlayer_thirteenGX(_local0);
if(_local1&&(msg.uids.indexOf(_local1.info.uid) != -1)){
_local2=msg.data.players[_local1.info.uid];
if(_local2){
this.visible=true;
_local1.mjhand=_local2.Card[0].concat(_local2.Card[1]).concat(_local2.Card[2]);
ShowFourCard_thirteenGX(this,_local0,true);
}}
---------------------------------------
==========================================================================E
==================================193==================================S
------------------Argv------------------
eD
---------------------------------------
----------------Content----------------
SetUserVisible_thirteenGX(this,2);
---------------------------------------
==========================================================================E
==================================194==================================S
------------------Argv------------------
eD
---------------------------------------
----------------Content----------------
SetUserVisible_thirteenGX(this,2);
---------------------------------------
==========================================================================E
==================================195==================================S
------------------Argv------------------
eD
---------------------------------------
----------------Content----------------
SetUserVisible_thirteenGX(this,2);
---------------------------------------
==========================================================================E
==================================196==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
InitUserCoinAndName_thirteenGX(this,2);
---------------------------------------
==========================================================================E
==================================197==================================S
------------------Argv------------------
msg,pl
---------------------------------------
----------------Content----------------
_local0=getUIPlayer_thirteenGX(2);
if(_local0&&(_local0.info.uid == msg.uid)){
_local0.onLine=msg.onLine;
setUserOffline(this,2);
}
---------------------------------------
==========================================================================E
==================================198==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
this.visible=false;
---------------------------------------
==========================================================================E
==================================199==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
GetReadyVisible(this,3);
---------------------------------------
==========================================================================E
==================================200==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
GetReadyVisible(this,3);
---------------------------------------
==========================================================================E
==================================201==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
GetReadyVisible(this,3);
---------------------------------------
==========================================================================E
==================================202==================================S
------------------Argv------------------
eD
---------------------------------------
----------------Content----------------
this.visible=false;
---------------------------------------
==========================================================================E
==================================203==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
GetReadyVisible(this,3);
---------------------------------------
==========================================================================E
==================================204==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
this.visible=false;
---------------------------------------
==========================================================================E
==================================205==================================S
------------------Argv------------------
msg
---------------------------------------
----------------Content----------------
if(getUIPlayer_thirteenGX(3)&&(getUIPlayer_thirteenGX(3).info.uid == msg.uid)){
this.visible=true;
}
---------------------------------------
==========================================================================E
==================================206==================================S
------------------Argv------------------
msg
---------------------------------------
----------------Content----------------
if(getUIPlayer_thirteenGX(3)&&(getUIPlayer_thirteenGX(3).info.uid == msg.uid)){
this.visible=false;
}
---------------------------------------
==========================================================================E
==================================207==================================S
------------------Argv------------------
msg,pl
---------------------------------------
----------------Content----------------
_local0=getUIPlayer_thirteenGX(3);
if(_local0&&_local0.trust){
this.visible=true;
} else {
}
this.visible=false;
---------------------------------------
==========================================================================E
==================================208==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
this.getParent().zIndex=500;
---------------------------------------
==========================================================================E
==================================209==================================S
------------------Argv------------------
msg
---------------------------------------
----------------Content----------------
showUserChat(this,3,msg);
---------------------------------------
==========================================================================E
==================================210==================================S
------------------Argv------------------
voicePath
---------------------------------------
----------------Content----------------
MjClient.data._tempMessage.msg=voicePath;
showUserChat(this,3,MjClient.data._tempMessage);
---------------------------------------
==========================================================================E
==================================211==================================S
------------------Argv------------------
btn
---------------------------------------
----------------Content----------------
showPlayerInfo(3,btn);
---------------------------------------
==========================================================================E
==================================212==================================S
------------------Argv------------------
d
---------------------------------------
----------------Content----------------
setWxHead_thirteenGX(this,d,3);
---------------------------------------
==========================================================================E
==================================213==================================S
------------------Argv------------------
eD
---------------------------------------
----------------Content----------------
---------------------------------------
==========================================================================E
==================================214==================================S
------------------Argv------------------
eD
---------------------------------------
----------------Content----------------
---------------------------------------
==========================================================================E
==================================215==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
---------------------------------------
==========================================================================E
==================================216==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
this.visible=false;
---------------------------------------
==========================================================================E
==================================217==================================S
------------------Argv------------------
mjHandMsg
---------------------------------------
----------------Content----------------
this.visible=false;
---------------------------------------
==========================================================================E
==================================218==================================S
------------------Argv------------------
msg,pl,tData
---------------------------------------
----------------Content----------------
_local0=getUIPlayer_thirteenGX(3);
if(_local0&&msg.players[_local0.info.uid]){
_local1=MjClient.data.sData.tData;
if((_local1.tState == TableState.waitJiazhu) || (_local1.tState == TableState.waitTanPai) || (_local1.tState == TableState.waitCard)){
this.visible=true;
ShowFourCard_thirteenGX(this,3,false);
} else {
}
this.visible=false;
} else {
}
this.visible=false;
---------------------------------------
==========================================================================E
==================================219==================================S
------------------Argv------------------
msg,off,pl,data
---------------------------------------
----------------Content----------------
_local0=3;
_local1=getUIPlayer_thirteenGX(_local0);
if(_local1&&(msg.uids.indexOf(_local1.info.uid) != -1)){
_local2=msg.data.players[_local1.info.uid];
if(_local2){
this.visible=true;
_local1.mjhand=_local2.Card[0].concat(_local2.Card[1]).concat(_local2.Card[2]);
ShowFourCard_thirteenGX(this,_local0,true);
}}
---------------------------------------
==========================================================================E
==================================220==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
---------------------------------------
==========================================================================E
==================================221==================================S
------------------Argv------------------
eD
---------------------------------------
----------------Content----------------
SetUserVisible_thirteenGX(this,3);
---------------------------------------
==========================================================================E
==================================222==================================S
------------------Argv------------------
eD
---------------------------------------
----------------Content----------------
SetUserVisible_thirteenGX(this,3);
---------------------------------------
==========================================================================E
==================================223==================================S
------------------Argv------------------
eD
---------------------------------------
----------------Content----------------
SetUserVisible_thirteenGX(this,3);
---------------------------------------
==========================================================================E
==================================224==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
InitUserCoinAndName_thirteenGX(this,3);
---------------------------------------
==========================================================================E
==================================225==================================S
------------------Argv------------------
msg,pl
---------------------------------------
----------------Content----------------
_local0=getUIPlayer_thirteenGX(3);
if(_local0&&(_local0.info.uid == msg.uid)){
_local0.onLine=msg.onLine;
setUserOffline(this,3);
}
---------------------------------------
==========================================================================E
==================================226==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
this.visible=false;
---------------------------------------
==========================================================================E
==================================227==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
GetReadyVisible(this,4);
---------------------------------------
==========================================================================E
==================================228==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
GetReadyVisible(this,4);
---------------------------------------
==========================================================================E
==================================229==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
GetReadyVisible(this,4);
---------------------------------------
==========================================================================E
==================================230==================================S
------------------Argv------------------
eD
---------------------------------------
----------------Content----------------
this.visible=false;
---------------------------------------
==========================================================================E
==================================231==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
GetReadyVisible(this,4);
---------------------------------------
==========================================================================E
==================================232==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
this.visible=false;
---------------------------------------
==========================================================================E
==================================233==================================S
------------------Argv------------------
msg
---------------------------------------
----------------Content----------------
if(getUIPlayer_thirteenGX(4)&&(getUIPlayer_thirteenGX(4).info.uid == msg.uid)){
this.visible=true;
}
---------------------------------------
==========================================================================E
==================================234==================================S
------------------Argv------------------
msg
---------------------------------------
----------------Content----------------
if(getUIPlayer_thirteenGX(4)&&(getUIPlayer_thirteenGX(4).info.uid == msg.uid)){
this.visible=false;
}
---------------------------------------
==========================================================================E
==================================235==================================S
------------------Argv------------------
msg,pl
---------------------------------------
----------------Content----------------
_local0=getUIPlayer_thirteenGX(4);
if(_local0&&_local0.trust){
this.visible=true;
} else {
}
this.visible=false;
---------------------------------------
==========================================================================E
==================================236==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
this.getParent().zIndex=500;
---------------------------------------
==========================================================================E
==================================237==================================S
------------------Argv------------------
msg
---------------------------------------
----------------Content----------------
showUserChat(this,4,msg);
---------------------------------------
==========================================================================E
==================================238==================================S
------------------Argv------------------
voicePath
---------------------------------------
----------------Content----------------
MjClient.data._tempMessage.msg=voicePath;
showUserChat(this,4,MjClient.data._tempMessage);
---------------------------------------
==========================================================================E
==================================239==================================S
------------------Argv------------------
btn
---------------------------------------
----------------Content----------------
showPlayerInfo(4,btn);
---------------------------------------
==========================================================================E
==================================240==================================S
------------------Argv------------------
d
---------------------------------------
----------------Content----------------
setWxHead_thirteenGX(this,d,4);
---------------------------------------
==========================================================================E
==================================241==================================S
------------------Argv------------------
eD
---------------------------------------
----------------Content----------------
---------------------------------------
==========================================================================E
==================================242==================================S
------------------Argv------------------
eD
---------------------------------------
----------------Content----------------
---------------------------------------
==========================================================================E
==================================243==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
---------------------------------------
==========================================================================E
==================================244==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
this.visible=false;
---------------------------------------
==========================================================================E
==================================245==================================S
------------------Argv------------------
mjHandMsg
---------------------------------------
----------------Content----------------
this.visible=false;
---------------------------------------
==========================================================================E
==================================246==================================S
------------------Argv------------------
msg,pl,tData
---------------------------------------
----------------Content----------------
_local0=getUIPlayer_thirteenGX(4);
if(_local0&&msg.players[_local0.info.uid]){
_local1=MjClient.data.sData.tData;
if((_local1.tState == TableState.waitJiazhu) || (_local1.tState == TableState.waitTanPai) || (_local1.tState == TableState.waitCard)){
this.visible=true;
ShowFourCard_thirteenGX(this,4,false);
} else {
}
this.visible=false;
} else {
}
this.visible=false;
---------------------------------------
==========================================================================E
==================================247==================================S
------------------Argv------------------
msg,off,pl,data
---------------------------------------
----------------Content----------------
_local0=4;
_local1=getUIPlayer_thirteenGX(_local0);
if(_local1&&(msg.uids.indexOf(_local1.info.uid) != -1)){
_local2=msg.data.players[_local1.info.uid];
if(_local2){
this.visible=true;
_local1.mjhand=_local2.Card[0].concat(_local2.Card[1]).concat(_local2.Card[2]);
ShowFourCard_thirteenGX(this,_local0,true);
}}
---------------------------------------
==========================================================================E
==================================248==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
---------------------------------------
==========================================================================E
==================================249==================================S
------------------Argv------------------
eD
---------------------------------------
----------------Content----------------
SetUserVisible_thirteenGX(this,4);
---------------------------------------
==========================================================================E
==================================250==================================S
------------------Argv------------------
eD
---------------------------------------
----------------Content----------------
SetUserVisible_thirteenGX(this,4);
---------------------------------------
==========================================================================E
==================================251==================================S
------------------Argv------------------
eD
---------------------------------------
----------------Content----------------
SetUserVisible_thirteenGX(this,4);
---------------------------------------
==========================================================================E
==================================252==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
InitUserCoinAndName_thirteenGX(this,4);
---------------------------------------
==========================================================================E
==================================253==================================S
------------------Argv------------------
msg,pl
---------------------------------------
----------------Content----------------
_local0=getUIPlayer_thirteenGX(4);
if(_local0&&(_local0.info.uid == msg.uid)){
_local0.onLine=msg.onLine;
setUserOffline(this,4);
}
---------------------------------------
==========================================================================E
==================================254==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
this.visible=false;
---------------------------------------
==========================================================================E
==================================255==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
GetReadyVisible(this,5);
---------------------------------------
==========================================================================E
==================================256==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
GetReadyVisible(this,5);
---------------------------------------
==========================================================================E
==================================257==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
GetReadyVisible(this,5);
---------------------------------------
==========================================================================E
==================================258==================================S
------------------Argv------------------
eD
---------------------------------------
----------------Content----------------
this.visible=false;
---------------------------------------
==========================================================================E
==================================259==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
GetReadyVisible(this,5);
---------------------------------------
==========================================================================E
==================================260==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
this.visible=false;
---------------------------------------
==========================================================================E
==================================261==================================S
------------------Argv------------------
msg
---------------------------------------
----------------Content----------------
if(getUIPlayer_thirteenGX(5)&&(getUIPlayer_thirteenGX(5).info.uid == msg.uid)){
this.visible=true;
}
---------------------------------------
==========================================================================E
==================================262==================================S
------------------Argv------------------
msg
---------------------------------------
----------------Content----------------
if(getUIPlayer_thirteenGX(5)&&(getUIPlayer_thirteenGX(5).info.uid == msg.uid)){
this.visible=false;
}
---------------------------------------
==========================================================================E
==================================263==================================S
------------------Argv------------------
msg,pl
---------------------------------------
----------------Content----------------
_local0=getUIPlayer_thirteenGX(5);
if(_local0&&_local0.trust){
this.visible=true;
} else {
}
this.visible=false;
---------------------------------------
==========================================================================E
==================================264==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
this.getParent().zIndex=500;
---------------------------------------
==========================================================================E
==================================265==================================S
------------------Argv------------------
msg
---------------------------------------
----------------Content----------------
showUserChat(this,1,msg);
---------------------------------------
==========================================================================E
==================================266==================================S
------------------Argv------------------
voicePath
---------------------------------------
----------------Content----------------
MjClient.data._tempMessage.msg=voicePath;
showUserChat(this,5,MjClient.data._tempMessage);
---------------------------------------
==========================================================================E
==================================267==================================S
------------------Argv------------------
btn
---------------------------------------
----------------Content----------------
showPlayerInfo(5,btn);
---------------------------------------
==========================================================================E
==================================268==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
---------------------------------------
==========================================================================E
==================================269==================================S
------------------Argv------------------
d
---------------------------------------
----------------Content----------------
setWxHead_thirteenGX(this,d,5);
---------------------------------------
==========================================================================E
==================================270==================================S
------------------Argv------------------
eD
---------------------------------------
----------------Content----------------
---------------------------------------
==========================================================================E
==================================271==================================S
------------------Argv------------------
eD
---------------------------------------
----------------Content----------------
---------------------------------------
==========================================================================E
==================================272==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
this.visible=false;
---------------------------------------
==========================================================================E
==================================273==================================S
------------------Argv------------------
mjHandMsg
---------------------------------------
----------------Content----------------
this.visible=false;
---------------------------------------
==========================================================================E
==================================274==================================S
------------------Argv------------------
msg,pl,tData
---------------------------------------
----------------Content----------------
_local0=getUIPlayer_thirteenGX(5);
if(_local0&&msg.players[_local0.info.uid]){
_local1=MjClient.data.sData.tData;
if((_local1.tState == TableState.waitJiazhu) || (_local1.tState == TableState.waitTanPai) || (_local1.tState == TableState.waitCard)){
this.visible=true;
ShowFourCard_thirteenGX(this,5,false);
} else {
}
this.visible=false;
} else {
}
this.visible=false;
---------------------------------------
==========================================================================E
==================================275==================================S
------------------Argv------------------
msg,off,pl,data
---------------------------------------
----------------Content----------------
_local0=5;
_local1=getUIPlayer_thirteenGX(_local0);
if(_local1&&(msg.uids.indexOf(_local1.info.uid) != -1)){
_local2=msg.data.players[_local1.info.uid];
if(_local2){
this.visible=true;
_local1.mjhand=_local2.Card[0].concat(_local2.Card[1]).concat(_local2.Card[2]);
ShowFourCard_thirteenGX(this,_local0,true);
}}
---------------------------------------
==========================================================================E
==================================276==================================S
------------------Argv------------------
eD
---------------------------------------
----------------Content----------------
SetUserVisible_thirteenGX(this,5);
---------------------------------------
==========================================================================E
==================================277==================================S
------------------Argv------------------
eD
---------------------------------------
----------------Content----------------
SetUserVisible_thirteenGX(this,5);
---------------------------------------
==========================================================================E
==================================278==================================S
------------------Argv------------------
eD
---------------------------------------
----------------Content----------------
SetUserVisible_thirteenGX(this,5);
---------------------------------------
==========================================================================E
==================================279==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
InitUserCoinAndName_thirteenGX(this,5);
---------------------------------------
==========================================================================E
==================================280==================================S
------------------Argv------------------
msg,pl
---------------------------------------
----------------Content----------------
_local0=getUIPlayer_thirteenGX(5);
if(_local0&&(_local0.info.uid == msg.uid)){
_local0.onLine=msg.onLine;
setUserOffline(this,5);
}
---------------------------------------
==========================================================================E
==================================281==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
this.visible=false;
---------------------------------------
==========================================================================E
==================================282==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
GetReadyVisible(this,6);
---------------------------------------
==========================================================================E
==================================283==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
GetReadyVisible(this,6);
---------------------------------------
==========================================================================E
==================================284==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
GetReadyVisible(this,6);
---------------------------------------
==========================================================================E
==================================285==================================S
------------------Argv------------------
eD
---------------------------------------
----------------Content----------------
this.visible=false;
---------------------------------------
==========================================================================E
==================================286==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
GetReadyVisible(this,6);
---------------------------------------
==========================================================================E
==================================287==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
this.visible=false;
---------------------------------------
==========================================================================E
==================================288==================================S
------------------Argv------------------
msg
---------------------------------------
----------------Content----------------
if(getUIPlayer_thirteenGX(6)&&(getUIPlayer_thirteenGX(6).info.uid == msg.uid)){
this.visible=true;
}
---------------------------------------
==========================================================================E
==================================289==================================S
------------------Argv------------------
msg
---------------------------------------
----------------Content----------------
if(getUIPlayer_thirteenGX(6)&&(getUIPlayer_thirteenGX(6).info.uid == msg.uid)){
this.visible=false;
}
---------------------------------------
==========================================================================E
==================================290==================================S
------------------Argv------------------
msg,pl
---------------------------------------
----------------Content----------------
_local0=getUIPlayer_thirteenGX(6);
if(_local0&&_local0.trust){
this.visible=true;
} else {
}
this.visible=false;
---------------------------------------
==========================================================================E
==================================291==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
this.getParent().zIndex=500;
---------------------------------------
==========================================================================E
==================================292==================================S
------------------Argv------------------
msg
---------------------------------------
----------------Content----------------
showUserChat(this,6,msg);
---------------------------------------
==========================================================================E
==================================293==================================S
------------------Argv------------------
voicePath
---------------------------------------
----------------Content----------------
MjClient.data._tempMessage.msg=voicePath;
showUserChat(this,6,MjClient.data._tempMessage);
---------------------------------------
==========================================================================E
==================================294==================================S
------------------Argv------------------
btn
---------------------------------------
----------------Content----------------
showPlayerInfo(6,btn);
---------------------------------------
==========================================================================E
==================================295==================================S
------------------Argv------------------
d
---------------------------------------
----------------Content----------------
setWxHead_thirteenGX(this,d,6);
---------------------------------------
==========================================================================E
==================================296==================================S
------------------Argv------------------
eD
---------------------------------------
----------------Content----------------
---------------------------------------
==========================================================================E
==================================297==================================S
------------------Argv------------------
eD
---------------------------------------
----------------Content----------------
---------------------------------------
==========================================================================E
==================================298==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
---------------------------------------
==========================================================================E
==================================299==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
this.visible=false;
---------------------------------------
==========================================================================E
==================================300==================================S
------------------Argv------------------
mjHandMsg
---------------------------------------
----------------Content----------------
this.visible=false;
---------------------------------------
==========================================================================E
==================================301==================================S
------------------Argv------------------
msg,pl,tData
---------------------------------------
----------------Content----------------
_local0=getUIPlayer_thirteenGX(6);
if(_local0&&msg.players[_local0.info.uid]){
_local1=MjClient.data.sData.tData;
if((_local1.tState == TableState.waitJiazhu) || (_local1.tState == TableState.waitTanPai) || (_local1.tState == TableState.waitCard)){
this.visible=true;
ShowFourCard_thirteenGX(this,6,false);
} else {
}
this.visible=false;
} else {
}
this.visible=false;
---------------------------------------
==========================================================================E
==================================302==================================S
------------------Argv------------------
msg,off,pl,data
---------------------------------------
----------------Content----------------
_local0=6;
_local1=getUIPlayer_thirteenGX(_local0);
if(_local1&&(msg.uids.indexOf(_local1.info.uid) != -1)){
_local2=msg.data.players[_local1.info.uid];
if(_local2){
this.visible=true;
_local1.mjhand=_local2.Card[0].concat(_local2.Card[1]).concat(_local2.Card[2]);
ShowFourCard_thirteenGX(this,_local0,true);
}}
---------------------------------------
==========================================================================E
==================================303==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
---------------------------------------
==========================================================================E
==================================304==================================S
------------------Argv------------------
eD
---------------------------------------
----------------Content----------------
SetUserVisible_thirteenGX(this,6);
---------------------------------------
==========================================================================E
==================================305==================================S
------------------Argv------------------
eD
---------------------------------------
----------------Content----------------
SetUserVisible_thirteenGX(this,6);
---------------------------------------
==========================================================================E
==================================306==================================S
------------------Argv------------------
eD
---------------------------------------
----------------Content----------------
SetUserVisible_thirteenGX(this,6);
---------------------------------------
==========================================================================E
==================================307==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
InitUserCoinAndName_thirteenGX(this,6);
---------------------------------------
==========================================================================E
==================================308==================================S
------------------Argv------------------
msg,pl
---------------------------------------
----------------Content----------------
_local0=getUIPlayer_thirteenGX(6);
if(_local0&&(_local0.info.uid == msg.uid)){
_local0.onLine=msg.onLine;
setUserOffline(this,6);
}
---------------------------------------
==========================================================================E
==================================309==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
this.visible=false;
---------------------------------------
==========================================================================E
==================================310==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
GetReadyVisible(this,7);
---------------------------------------
==========================================================================E
==================================311==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
GetReadyVisible(this,7);
---------------------------------------
==========================================================================E
==================================312==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
GetReadyVisible(this,7);
---------------------------------------
==========================================================================E
==================================313==================================S
------------------Argv------------------
eD
---------------------------------------
----------------Content----------------
this.visible=false;
---------------------------------------
==========================================================================E
==================================314==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
GetReadyVisible(this,7);
---------------------------------------
==========================================================================E
==================================315==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
this.visible=false;
---------------------------------------
==========================================================================E
==================================316==================================S
------------------Argv------------------
msg
---------------------------------------
----------------Content----------------
if(getUIPlayer_thirteenGX(7)&&(getUIPlayer_thirteenGX(7).info.uid == msg.uid)){
this.visible=true;
}
---------------------------------------
==========================================================================E
==================================317==================================S
------------------Argv------------------
msg
---------------------------------------
----------------Content----------------
if(getUIPlayer_thirteenGX(7)&&(getUIPlayer_thirteenGX(7).info.uid == msg.uid)){
this.visible=false;
}
---------------------------------------
==========================================================================E
==================================318==================================S
------------------Argv------------------
msg,pl
---------------------------------------
----------------Content----------------
_local0=getUIPlayer_thirteenGX(7);
if(_local0&&_local0.trust){
this.visible=true;
} else {
}
this.visible=false;
---------------------------------------
==========================================================================E
==================================319==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
this.getParent().zIndex=500;
---------------------------------------
==========================================================================E
==================================320==================================S
------------------Argv------------------
msg
---------------------------------------
----------------Content----------------
showUserChat(this,6,msg);
---------------------------------------
==========================================================================E
==================================321==================================S
------------------Argv------------------
voicePath
---------------------------------------
----------------Content----------------
MjClient.data._tempMessage.msg=voicePath;
showUserChat(this,6,MjClient.data._tempMessage);
---------------------------------------
==========================================================================E
==================================322==================================S
------------------Argv------------------
btn
---------------------------------------
----------------Content----------------
showPlayerInfo(7,btn);
---------------------------------------
==========================================================================E
==================================323==================================S
------------------Argv------------------
d
---------------------------------------
----------------Content----------------
setWxHead_thirteenGX(this,d,7);
---------------------------------------
==========================================================================E
==================================324==================================S
------------------Argv------------------
eD
---------------------------------------
----------------Content----------------
---------------------------------------
==========================================================================E
==================================325==================================S
------------------Argv------------------
eD
---------------------------------------
----------------Content----------------
---------------------------------------
==========================================================================E
==================================326==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
---------------------------------------
==========================================================================E
==================================327==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
this.visible=false;
---------------------------------------
==========================================================================E
==================================328==================================S
------------------Argv------------------
mjHandMsg
---------------------------------------
----------------Content----------------
this.visible=false;
---------------------------------------
==========================================================================E
==================================329==================================S
------------------Argv------------------
msg,pl,tData
---------------------------------------
----------------Content----------------
_local0=getUIPlayer_thirteenGX(7);
if(_local0&&msg.players[_local0.info.uid]){
_local1=MjClient.data.sData.tData;
if((_local1.tState == TableState.waitJiazhu) || (_local1.tState == TableState.waitTanPai) || (_local1.tState == TableState.waitCard)){
this.visible=true;
ShowFourCard_thirteenGX(this,7,false);
} else {
}
this.visible=false;
} else {
}
this.visible=false;
---------------------------------------
==========================================================================E
==================================330==================================S
------------------Argv------------------
msg,off,pl,data
---------------------------------------
----------------Content----------------
_local0=7;
_local1=getUIPlayer_thirteenGX(_local0);
if(_local1&&(msg.uids.indexOf(_local1.info.uid) != -1)){
_local2=msg.data.players[_local1.info.uid];
if(_local2){
this.visible=true;
_local1.mjhand=_local2.Card[0].concat(_local2.Card[1]).concat(_local2.Card[2]);
ShowFourCard_thirteenGX(this,_local0,true);
}}
---------------------------------------
==========================================================================E
==================================331==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
---------------------------------------
==========================================================================E
==================================332==================================S
------------------Argv------------------
eD
---------------------------------------
----------------Content----------------
SetUserVisible_thirteenGX(this,7);
---------------------------------------
==========================================================================E
==================================333==================================S
------------------Argv------------------
eD
---------------------------------------
----------------Content----------------
SetUserVisible_thirteenGX(this,7);
---------------------------------------
==========================================================================E
==================================334==================================S
------------------Argv------------------
eD
---------------------------------------
----------------Content----------------
SetUserVisible_thirteenGX(this,7);
---------------------------------------
==========================================================================E
==================================335==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
InitUserCoinAndName_thirteenGX(this,7);
---------------------------------------
==========================================================================E
==================================336==================================S
------------------Argv------------------
msg,pl
---------------------------------------
----------------Content----------------
_local0=getUIPlayer_thirteenGX(7);
if(_local0&&(_local0.info.uid == msg.uid)){
_local0.onLine=msg.onLine;
setUserOffline(this,7);
}
---------------------------------------
==========================================================================E
==================================337==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
this.visible=false;
---------------------------------------
==========================================================================E
==================================338==================================S
------------------Argv------------------
chatlayer
---------------------------------------
----------------Content----------------
_local0=new ChatLayer();
MjClient.Scene.addChild(_local0);
---------------------------------------
==========================================================================E
==================================339==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
this.visible=false;
initVoiceData();
cc.eventManager.addListener(getTouchListener(),this);
if(MjClient.isShenhe){
this.visible=false;
}
---------------------------------------
==========================================================================E
==================================340==================================S
------------------Argv------------------
btn,eT
---------------------------------------
----------------Content----------------
if((eT == 0)){
startRecord();
} else if((eT == 2)){
endRecord();
} else if((eT == 3)){
cancelRecord();
}
---------------------------------------
==========================================================================E
==================================341==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
MjClient.native.HelloOC("cancelRecord !!!");
---------------------------------------
==========================================================================E
==================================342==================================S
------------------Argv------------------
filePath
---------------------------------------
----------------Content----------------
if(filePath){
MjClient.native.HelloOC("upload voice file");
MjClient.native.UploadFile(filePath,MjClient.remoteCfg.voiceUrl,"sendVoice");
} else {
}
MjClient.native.HelloOC("No voice file update");
---------------------------------------
==========================================================================E
==================================343==================================S
------------------Argv------------------
fullFilePath,getFileName,extensionName,fileName
---------------------------------------
----------------Content----------------
if(!(fullFilePath)){
console.log("sendVoice No fileName");
return undefined;
}
_local0=/[^\/]+$/;
_local1=_local0.exec(fullFilePath);
_local2=_local1[(_local1.length - 1)];
console.log(("sfileName is:" + _local2));
MjClient.gamenet.request("pkroom.handler.tableMsg",{cmd:"downAndPlayVoice",uid:SelfUidSSS(),type:3,msg:_local2,num:MjClient.data._JiaheTempTime});
MjClient.native.HelloOC("download file");
---------------------------------------
==========================================================================E
==================================344==================================S
------------------Argv------------------
msg
---------------------------------------
----------------Content----------------
MjClient.native.HelloOC("downloadPlayVoice ok");
MjClient.data._tempMessage=msg;
MjClient.native.HelloOC(("mas is" + JSON.stringify(msg)));
downAndPlayVoice(msg.uid,msg.msg);
---------------------------------------
==========================================================================E
==================================345==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
this.visible=false;
this.zIndex=500;
---------------------------------------
==========================================================================E
==================================346==================================S
------------------Argv------------------
btn,eT
---------------------------------------
----------------Content----------------
if((eT == 2)){
MjClient.gamenet.request("pkroom.handler.tableMsg",{cmd:"cancelTrust"},function () { __FUNC_347__ });
}
---------------------------------------
==========================================================================E
==================================347==================================S
------------------Argv------------------
rtn
---------------------------------------
----------------Content----------------
_aliased1746.getParent().setVisible(false);
---------------------------------------
==========================================================================E
==================================348==================================S
------------------Argv------------------
msg
---------------------------------------
----------------Content----------------
cc.log(("wxd........beTrust......." + JSON.stringify(msg)));
if(getUIPlayer_thirteenGX(0)&&(getUIPlayer_thirteenGX(0).info.uid == msg.uid)){
if(MjClient.movingCard){
MjClient.movingCard.setTouchEnabled(false);
MjClient.movingCard.setScale(cardBeginScale);
MjClient.movingCard.setTouchEnabled(true);
}
this.visible=true;
}
---------------------------------------
==========================================================================E
==================================349==================================S
------------------Argv------------------
msg
---------------------------------------
----------------Content----------------
if(getUIPlayer_thirteenGX(0)&&(getUIPlayer_thirteenGX(0).info.uid == msg.uid)){
this.visible=false;
}
---------------------------------------
==========================================================================E
==================================350==================================S
------------------Argv------------------
msg,pl
---------------------------------------
----------------Content----------------
_local0=getUIPlayer_thirteenGX(0);
if(_local0&&_local0.trust){
this.visible=true;
} else {
}
this.visible=false;
---------------------------------------
==========================================================================E
==================================351==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
this.visible=isLookOnSSS();
---------------------------------------
==========================================================================E
==================================352==================================S
------------------Argv------------------
btn,eT
---------------------------------------
----------------Content----------------
if((eT == 2)){
MjClient.leaveGame();
playEffectInPlay_thirteenGX("sound-button");
}
---------------------------------------
==========================================================================E
==================================353==================================S
------------------Argv------------------
btn,eT,tbParams,clubInfoTable
---------------------------------------
----------------Content----------------
if((eT == 2)){
_local0={};
_local0.tableid=MjClient.data.sData.tData.tableid;
_local0.gameType=MjClient.gameType;
_local1=getClubInfoInTable();
if(_local1){
_local0.clubId=_local1.clubId;
}
_local0.isLookOn=false;
MjClient.leaveGame(function () { __FUNC_354__ },_local0);
}
---------------------------------------
==========================================================================E
==================================354==================================S
------------------Argv------------------
params
---------------------------------------
----------------Content----------------
print(("" + JSON.stringify(params)));
MjClient.joinGame(params.tableid,null,false,params.gameType,false,params);
---------------------------------------
==========================================================================E
==================================355==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
this.visible=isLookOnSSS();
---------------------------------------
==========================================================================E
==================================356==================================S
------------------Argv------------------
playui,tData
---------------------------------------
----------------Content----------------
this._super();
_local0=ccs.load("Play_thirteenGX.json");
MjClient.playui=this;
MjClient.playnode=_local0.node;
this._Node_player1=_local0.node.getChildByName("Node_player1");
this._Node_player2=_local0.node.getChildByName("Node_player2");
this._Node_player3=_local0.node.getChildByName("Node_player3");
this._Node_player4=_local0.node.getChildByName("Node_player4");
this._Node_player5=_local0.node.getChildByName("Node_player5");
this._Node_player6=_local0.node.getChildByName("Node_player6");
this._Node_player7=_local0.node.getChildByName("Node_player7");
this._Node_player8=_local0.node.getChildByName("Node_player8");
MjClient.playui._AniNode=_local0.node.getChildByName("eat");
MjClient.playui._WaitNode=_local0.node.getChildByName("wait");
MjClient.playui._BtnReady=_local0.node.getChildByName("BtnReady");
MjClient.playui._BtnXiPai=_local0.node.getChildByName("BtnXiPai");
MjClient.playui._Image_fapai=_local0.node.getChildByName("Image_fapai");
MjClient.playui._Image_banker=_local0.node.getChildByName("Image_banker");
MjClient.playui._imgPoint=_local0.node.getChildByName("Image_point");
MjClient.playui._downNode=this._Node_player1;
BindUiAndLogic(_local0.node,this.jsBind);
this.addChild(_local0.node);
_local1=MjClient.data.sData.tData;
MjClient.SSS_GX_MaxPlayerNum=_local1.areaSelectMode.maxPlayer;
this._Node_player1.setVisible(true);
this._Node_player2.setVisible(true);
if((_local1.areaSelectMode.maxPlayer == 2)){
this._Node_player3.setVisible(false);
this._Node_player4.setVisible(false);
this._Node_player5.setVisible(false);
this._Node_player6.setVisible(false);
this._Node_player7.setVisible(false);
this._Node_player8.setVisible(false);
setWgtLayout(this._Node_player1,[0.2031,0.3153],[0.5,0.161],[0,0]);
setWgtLayout(this._Node_player2,[0.2031,0.3153],[0.44,0.85],[0,0]);
this._Node_player2.getChildByName("head").setPosition(cc.p(39,145));
this._Node_player2.getChildByName("panel_Cards").setPosition(cc.p(189,90.55));
} else if((_local1.areaSelectMode.maxPlayer == 4)){
this._Node_player5.setVisible(false);
this._Node_player6.setVisible(false);
this._Node_player7.setVisible(false);
this._Node_player8.setVisible(false);
setWgtLayout(this._Node_player1,[0.2031,0.3153],[0.5,0.161],[0,0]);
setWgtLayout(this._Node_player2,[0.2031,0.3153],[0.9,0.475],[0,0]);
setWgtLayout(this._Node_player3,[0.2031,0.3153],[0.44,0.85],[0,0]);
setWgtLayout(this._Node_player4,[0.2031,0.3153],[0.1,0.475],[0,0]);
this._Node_player3.getChildByName("head").setPosition(cc.p(39,145));
this._Node_player3.getChildByName("panel_Cards").setPosition(cc.p(189,90.55));
this._Node_player4.getChildByName("head").setPosition(cc.p(39,145));
this._Node_player4.getChildByName("panel_Cards").setPosition(cc.p(189,90.55));
} else if((_local1.areaSelectMode.maxPlayer == 6)){
this._Node_player7.setVisible(false);
this._Node_player8.setVisible(false);
setWgtLayout(this._Node_player1,[0.2031,0.3153],[0.5,0.161],[0,0]);
setWgtLayout(this._Node_player2,[0.2031,0.3153],[0.9,0.475],[0,0]);
setWgtLayout(this._Node_player3,[0.2031,0.3153],[0.85,0.8],[0,0]);
setWgtLayout(this._Node_player4,[0.2031,0.3153],[0.44,0.85],[0,0]);
setWgtLayout(this._Node_player5,[0.2031,0.3153],[0.15,0.8],[0,0]);
setWgtLayout(this._Node_player6,[0.2031,0.3153],[0.1,0.475],[0,0]);
this._Node_player4.getChildByName("head").setPosition(cc.p(39,145));
this._Node_player4.getChildByName("panel_Cards").setPosition(cc.p(189,90.55));
}
addClubYaoqingBtn(1);
playMusic_thirteenGX("music_game");
MjClient.lastMJTick=Date.now();
this.runAction(cc.repeatForever(cc.sequence(cc.callFunc(function () { __FUNC_357__ }),cc.delayTime(7))));
this.shuffleList=[];
this.addRestCardLayer();
return true;
---------------------------------------
==========================================================================E
==================================357==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
if(MjClient.game_on_show){
MjClient.tickGame(0);
}
---------------------------------------
==========================================================================E
==================================358==================================S
------------------Argv------------------
uid
---------------------------------------
----------------Content----------------
if(this.isPlayShuffle||(this.shuffleList.length <= 0)){
return undefined;
}
this.isPlayShuffle=true;
if(!(this.shuffleNode)){
this.shuffleNode=new ShuffleEffectLayer_Poker();
this.jsBind._node.addChild(this.shuffleNode,499);
}
this.shuffleNode.visible=true;
_local0=this.shuffleList[0];
this.shuffleList.splice(0,1);
this.shuffleNode.playEffect(_local0);
this.scheduleOnce(function () { __FUNC_359__ },1.6);
---------------------------------------
==========================================================================E
==================================359==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
this.isPlayShuffle=false;
if(this.shuffleNode){
this.shuffleNode.visible=false;
}
this.playShuffleEffect();
---------------------------------------
==========================================================================E
==================================360==================================S
------------------Argv------------------
off,index,cards,score,sort,time,maxPlayerNum,winone,specialType,isGunArr,callback,that
---------------------------------------
----------------Content----------------
time = 1;
_local0=this;
MjClient.playui.runAction(cc.sequence(time||cc.delayTime(time),cc.callFunc(function () { __FUNC_361__ })));
---------------------------------------
==========================================================================E
==================================361==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
MjClient.playui.showHandCard(_aliased1746,_aliased6212,_aliased7136,undefined,undefined,undefined);
if((undefined == (undefined - 1))&&(_aliased6212 == 3)){
if(undefined){
(undefined)();
}}
---------------------------------------
==========================================================================E
==================================362==================================S
------------------Argv------------------
eD,tData,maxPlayerNum,i,playdata,j
---------------------------------------
----------------Content----------------
_local0=MjClient.data.sData.tData;
_local0.uids=eD.uids;
MjClient.playui.gunRelationArr=[];
_local1=eD.uids.length;
_local2=0;
break;
while((_local2 < eD.uids.length)){
if(eD.uids[_local2]){
_local3=eD.data.players[eD.uids[_local2]];
_local4=0;
while((_local4 < _local3.isGun.length)){
if((_local4 != _local2)&&(_local3.isGun[_local4] == 1)){
MjClient.playui.gunRelationArr.push([eD.uids[_local2],eD.uids[_local4]]);
}
_local4=(_local4 + 1);
_local4=0;
}
}
_local2=(_local2 + 1);
_local2=0;
}
print(("" + JSON.stringify(MjClient.playui.gunRelationArr)));
MjClient.playui.playGunEffect();
---------------------------------------
==========================================================================E
==================================363==================================S
------------------Argv------------------
relation
---------------------------------------
----------------Content----------------
if((MjClient.playui.gunRelationArr.length <= 0)){
return undefined;
}
_local0=MjClient.playui.gunRelationArr[0];
MjClient.playui.gunRelationArr.splice(0,1);
playGunAni_thirteenGX(_local0[0],_local0[1],function () { __FUNC_364__ });
---------------------------------------
==========================================================================E
==================================364==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
MjClient.playui.playGunEffect();
---------------------------------------
==========================================================================E
==================================365==================================S
------------------Argv------------------
off,index,cards,score,winone,specialType,_node,nodeStand,strList,cardCountList,i,ccCard,szCardName,maIcon,nInitScale,txt,txt_zf,str,newCards,cardType,szAniName,node,szSpecialName,sprite
---------------------------------------
----------------Content----------------
_local0=getNode_thirteenGX(off);
_local1=_local0.getChildByName("panel_Cards");
_local1.visible=true;
_local2=["","td","zd","wd"];
_local3=[3,5,5];
_local4=0;
while((_local4 < _local3[(index - 1)])){
_local5=_local1.getChildByName((("hcard" + index) + (_local4 + 1)));
_local6=getCardName_thirteenGX(cards[_local4]);
_local5.loadTexture(_local6);
_local5.removeAllChildren();
_local5.setColor(cc.color(255,255,255));
print(((MjClient.SSS_GX_MaPai + "") + cards[_local4]));
if((Number(cards[_local4]) == Number(MjClient.SSS_GX_MaPai))){
print("");
_local7=new cc.Sprite("poker/shisanzhang/ma_image.png");
_local5.addChild(_local7);
_local5.setColor(cc.color(255,255,63));
_local7.setPosition(cc.p(24.65,159.74));
}
_local8=0.6;
if((off == 0)){
_local8=0.8;
}
_local5.runAction(cc.sequence(cc.scaleTo(0.3,1),cc.scaleTo(0.3,_local8)));
_local4=(_local4 + 1);
_local4=0;
}
_local9=_local1.getChildByName(("num_" + _local2[index]));
_local9.visible=true;
_local9.setString(TxtScoreFormat_thirteenGX(score));
if((index == 3)){
_local10=_local1.getChildByName("num_zf");
_local10.visible=true;
_local11="";
if((winone == 0)){
_local11="0";
} else if((winone > 0)){
_local11=("+" + winone)||"";
} else {
}
_local11=winone||"";
_local10.setString(_local11);
}
_local12=exchangeTo16Data_thirteenGX(cards);
_local13=MjClient.poker_thirteenGX.CheckCardType(_local12);
_local14="";
if((_local13 == 0)){
playEffectInPlay_thirteenGX("duizi");
_local14="3duizi";
} else if((_local13 == 1)){
playEffectInPlay_thirteenGX("liangdui");
_local14="2liangdui";
} else if((_local13 == 2)){
playEffectInPlay_thirteenGX("santiao");
_local14="4santiao";
} else if((_local13 == 3)){
playEffectInPlay_thirteenGX("shunzi");
_local14="5shunzi";
} else if((_local13 == 4)){
playEffectInPlay_thirteenGX("tonghua");
_local14="7tonghua";
} else if((_local13 == 5)){
playEffectInPlay_thirteenGX("tonghua");
_local14="7tonghua";
} else if((_local13 == 6)){
playEffectInPlay_thirteenGX("tonghua");
_local14="7tonghua";
} else if((_local13 == 7)){
playEffectInPlay_thirteenGX("hulu");
_local14="6hulu";
} else if((_local13 == 8)){
playEffectInPlay_thirteenGX("tiezhi");
_local14="8tiezhi";
} else if((_local13 == 9)){
playEffectInPlay_thirteenGX("tonghuashun");
_local14="9tonghuashun";
} else if((_local13 == 10)){
playEffectInPlay_thirteenGX("wutong");
_local14="10wutong";
} else if((_local13 == -1)){
playEffectInPlay_thirteenGX("wulong");
_local14="1wulong";
}
_local15=_local1.getChildByName(("img_" + _local2[index]));
_local15.visible=true;
doPaiXing_thirteenGX(_local15,_local14);
if((index == 3)){
_local15.removeChildByName("PaiXingSpecial");
}
if((index == 3)&&(specialType > 0)){
_local16=(("poker/shisanzhang/words/cardType_" + specialType) + ".png");
_local17=new cc.Sprite(_local16);
_local17.setName("PaiXingSpecial");
_local17.setScale(2);
_local17.setPosition((_local15.getContentSize().width / 2),((_local15.getContentSize().height / 2) + 50));
_local15.addChild(_local17);
}
---------------------------------------
==========================================================================E
==================================366==================================S
------------------Argv------------------
parent,cardvalue,nodeCard,card
---------------------------------------
----------------Content----------------
_local0=new PokerCardNode_DoudizhuSpecial(0,1);
_local1=_local0.createOneCardNode(cardvalue,"pkhand");
_local1.setScale(0.65);
parent.addChild(_local1);
_local1.isSelect=false;
_local1.value=cardvalue;
_local1.setPosition(cc.p((parent.getContentSize().width / 2),(parent.getContentSize().height / 2)));
return _local1;
---------------------------------------
==========================================================================E
==================================367==================================S
------------------Argv------------------
tData,str,strPayWay,str5
---------------------------------------
----------------Content----------------
_local0=MjClient.data.sData.tData;
_local1="";
switch(_local0.areaSelectMode.zhuangType){
case 0:
_local1="";
break;
case 1:
_local1="";
break;
default:
}
switch(_local0.areaSelectMode.fengZhu){
case 0:
_local1=(_local1 + "");
break;
case 1:
_local1=(_local1 + "");
break;
case 2:
_local1=(_local1 + "");
break;
case 3:
_local1=(_local1 + "");
break;
case 4:
_local1=(_local1 + "");
break;
case 5:
_local1=(_local1 + "");
break;
default:
}
if(_local0.areaSelectMode.antiLastJoin){
_local1=(_local1 + "");
}
if(_local0.areaSelectMode.antiCenterJoin){
_local1=(_local1 + "");
}
if(_local0.areaSelectMode.antiCuoPai){
_local1=(_local1 + "");
}
if(_local0.areaSelectMode.antiDissRoom){
_local1=(_local1 + "");
}
switch(_local0.areaSelectMode.startmode){
case 0:
_local1=(_local1 + "");
break;
case 1:
_local1=(_local1 + "");
break;
case 2:
_local1=(_local1 + "");
break;
default:
}
(_local0.areaSelectMode.trustTime > 0)?_local1=(_local1 + (_local0.areaSelectMode.trustTime + "")):_local1=(_local1 + "");
if((_local0.areaSelectMode.trustTime > 0)){
switch(_local0.areaSelectMode.trustType){
case 0:
_local1=(_local1 + "");
break;
case 1:
_local1=(_local1 + "");
break;
case 2:
_local1=(_local1 + "");
} else {
default:
}}
_local1=(_local1 + (("剩余" + (_local0.roundNum - 1)) + "局"));
_local2="";
switch(_local0.areaSelectMode.payWay){
case 0:
_local2="";
break;
case 1:
_local2="";
break;
case 2:
_local2="";
break;
default:
}
_local3=_local2;
return (_local1 + _local3);
---------------------------------------
==========================================================================E
==================================368==================================S
------------------Argv------------------
StartOff,EndOff,kind,sNode,eNode,StarNode,EndNode,startPos,endPos,_AniNode,distance,costTime,midX,midY,move,firstFrame,sound,playSoundFunc,frames,prefix,fc,i,name,f,animate
---------------------------------------
----------------Content----------------
_local0=getNode_thirteenGX(StartOff);
_local1=getNode_thirteenGX(EndOff);
if((_local0 == null)||(_local1 == null)){
return undefined;
}
_local2=_local0.getChildByName("head")||_local0.getChildByName("layout_head");
_local3=_local1.getChildByName("head")||_local1.getChildByName("layout_head");
_local4=_local2.convertToWorldSpace(_local2.getAnchorPointInPoints());
_local5=_local3.convertToWorldSpace(_local3.getAnchorPointInPoints());
_local6=MjClient.playui._AniNode;
_local7=cc.pDistance(_local4,_local5);
_local8=(_local7 / 600);
if((_local8 > 1)){
_local8=1;
} else if((_local8 < 0.5)){
_local8=0.5;
}
_local9=(((_local5.x - _local4.x) / 2) + _local4.x);
if((Math.abs((_local5.x - _local4.x)) < 10)){
_local9=(_local9 + (_local7 / 5));
}
_local10=Math.max(_local4.y,_local5.y);
if((Math.abs((_local5.y - _local4.y)) < 10)){
_local10=(_local10 + (_local7 / 5));
}
_local11=cc.bezierTo(_local8,[_local4,cc.p(_local9,_local10),_local5]);
switch(kind){
case 2:
_local11=cc.spawn(_local11,cc.rotateBy(_local8,720));
break;
case 6:
_local11=cc.spawn(_local11,cc.rotateBy(_local8,360));
break;
default:
}
cc.spriteFrameCache.addSpriteFrames("playing/other/emj.plist","playing/other/emj.png");
_local12=null;
_aliased1462="";
_local14=cc.callFunc(function () { __FUNC_369__ });
switch(kind){
case 0:
_aliased7806="ie_flower";
_local12=new cc.Sprite("playing/other/info_n_send_0.png");
_local15=[];
_local16="info_n_send_0_";
_local17=cc.spriteFrameCache;
_local18=1;
while((_local18 < 15)){
_local19=((_local16 + _local18) + ".png");
_local20=_local17.getSpriteFrame(_local19);
if(_local20){
_local15.push(_local20);
}
_local18=(_local18 + 1);
_local18=1;
}
_local21=cc.animate(new cc.Animation(_local15,0.08,1));
_local12.runAction(cc.sequence(_local11,_local14,_local21,cc.removeSelf()));
break;
case 1:
_aliased9101="ie_diamond";
_local12=new cc.Sprite("#info_n_send_1_0.png");
_local15=[];
_local16=(("info_n_send_" + kind) + "_");
_local17=cc.spriteFrameCache;
_local18=1;
while((_local18 < 15)){
_local19=((_local16 + _local18) + ".png");
_local20=_local17.getSpriteFrame(_local19);
if(_local20){
_local15.push(_local20);
}
_local18=(_local18 + 1);
_local18=1;
}
_local21=cc.animate(new cc.Animation(_local15,0.1,1));
_local12.runAction(cc.sequence(_local11,cc.delayTime(0.1),_local14,_local21,cc.removeSelf()));
break;
case 2:
_aliased8041="ie_egg";
_local12=new cc.Sprite("#info_n_send_2_0.png");
_local15=[];
_local16="info_n_send_2_";
_local17=cc.spriteFrameCache;
_local18=1;
while((_local18 < 10)){
_local19=((_local16 + _local18) + ".png");
_local20=_local17.getSpriteFrame(_local19);
if(_local20){
_local15.push(_local20);
}
_local18=(_local18 + 1);
_local18=1;
}
_local21=cc.animate(new cc.Animation(_local15,0.08,1));
_local12.runAction(cc.sequence(_local11,_local14,_local21,cc.removeSelf()));
break;
case 3:
_aliased7871="ie_boom";
_local12=new cc.Sprite("#info_n_send_3_0.png");
_local15=[];
_local16=(("info_n_send_" + kind) + "_");
_local17=cc.spriteFrameCache;
_local18=1;
while((_local18 < 15)){
_local19=((_local16 + _local18) + ".png");
_local20=_local17.getSpriteFrame(_local19);
if(_local20){
_local15.push(_local20);
}
_local18=(_local18 + 1);
_local18=1;
}
_local21=cc.animate(new cc.Animation(_local15,0.08,1));
_local12.runAction(cc.sequence(_local11,cc.delayTime(0.1),_local14,_local21,cc.removeSelf()));
break;
case 4:
_aliased6574="ie_kiss";
_local12=new cc.Sprite("#info_n_send_4_0.png");
_local15=[];
_local16=(("info_n_send_" + kind) + "_");
_local17=cc.spriteFrameCache;
_local18=1;
while((_local18 < 15)){
_local19=((_local16 + _local18) + ".png");
_local20=_local17.getSpriteFrame(_local19);
if(_local20){
_local15.push(_local20);
}
_local18=(_local18 + 1);
_local18=1;
}
_local21=cc.animate(new cc.Animation(_local15,0.12,1));
_local12.runAction(cc.sequence(_local11,cc.delayTime(0.1),_local14,_local21,cc.removeSelf()));
break;
case 5:
_aliased5494="ie_cheer";
_local12=new cc.Sprite("#info_n_send_5_0.png");
_local15=[];
_local16=(("info_n_send_" + kind) + "_");
_local17=cc.spriteFrameCache;
_local18=1;
while((_local18 < 15)){
_local19=((_local16 + _local18) + ".png");
_local20=_local17.getSpriteFrame(_local19);
if(_local20){
_local15.push(_local20);
}
_local18=(_local18 + 1);
_local18=1;
}
_local21=cc.animate(new cc.Animation(_local15,0.12,1));
_local12.runAction(cc.sequence(_local11,cc.delayTime(0.1),_local14,_local21,cc.removeSelf()));
break;
case 6:
_aliased7553="ie_tomato";
_local12=new cc.Sprite("#info_n_send_6_0.png");
_local15=[];
_local16=(("info_n_send_" + kind) + "_");
_local17=cc.spriteFrameCache;
_local18=1;
while((_local18 < 15)){
_local19=((_local16 + _local18) + ".png");
_local20=_local17.getSpriteFrame(_local19);
if(_local20){
_local15.push(_local20);
}
_local18=(_local18 + 1);
_local18=1;
}
_local21=cc.animate(new cc.Animation(_local15,0.08,1));
_local12.runAction(cc.sequence(_local11,_local14,_local21,cc.removeSelf()));
break;
default:
}
_local12.setPosition(_local4);
_local12.setScale((MjClient.size.height / 800));
_local6.addChild(_local12,10000);
---------------------------------------
==========================================================================E
==================================369==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
playEffect(_aliased7553);
---------------------------------------
==========================================================================E
==================================370==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
this.getCardLayer=new thirteenGX_getCard();
this.addChild(this.getCardLayer);
---------------------------------------
==========================================================================E
