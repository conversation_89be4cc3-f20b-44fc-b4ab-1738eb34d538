==================================0==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
var GameOver_thirteenGX;
GameOver_thirteenGX = cc.Layer.extend({ctor:function () { __FUNC_1__ }});
GameOver_thirteenGX.prototype.getGameInfoText=function () { __FUNC_2__ };
GameOver_thirteenGX.prototype.onShareButtonClick=function () { __FUNC_3__ };
GameOver_thirteenGX.prototype.onShareCallback=function () { __FUNC_4__ };
GameOver_thirteenGX.prototype.onCaptureScreen=function () { __FUNC_5__ };
GameOver_thirteenGX.prototype.onLoadClubAvatar=function () { __FUNC_6__ };
GameOver_thirteenGX.prototype.onCloseButtonClick=function () { __FUNC_7__ };
GameOver_thirteenGX.prototype.onLeaveGameCallback=function () { __FUNC_8__ };
GameOver_thirteenGX.prototype.onReplayButtonClick=function () { __FUNC_9__ };
GameOver_thirteenGX.prototype.onJoinVipTable=function () { __FUNC_10__ };
GameOver_thirteenGX.prototype.SetGameOverLayer_SG=function () { __FUNC_11__ };
GameOver_thirteenGX.prototype.setPlayerNameStyle=function () { __FUNC_12__ };
GameOver_thirteenGX.prototype.getPlayerName=function () { __FUNC_13__ };
GameOver_thirteenGX.prototype.setPlayerIdStyle=function () { __FUNC_14__ };
GameOver_thirteenGX.prototype.getPlayerId=function () { __FUNC_15__ };
---------------------------------------
==========================================================================E
==================================1==================================S
------------------Argv------------------
_jsonPath,endallui,clubInfoTable,_block,_back,_titleLose,_titlePingju,_titleWin,sData,tData,uid,pi,_share,_difenText,image_title,clubNode,clubName,_nameStr,_ruleName,_infoMation,_infoText,_time,_close1,_btnReplay,i,scrollView,player
---------------------------------------
----------------Content----------------
_local20=function () { __FUNC_2__ };
this._super();
_local0="endAll_thirteenGX.json";
_local1=ccs.load(_local0);
this.addChild(_local1.node);
MjClient.endallui=this;
_local2=getClubInfoInTable();
_local3=_local1.node.getChildByName("block");
setWgtLayout(_local3,[1,1],[0.5,0.5],[0,0],true);
_aliased5568=_local1.node.getChildByName("back");
setWgtLayout(_aliased5568,[1,1],[0.5,0.5],[0,0]);
_local5=_aliased5568.getChildByName("losetitle");
_local6=_aliased5568.getChildByName("pingju");
_local7=_aliased5568.getChildByName("wintitle");
_local5.visible=false;
_local6.visible=false;
_local7.visible=false;
_local8=MjClient.data.sData;
_aliased5983=_local8.tData;
while(_local8.players has _iternext){
_local10=_iternext;
_local11=_local8.players[_local10];
if(_local11&&(_local10 == SelfUid())){
if((_local11.winall > 0)){
_local7.visible=true;
} else if((_local11.winall < 0)){
_local5.visible=true;
} else {
}
_local6.visible=true;
break;
}
}
_aliased8791=_aliased5568.getChildByName("btnShare");
_aliased3489=MjClient.data.sData.tData;
(_local2 != null)?_aliased8791.visible=false:_aliased8791.visible=true;
_aliased8791.addTouchEventListener(function () { __FUNC_3__ },this);
_local8=MjClient.data.sData;
_aliased3425=_local8.tData;
_local13=_aliased5568.getChildByName("Text_difen");
if(_local13){
_local13.setString((" X " + _aliased3425.areaSelectMode.tonghua_difen));
}
_local14=_aliased5568.getChildByName("Image_title");
_aliased9329=_aliased5568.getChildByName("Image_club");
if(_local2){
_aliased9329.visible=true;
_local16=_aliased9329.getChildByName("Text_name");
_local16.ignoreContentAdaptWithSize(true);
_local16.setString(("" + unescape(_local2.clubTitle)));
_local17=unescape(_local2.ruleName);
_local18=_local16.clone();
_local18.ignoreContentAdaptWithSize(true);
_local18.setString(_local17);
_local18.y=((_local16.y + (_local16.height / 2)) + (_local18.height / 2));
_local18.x=_local16.x;
_aliased9329.addChild(_local18);
_local2.clubAvatar?MjClient.loadImgQX(_local2.clubAvatar,{isCrossOrigin:true},function () { __FUNC_6__ }):MjClient.loadImgQX("png/default_headpic.png",{isCrossOrigin:true},function () { __FUNC_6__ });
} else {
}
_aliased9329.visible=false;
_local19=_aliased5568.getChildByName("infoMation");
_local19.visible=true;
_local19.setString(_local20());
_local19.setFontName("Arial");
_local19.setFontSize(_local19.getFontSize());
_local19.ignoreContentAdaptWithSize(true);
_local21=_aliased5568.getChildByName("time");
_local21.visible=true;
_local21.setString("");
if(MjClient.roundEndTime){
_local21.setString(("" + MjClient.roundEndTime));
}
_local22=_aliased5568.getChildByName("close");
this._close1=_local22;
_local22.addTouchEventListener(function () { __FUNC_7__ },this);
_local23=_aliased5568.getChildByName("btnReplay");
_aliased3216=MjClient.data.sData.tData;
(_local2 != null)?_local23.visible=false:_local23.visible=true;
_local23.addTouchEventListener(function () { __FUNC_9__ },this);
_local24=0;
while((_local24 < 8)){
_local25=_aliased5568.getChildByName("scrollView");
if(!(_local25)){
return undefined;
}
_local26=_local25.getChildByName(("head" + _local24));
print(("" + _local24));
this.SetGameOverLayer_SG(_local26,_local24,_aliased5568);
_local24=(_local24 + 1);
_local24=0;
}
return true;
---------------------------------------
==========================================================================E
==================================2==================================S
------------------Argv------------------
sData,tData,text,extraNum,strPayWay
---------------------------------------
----------------Content----------------
_local0=MjClient.data.sData;
_local1=_local0.tData;
_local2="";
_local2=(_local2 + GameCnName[MjClient.gameType]);
_local1.extraNum?_local3=_local1.extraNum:_local3=0;
_local4="";
switch(_local1.areaSelectMode.payWay){
case 0:
_local4="";
break;
case 1:
_local4="";
break;
case 2:
_local4="";
break;
default:
}
_local2=(_local2 + _local4);
_local2=(_local2 + "");
_local2=(_local2 + _local1.tableid);
return _local2;
---------------------------------------
==========================================================================E
==================================3==================================S
------------------Argv------------------
sender,Type
---------------------------------------
----------------Content----------------
switch(Type){
case ccui.Widget.TOUCH_ENDED:
if(isJinZhongAPPType()){
ShareGameOverInfo();
} else {
}
MjClient.shareMultiPlatform(MjClient.systemConfig.sharePlatforms,function () { __FUNC_4__ });
MjClient.native.umengEvent4CountWithProperty("Fangjiannei_Dajiesuanjiemian_Fenxiang",{uid:SelfUid(),gameType:MjClient.gameType});
break;
default:
}
---------------------------------------
==========================================================================E
==================================4==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
COMMON_UI.getShareCodeTexture(_aliased3216.tableInfoUrl,_aliased5568,function () { __FUNC_5__ });
---------------------------------------
==========================================================================E
==================================5==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
postEvent("capture_screen");
if(MjClient.endallui&&cc.sys.isObjectValid(MjClient.endallui)){
MjClient.endallui.capture_screen=true;
}
_aliased8791.setTouchEnabled(false);
---------------------------------------
==========================================================================E
==================================6==================================S
------------------Argv------------------
err,texture,sp
---------------------------------------
----------------Content----------------
if(err || !(texture) || !(sys.isObjectValid(_aliased9329))){
return undefined;
}
_local0=new cc.Sprite(texture);
if(!(_local0)){
return undefined;
}
_local0.setScale(((_aliased9329.width - 8) / _local0.width));
_local0.setPosition(cc.p((_aliased9329.width / 2),(_aliased9329.height / 2)));
_aliased9329.addChild(_local0);
---------------------------------------
==========================================================================E
==================================7==================================S
------------------Argv------------------
sender,Type,clubInfoTable
---------------------------------------
----------------Content----------------
switch(Type){
case ccui.Widget.TOUCH_ENDED:
if(MjClient.Scene.getChildByName("shareCode")){
MjClient.Scene.removeChildByName("shareCode");
}
_local0=getClubInfoInTable();
if(_local0){
if(!(MjClient.FriendCard_main_ui)){
MjClient.Scene.addChild(new FriendCard_main(_local0.clubId));
}}
MjClient.gamenet.request("pkplayer.handler.LeaveGame",{},function () { __FUNC_8__ });
delete MjClient.data.sData;
delete MjClient.gameType;
postEvent("LeaveGame");
if(MjClient.enterui){
MjClient.enterui.removeFromParent(true);
MjClient.enterui=null;
}
if(MjClient.createui){
MjClient.createui.removeFromParent(true);
MjClient.createui=null;
}
if(MjClient.endallui){
MjClient.endallui.removeFromParent(true);
MjClient.endallui=null;
}
if(cc.sys.isObjectValid(MjClient.goldMatchingui)){
MjClient.goldMatchingui.removeFromParent();
delete MjClient.goldMatchingui;
}
if((MjClient.rePlayVideo >= 0)&&MjClient.replayui){
MjClient.replayui.replayEnd();
}
default:
---------------------------------------
==========================================================================E
==================================8==================================S
------------------Argv------------------
rtn
---------------------------------------
----------------Content----------------
---------------------------------------
==========================================================================E
==================================9==================================S
------------------Argv------------------
sender,Type
---------------------------------------
----------------Content----------------
switch(Type){
case ccui.Widget.TOUCH_ENDED:
if(MjClient.data.inviteVipTable){
MjClient.leaveGame(function () { __FUNC_10__ });
} else {
}
MjClient.reCreateRoom();
break;
default:
}
---------------------------------------
==========================================================================E
==================================10==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
MjClient.joinGame(MjClient.data.inviteVipTable,null,false,MjClient.gameType);
---------------------------------------
==========================================================================E
==================================11==================================S
------------------Argv------------------
node,off,_back,sData,tData,pl,uidSelf,MaxWinAll,MaxDianPao,uid,pi,uibind,wxNode
---------------------------------------
----------------Content----------------
_local0=MjClient.data.sData;
_local1=_local0.tData;
_aliased9776=MjClient.getPlayerByIndex(off);
node.setVisible(false);
if(!(_aliased9776)){
print("");
return undefined;
}
node.setVisible(true);
node.getChildByName("tuoguan").setVisible(false);
if(_local1.dissmissDesc&&_local1.dissmissDesc[_aliased9776.info.uid]){
node.getChildByName("tuoguan").setVisible(true);
}
_local3=SelfUid();
_local4=0;
_local5=0;
while(_local0.players has _iternext){
_local6=_iternext;
_local7=_local0.players[_local6];
if(_local7){
(_local4 > _local7.winall)?_local4=_local4:_local4=_local7.winall;
}
}
_local8={name:{_run:function () { __FUNC_12__ },_text:function () { __FUNC_13__ }},id:{_run:function () { __FUNC_14__ },_text:function () { __FUNC_15__ }},normalNode:{winNum1:{},winNum2:{}},bigWinner:{}};
_local9=node.getChildByName("head");
addWxHeadToEndUI(_local9,off);
BindUiAndLogic(node,_local8);
if(!(_aliased9776)){
_local8.normalNode._node.visible=false;
node.getChildByName("fangzhu").visible=false;
return undefined;
}
_local8.normalNode._node.visible=true;
if((_aliased9776.winall >= 0)){
_local8.normalNode.winNum2._node.visible=false;
_local8.normalNode.winNum1._node.visible=true;
_local8.normalNode.winNum1._node.setString(("/" + _aliased9776.winall));
_local8.normalNode.winNum1._node.ignoreContentAdaptWithSize(true);
} else {
}
_local8.normalNode.winNum1._node.visible=false;
_local8.normalNode.winNum2._node.visible=true;
_local8.normalNode.winNum2._node.setString(("/" + Math.abs(_aliased9776.winall)));
_local8.normalNode.winNum2._node.ignoreContentAdaptWithSize(true);
if((_local4 != 0)&&(_local4 == _aliased9776.winall)){
node.getChildByName("bigWinner").visible=true;
} else {
}
node.getChildByName("bigWinner").visible=false;
node.getChildByName("fangzhu").visible=(_local1.owner == _aliased9776.info.uid);
---------------------------------------
==========================================================================E
==================================12==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
this.setFontName("Arial");
this.setFontSize(this.getFontSize());
this.setFontSize(this.getFontSize());
---------------------------------------
==========================================================================E
==================================13==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
if(!(_aliased9776)){
return "";
}
return (unescape(_aliased9776.info.nickname) + "");
---------------------------------------
==========================================================================E
==================================14==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
this.ignoreContentAdaptWithSize(true);
---------------------------------------
==========================================================================E
==================================15==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
if(!(_aliased9776)){
return "";
}
return ("ID:" + _aliased9776.info.uid.toString());
---------------------------------------
==========================================================================E
