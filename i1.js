==================================0==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
var Setting_thirteenGX;
Setting_thirteenGX = cc.Layer.extend({jsBind:{_event:{roundEnd:function () { __FUNC_1__ },LeaveGame:function () { __FUNC_2__ },endRoom:function () { __FUNC_3__ }},block:{_layout:[[1,1],[0.5,0.5],[0,0],true],_click:function () { __FUNC_4__ }},back:{_layout:[[1,1],[1,0],[0,0]],menu:{_run:function () { __FUNC_5__ },picture:{_click:function () { __FUNC_6__ }},gongneng:{_click:function () { __FUNC_7__ }}},delRoom:{_run:function () { __FUNC_8__ },_click:function () { __FUNC_9__ },onLeaveGameConfirm:function () { __FUNC_10__ },onLeaveGameCancel:function () { __FUNC_11__ },onDelRoomConfirm:function () { __FUNC_12__ },onDelRoomCancel:function () { __FUNC_13__ }},fixBtn:{_visible:true,_run:function () { __FUNC_14__ },_click:function () { __FUNC_15__ }},panel3:{_visible:false,_event:{showRoomSettingPanel:function () { __FUNC_16__ }},_run:function () { __FUNC_17__ }}}},ctor:function () { __FUNC_18__ },initSettingUI:function () { __FUNC_19__ },loadGameBackground:function () { __FUNC_20__ },onExit:function () { __FUNC_21__ },getRoomSettingPanel:function () { __FUNC_22__ },addListenerText:function () { __FUNC_23__ },setTextClick:function () { __FUNC_24__ },onGameBgClick:function () { __FUNC_25__ },onPokerImgClick:function () { __FUNC_26__ },updateGameBgType:function () { __FUNC_27__ },updatePokerImgType:function () { __FUNC_28__ },removeSettingPanel:function () { __FUNC_29__ }});
---------------------------------------
==========================================================================E
==================================1==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
MjClient.setui.removeSettingPanel();
---------------------------------------
==========================================================================E
==================================2==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
MjClient.setui.removeSettingPanel();
---------------------------------------
==========================================================================E
==================================3==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
MjClient.setui.removeSettingPanel();
---------------------------------------
==========================================================================E
==================================4==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
MjClient.setui.removeSettingPanel();
---------------------------------------
==========================================================================E
==================================5==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
MjClient.setui.getRoomSettingPanel();
---------------------------------------
==========================================================================E
==================================6==================================S
------------------Argv------------------
type,curGameType
---------------------------------------
----------------Content----------------
_local0=1;
_local1=GameClass[MjClient.gameType];
MjClient.setui.getRoomSettingPanel(_local0);
postEvent("showRoomSettingPanel",{type:_local0,gameType:_local1});
util.localStorageEncrypt.setNumberItem("menu",_local0);
---------------------------------------
==========================================================================E
==================================7==================================S
------------------Argv------------------
type,curGameType
---------------------------------------
----------------Content----------------
_local0=2;
_local1=GameClass[MjClient.gameType];
MjClient.setui.getRoomSettingPanel(_local0);
postEvent("showRoomSettingPanel",{type:_local0,gameType:_local1});
util.localStorageEncrypt.setNumberItem("menu",_local0);
---------------------------------------
==========================================================================E
==================================8==================================S
------------------Argv------------------
sData,tData
---------------------------------------
----------------Content----------------
if(!(MjClient.data) || !(MjClient.data.sData) || !(MjClient.data.sData.tData)){
this.setVisible(false);
return undefined;
}
_local0=MjClient.data.sData;
_local1=_local0.tData;
if(_local1.matchId||_local1.fieldId){
this.setVisible(false);
}
---------------------------------------
==========================================================================E
==================================9==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
if(!(IsRoomCreator()) && (MjClient.data.sData.tData.tState === TableState.waitJoin) || (MjClient.data.sData.tData.tState === TableState.waitReady)){
MjClient.showMsg("",function () { __FUNC_10__ },function () { __FUNC_11__ });
} else {
}
MjClient.showMsg("确认解散房间？",function () { __FUNC_12__ },function () { __FUNC_13__ },1);
---------------------------------------
==========================================================================E
==================================10==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
MjClient.leaveGame();
if(MjClient.setui){
MjClient.setui.removeFromParent(true);
MjClient.setui=null;
}
---------------------------------------
==========================================================================E
==================================11==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
---------------------------------------
==========================================================================E
==================================12==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
MjClient.delRoom(true);
if(MjClient.setui){
MjClient.setui.removeFromParent(true);
MjClient.setui=null;
}
MjClient.delRoomTime=new Date().getTime();
---------------------------------------
==========================================================================E
==================================13==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
---------------------------------------
==========================================================================E
==================================14==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
if((MjClient.isShenhe === true)){
this.setVisible(false);
}
---------------------------------------
==========================================================================E
==================================15==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
if(MjClient.native.yayaVoice&&MjClient.native.yayaVoice._isOpenVoice){
MjClient.native.yayaVoice.leaveRoom();
}
removeUpdataDirectory();
---------------------------------------
==========================================================================E
==================================16==================================S
------------------Argv------------------
ed
---------------------------------------
----------------Content----------------
if((ed.gameType != MjClient.GAME_CLASS.MA_JIANG)){
if((ed.type == 1)){
this.visible=true;
} else {
}
this.visible=false;
}
---------------------------------------
==========================================================================E
==================================17==================================S
------------------Argv------------------
type
---------------------------------------
----------------Content----------------
_local0=util.localStorageEncrypt.getNumberItem("menu",1);
if((GameClass[MjClient.gameType] !== MjClient.GAME_CLASS.MA_JIANG)&&(_local0 === 1)){
this.visible=true;
}
---------------------------------------
==========================================================================E
==================================18==================================S
------------------Argv------------------
jsonFile,setui,_back,gameBGName,nodeListgamePKBg,i,gameBg,file,ccImg,image_node,text_node,gameBgEventCb_Poker,gameBgType,nodeListPKImg,PKImg,pokerImgEventCb,PKImgType
---------------------------------------
