==================================0==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
function RankLogic_thirteenGX( __ARGV_1__ ){ __FUNC_1__ }
RankLogic_thirteenGX.prototype.DeepCopy=function () { __FUNC_2__ };
RankLogic_thirteenGX.prototype.InitDunState=function () { __FUNC_3__ };
RankLogic_thirteenGX.prototype.GetIsSixteen=function () { __FUNC_4__ };
RankLogic_thirteenGX.prototype.InitCount=function () { __FUNC_5__ };
RankLogic_thirteenGX.prototype.GetSpecialType=function () { __FUNC_6__ };
RankLogic_thirteenGX.prototype.SetSpecialCard=function () { __FUNC_7__ };
RankLogic_thirteenGX.prototype.GetDunLength=function () { __FUNC_8__ };
RankLogic_thirteenGX.prototype.getDunListByType=function () { __FUNC_9__ };
RankLogic_thirteenGX.prototype.setDunListByType=function () { __FUNC_10__ };
RankLogic_thirteenGX.prototype.clearSelectedCards=function () { __FUNC_11__ };
RankLogic_thirteenGX.prototype.SetCardSelected=function () { __FUNC_12__ };
RankLogic_thirteenGX.prototype.DeleteCardSelected=function () { __FUNC_13__ };
RankLogic_thirteenGX.prototype.CheckDaoShui=function () { __FUNC_14__ };
RankLogic_thirteenGX.prototype.AutoSetDun=function () { __FUNC_15__ };
RankLogic_thirteenGX.prototype.QuickSetDunEx=function () { __FUNC_16__ };
RankLogic_thirteenGX.prototype.SetDunEx=function () { __FUNC_17__ };
RankLogic_thirteenGX.prototype.SetDun=function () { __FUNC_18__ };
RankLogic_thirteenGX.prototype.GetDunCards=function () { __FUNC_19__ };
RankLogic_thirteenGX.prototype.pushDownCards=function () { __FUNC_20__ };
RankLogic_thirteenGX.prototype.ClearDun=function () { __FUNC_21__ };
RankLogic_thirteenGX.prototype.GetDunCard=function () { __FUNC_22__ };
RankLogic_thirteenGX.prototype.ClearOneCard=function () { __FUNC_23__ };
RankLogic_thirteenGX.prototype.CheckAllRanked=function () { __FUNC_24__ };
RankLogic_thirteenGX.prototype.CheckSelected=function () { __FUNC_25__ };
RankLogic_thirteenGX.prototype.CheckSelected2=function () { __FUNC_26__ };
RankLogic_thirteenGX.prototype.SendRankList=function () { __FUNC_27__ };
RankLogic_thirteenGX.prototype.CheckDuiZi=function () { __FUNC_29__ };
RankLogic_thirteenGX.prototype.CheckLiangDui=function () { __FUNC_30__ };
RankLogic_thirteenGX.prototype.CheckSanTiao=function () { __FUNC_31__ };
RankLogic_thirteenGX.prototype.CheckShunzi=function () { __FUNC_32__ };
RankLogic_thirteenGX.prototype.CheckTonghua=function () { __FUNC_33__ };
RankLogic_thirteenGX.prototype.CheckHulu=function () { __FUNC_34__ };
RankLogic_thirteenGX.prototype.CheckZhaDang=function () { __FUNC_35__ };
RankLogic_thirteenGX.prototype.CheckTongHuaShun=function () { __FUNC_36__ };
RankLogic_thirteenGX.prototype.CheckWuTong=function () { __FUNC_37__ };
MjClient.rank_thirteenGX=new RankLogic_thirteenGX();
---------------------------------------
==========================================================================E
==================================1==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
this.LogicGame=MjClient.poker_thirteenGX;
this.dunTypes=["DOWN","SELECTED","DUN1","DUN2","DUN3"];
this._allowRepeatCard=true;
this.InitCount();
this.lastShouCard=[];
this.publicCard=-1;
this.cardStateList=[];
_local0=0;
while((_local0 < this.dunTypes.length)){
_local1=undefined;
_local1=this.dunTypes[_local0];
this.cardStateList[_local1]=[];
}
        
_local0=(_local0 + 1);
_local0=0;
}
}
        
this._selectCardArr=[];
---------------------------------------
==========================================================================E
==================================2==================================S
------------------Argv------------------
target
---------------------------------------
----------------Content----------------
return JSON.parse(JSON.stringify(target));
---------------------------------------
==========================================================================E
==================================3==================================S
------------------Argv------------------
shouCardList
---------------------------------------
----------------Content----------------
this.isSpecial=false;
if((shouCardList.length > 13)){
this.isSixteen=true;
} else {
}
this.isSixteen=false;
if(this.lastShouCard.length){
_local0=undefined;
_local0=0;
_local1=0;
while((_local1 < shouCardList.length)){
_local2=undefined;
_local2=shouCardList[_local1];
if((this.lastShouCard.indexOf(_local2) != -1)){
_local0=(_local0 + 1);
_local0=0;
}
        
}
_local1=(_local1 + 1);
_local1=0;
}
}
        
if((_local0 == 13)){
this.lastShouCard=[];
undefined;
}
        
}
        
}
}
this.lastShouCard=this.DeepCopy(shouCardList);
if((this.publicCard > -1)){
this.lastShouCard.push(this.publicCard);
}
cc.log("");
this.cardStateList.DOWN=this.DeepCopy(shouCardList);
this.cardStateList.DUN1=[];
this.cardStateList.DUN2=[];
this.cardStateList.DUN3=[];
this.cardStateList.SELECTED=[];
this._selectCardArr=[];
cc.log("");
this.InitCount();
---------------------------------------
==========================================================================E
==================================4==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
return this.isSixteen;
---------------------------------------
==========================================================================E
==================================5==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
this.duiziCount=0;
this.liangduiCount=0;
this.liangduiCount2=0;
this.santiaoCount=0;
this.shunziCount=0;
this.tonghuaCount=0;
this.huluCount=0;
this.zhadanCount=0;
this.tonghuaShunCount=0;
this.wutongCount=0;
this.duiziLen=0;
this.liangduiLen=0;
this.santiaoLen=0;
this.shunziLen=0;
this.tonghuaLen=0;
this.huluLen=0;
this.zhadanLen=0;
this.tonghuaShunLen=0;
this.wutongLen=0;
---------------------------------------
==========================================================================E
==================================6==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
return this.specialType;
---------------------------------------
==========================================================================E
==================================7==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
this.isSpecial=true;
---------------------------------------
==========================================================================E
==================================8==================================S
------------------Argv------------------
dun
---------------------------------------
----------------Content----------------
return this.cardStateList[dun].length;
---------------------------------------
==========================================================================E
==================================9==================================S
------------------Argv------------------
type
---------------------------------------
----------------Content----------------
return this.cardStateList[type];
---------------------------------------
==========================================================================E
==================================10==================================S
------------------Argv------------------
dun,cardList
---------------------------------------
----------------Content----------------
this.cardStateList[dun]=cardList;
---------------------------------------
==========================================================================E
==================================11==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
if(this.cardStateList.SELECTED.length){
this.cardStateList.SELECTED=[];
this._selectCardArr=[];
postEvent("EVT_DUN_UPDATE");
}
---------------------------------------
==========================================================================E
==================================12==================================S
------------------Argv------------------
cardIdx,cardType,pos
---------------------------------------
----------------Content----------------
_local0=this.cardStateList.DOWN[(cardIdx - 1)];
_local1=this.cardStateList.SELECTED.indexOf(_local0);
if(_local1||this._allowRepeatCard){
this.cardStateList.SELECTED.push(_local0);
if((this._selectCardArr.indexOf(cardIdx) == -1)){
this._selectCardArr.push(cardIdx);
print(("" + cardIdx));
}
console.log("",this.cardStateList.SELECTED);
} else {
}
print("can not find cardtype %d",_local0);
---------------------------------------
==========================================================================E
==================================13==================================S
------------------Argv------------------
cardIdx,cardType,pos
---------------------------------------
----------------Content----------------
_local0=this.cardStateList.DOWN[(cardIdx - 1)];
_local1=this.cardStateList.SELECTED.indexOf(_local0);
if((_local1 > -1)||this._allowRepeatCard){
this.cardStateList.SELECTED.splice(_local1,1);
postEvent("EVT_DUN_UPDATE");
}
---------------------------------------
==========================================================================E
==================================14==================================S
------------------Argv------------------
dun,tempSelectCards,DaoShui,NotDaoShui
---------------------------------------
----------------Content----------------
_local0=[];
_local1=0;
_local2=1;
_local3=0;
while((_local3 < this.cardStateList[dun].length)){
_local0.push(this.cardStateList[dun][_local3]);
_local3=(_local3 + 1);
_local3=0;
}
}
        
_local3=0;
while((_local3 < this.cardStateList.SELECTED.length)){
_local0.push(this.cardStateList.SELECTED[_local3]);
if((dun == "DUN1")&&(_local3 == 2)){
break;
} else if(((dun == "DUN2") || (dun == "DUN3"))&&(_local3 == 4)){
break;
_local3=(_local3 + 1);
_local3=0;
}
}
        
if((dun == "DUN1")&&(_local0.length != 3)){
return _local2;
} else if((dun == "DUN1")&&(_local0.length == 3)){
if((this.cardStateList.DUN2.length != 5)&&(this.cardStateList.DUN3.length != 5)){
return _local2;
}}
if((dun == "DUN2")&&(_local0.length != 5)){
return _local2;
} else if((dun == "DUN2")&&(_local0.length == 5)){
if((this.cardStateList.DUN3.length != 5)&&(this.cardStateList.DUN1.length != 3)){
return _local2;
}}
if((dun == "DUN3")&&(_local0.length != 5)){
return _local2;
}
if((dun == "DUN1")){
if((this.cardStateList.DUN2.length == 0)&&(this.cardStateList.DUN3.length == 0)){
return _local2;
}
if((this.cardStateList.DUN2.length != 0)){
if((this.LogicGame.CheckCardBigOrSmall(_local0,this.cardStateList.DUN2) == 0)){
return _local1;
}}
if((this.cardStateList.DUN3.length != 0)){
if((this.LogicGame.CheckCardBigOrSmall(_local0,this.cardStateList.DUN3) == 0)){
return _local1;
}}
} else 
if((this.cardStateList.DUN3.length == 0)&&(this.cardStateList.DUN1.length == 0)){
return _local2;
}
if((this.cardStateList.DUN1.length != 0)){
if((this.LogicGame.CheckCardBigOrSmall(this.cardStateList.DUN1,_local0) == 0)){
return _local1;
}}
if((this.cardStateList.DUN3.length != 0)){
if((this.LogicGame.CheckCardBigOrSmall(_local0,this.cardStateList.DUN3) == 0)){
return _local1;
}}
} else 
if((this.cardStateList.DUN1.length == 0)&&(this.cardStateList.DUN2.length == 0)){
return _local2;
}
if((this.cardStateList.DUN1.length != 0)){
if((this.LogicGame.CheckCardBigOrSmall(this.cardStateList.DUN1,_local0) == 0)){
return _local1;
}}
if((this.cardStateList.DUN2.length != 0)){
if((this.LogicGame.CheckCardBigOrSmall(this.cardStateList.DUN2,_local0) == 0)){
return _local1;
}}}
return _local2;
---------------------------------------
==========================================================================E
==================================15==================================S
------------------Argv------------------
isShow,cardID,publicID,nIdx
---------------------------------------
----------------Content----------------
if(((this.cardStateList.DUN1.length == 3) && (this.cardStateList.DUN2.length == 5)) || ((this.cardStateList.DUN1.length == 3) && (this.cardStateList.DUN3.length == 5)) || (this.cardStateList.DUN2.length == 5) && (this.cardStateList.DUN3.length == 5)){
_local5=undefined;
_local4=undefined;
_local3=undefined;
_local2=undefined;
_local1=undefined;
_local1=[];
_local2=this.DeepCopy(this.cardStateList.DOWN);
_local3=[];
_local4=[];
this.clearSelectedCards();
if((this.cardStateList.DUN1.length < 3)){
_local6=undefined;
_local3=_local3.concat(this.cardStateList.DUN1,_local2);
_local6=this.LogicGame.GetMaxCardType(_local3);
_local1=[];
_local7=0;
break;
while((_local7 < _local6.length)){
_local8=undefined;
break;
while(_local6[_local7] has _iternext){
_local8=_iternext;
_local9=undefined;
_local9=_local6[_local7][_local8].cardList;
_local10=0;
while((_local10 < _local9.length)){
_local1.push(_local9[_local10]);
_local10=(_local10 + 1);
_local10=0;
}
}
        
}
        
}
}
        
_local7=(_local7 + 1);
_local7=0;
}
}
        
if((_local1.length < 3)){
_local1=_local2;
}
_local7=0;
while((_local7 < _local1.length)){
_local8=undefined;
_local8=_local1[_local7];
if((this.cardStateList.DUN1.length == 3)){
}
        
break;
}
this.cardStateList.DUN1.push(_local8);
}
        
_local7=(_local7 + 1);
_local7=0;
}
}
        
}
        
break;
}
if((this.cardStateList.DUN2.length < 5)){
_local6=undefined;
_local3=_local3.concat(this.cardStateList.DUN2,_local2);
this.cardStateList.DUN2=[];
_local6=this.LogicGame.GetMaxCardType(_local3);
_local1=[];
_local7=0;
break;
while((_local7 < _local6.length)){
_local8=undefined;
break;
while(_local6[_local7] has _iternext){
_local8=_iternext;
_local9=undefined;
_local9=_local6[_local7][_local8].cardList;
_local10=0;
} else {
while((_local10 < _local9.length)){
_local1.push(_local9[_local10]);
_local10=(_local10 + 1);
_local10=0;
}
}
        
}
        
}
}
        
_local7=(_local7 + 1);
_local7=0;
}
}
        
if((_local1.length < 5)){
_local1=_local2;
}
_local7=0;
while((_local7 < _local1.length)){
_local8=undefined;
_local8=_local1[_local7];
if((this.cardStateList.DUN2.length == 5)){
}
        
break;
}
this.cardStateList.DUN2.push(_local8);
}
        
_local7=(_local7 + 1);
_local7=0;
}
}
        
}
        
break;
}
if((this.cardStateList.DUN3.length < 5)){
_local6=undefined;
_local3=_local3.concat(this.cardStateList.DUN3,_local2);
this.cardStateList.DUN3=[];
_local6=this.LogicGame.GetMaxCardType(_local3);
_local1=[];
_local7=0;
break;
while((_local7 < _local6.length)){
_local8=undefined;
break;
while(_local6[_local7] has _iternext){
_local8=_iternext;
_local9=undefined;
_local9=_local6[_local7][_local8].cardList;
_local10=0;
} else {
while((_local10 < _local9.length)){
_local1.push(_local9[_local10]);
_local10=(_local10 + 1);
_local10=0;
}
}
        
}
        
}
}
        
_local7=(_local7 + 1);
_local7=0;
}
}
        
if((_local1.length < 5)){
_local1=_local2;
}
_local7=0;
while((_local7 < _local1.length)){
_local8=undefined;
_local8=_local1[_local7];
if((this.cardStateList.DUN3.length == 5)){
}
        
break;
this.cardStateList.DUN3.push(_local8);
}
        
_local7=(_local7 + 1);
_local7=0;
}
}
        
}
        
}
_local4=_local4.concat(this.cardStateList.DUN1,this.cardStateList.DUN2,this.cardStateList.DUN3);
_local6=0;
while((_local6 < _local1.length)){
_local7=undefined;
_local7=_local1[_local6];
} else if((_local4.indexOf(_local7) > -1)||this._allowRepeatCard){
_local0=this.cardStateList.DOWN.indexOf(_local7);
if((_local0 > -1)||this._allowRepeatCard){
this.cardStateList.DOWN.splice(_local0,1);
}
        
}}
_local6=(_local6 + 1);
_local6=0;
}
}
        
_local5=publicID;
if((publicID != "")){
if(((this.cardStateList.DUN1.indexOf(publicID) > 0) || ((this.cardStateList.DUN2.indexOf(publicID) > 0) || (this.cardStateList.DUN3.indexOf(publicID) > 0)))&&(this.cardStateList.DOWN.length == 1)){
_local5=this.cardStateList.DOWN[0];
}}
postEvent("EVT_DUN_UPDATE");
}
        
}
---------------------------------------
==========================================================================E
==================================16==================================S
------------------Argv------------------
dun,dunCards
---------------------------------------
----------------Content----------------
this.setDunListByType(dun,dunCards);
---------------------------------------
==========================================================================E
==================================17==================================S
------------------Argv------------------
dun,catlist
---------------------------------------
----------------Content----------------
this.cardStateList.SELECTED=catlist;
_local0=0;
while((_local0 < this.cardStateList.SELECTED.length)){
_local3=undefined;
_local2=undefined;
_local1=undefined;
if((dun == "DUN1")&&(this.cardStateList[dun].length >= 3)){
}
        
} else if((dun == "DUN2")&&(this.cardStateList[dun].length >= 5)){
}
        
} else if((dun == "DUN3")&&(this.cardStateList[dun].length >= 5)){
}
        
_local1=this.cardStateList.SELECTED[_local0];
_local2=this.cardStateList[dun].indexOf(_local1);
_local3=this.cardStateList.DOWN.indexOf(_local1);
} else if(((_local2 == -1) && (_local3 > -1))||this._allowRepeatCard){
this.cardStateList.DOWN.splice(_local3,1);
this.cardStateList[dun].push(_local1);
}
        
}
_local0=(_local0 + 1);
_local0=0;
}
}
        
this.cardStateList.SELECTED=[];
postEvent("EVT_DUN_UPDATE");
---------------------------------------
==========================================================================E
==================================18==================================S
------------------Argv------------------
dun
---------------------------------------
----------------Content----------------
if((this.cardStateList.SELECTED == [])){
return undefined;
}
_local0=0;
while((_local0 < this.cardStateList.SELECTED.length)){
_local3=undefined;
_local2=undefined;
_local1=undefined;
if((dun == "DUN1")&&(this.cardStateList[dun].length >= 3)){
}
        
} else if((dun == "DUN2")&&(this.cardStateList[dun].length >= 5)){
}
        
} else if((dun == "DUN3")&&(this.cardStateList[dun].length >= 5)){
}
        
_local1=this.cardStateList.SELECTED[_local0];
_local2=this.cardStateList[dun].indexOf(_local1);
_local3=this.cardStateList.DOWN.indexOf(_local1);
} else if(((_local2 == -1) && (_local3 > -1))||this._allowRepeatCard){
print(("" + _local1));
this.cardStateList.DOWN.splice(_local3,1);
this.cardStateList[dun].push(_local1);
}
        
}
_local0=(_local0 + 1);
_local0=0;
}
}
        
this.cardStateList.SELECTED=[];
postEvent("EVT_DUN_UPDATE");
---------------------------------------
==========================================================================E
==================================19==================================S
------------------Argv------------------
tempSelectCards,pokers,duizis,liangdui,santiaos,shunzi,tonghua,zhadan,tonghuashun,wutong
---------------------------------------
----------------Content----------------
_local0=[];
_local1=this.LogicGame.GetDuiZi(tempSelectCards,true);
_local2=this.LogicGame.GetLiangDui(tempSelectCards);
_local3=this.LogicGame.GetSanTiao(tempSelectCards,true);
_local4=this.LogicGame.GetShunzi(tempSelectCards);
_local5=this.LogicGame.GetTonghua(tempSelectCards);
_local6=this.LogicGame.GetZhaDang(tempSelectCards,true);
_local7=this.LogicGame.GetTongHuaShunEx(tempSelectCards);
_local8=this.LogicGame.GetWuTong(tempSelectCards);
if((_local1.length > 0)){
_local0=_local1[0];
} else if((_local2.length > 0)){
_local0=_local2[0];
} else if((_local3.length > 0)){
_local0=_local3[0];
} else if((_local4.length > 0)){
_local0=_local4[0];
} else if((_local5.length > 0)){
_local0=_local5[0];
} else if((_local6.length > 0)){
_local0=_local6[0];
} else if((_local7.length > 0)){
_local0=_local7[0];
} else if((_local8.length > 0)){
_local0=_local8[0];
}
return _local0;
---------------------------------------
==========================================================================E
==================================20==================================S
------------------Argv------------------
pokers
---------------------------------------
----------------Content----------------
this.cardStateList.DOWN=[];
_local0=0;
while((_local0 < pokers.length)){
_local1=undefined;
_local1=pokers[_local0].cardX16;
this.cardStateList.DOWN.push(_local1);
}
        
_local0=(_local0 + 1);
_local0=0;
}
}
        
postEvent("EVT_DUN_UPDATE");
---------------------------------------
==========================================================================E
==================================21==================================S
------------------Argv------------------
dun,trigger,cardId
---------------------------------------
----------------Content----------------
_local0=0;
while((_local0 < this.cardStateList[dun].length)){
_local2=undefined;
_local1=undefined;
_local1=this.cardStateList[dun][_local0];
print(("" + _local1));
_local2=this.cardStateList.DOWN.indexOf(_local1);
if((-1 == _local2)||this._allowRepeatCard){
print("");
this.cardStateList.DOWN.push(_local1);
} else {
}
console.error("failed to clear because down has this cardType %s",_local1);
}
        
_local0=(_local0 + 1);
_local0=0;
}
}
        
print(("++++++ DOWN = " + JSON.stringify(this.cardStateList.DOWN)));
this.cardStateList[dun]=[];
if(trigger){
_local0=undefined;
_local0={cardId:cardId};
postEvent("EVT_DUN_UPDATE",_local0);
}
        
}
---------------------------------------
==========================================================================E
==================================22==================================S
------------------Argv------------------
dun,idx
---------------------------------------
----------------Content----------------
if(!(this.cardStateList[dun][idx])){
return false;
} else {
}
return this.cardStateList[dun][idx];
---------------------------------------
==========================================================================E
==================================23==================================S
------------------Argv------------------
dun,idx,cardId
---------------------------------------
----------------Content----------------
print(("++++++++++++++++= ClearOneCard dun = " + dun));
console.log("",this.cardStateList.SELECTED);
if(!(this.cardStateList[dun][idx])){
print("");
if(this.cardStateList.SELECTED.length){
this.SetDun(dun);
} else {
}
return undefined;
break;
print("");
if((this.cardStateList.SELECTED.length == 1)){
_local1=undefined;
_local0=undefined;
print("");
_local0=this.cardStateList[dun][idx];
_local1=this.cardStateList.DOWN.indexOf(_local0);
if((-1 == _local1)||this._allowRepeatCard){
print("");
_local2=0;
while((_local2 < this.cardStateList[dun].length)){
} else if((_local0 == this.cardStateList[dun][_local2])){
this.cardStateList[dun].splice(_local2,1);
print("");
}
_local2=(_local2 + 1);
_local2=0;
}
}
        
print("");
this.cardStateList.DOWN.push(_local0);
} else {
}
console.error("failed to clear because down has this cardType %s",_local0);
this.SetDun(dun);
}
        
break;
_local1=undefined;
_local0=undefined;
print("");
_local0=this.cardStateList[dun][idx];
_local1=this.cardStateList.DOWN.indexOf(_local0);
if((-1 == _local1)||this._allowRepeatCard){
_local2=0;
while((_local2 < this.cardStateList[dun].length)){
} else if((_local0 == this.cardStateList[dun][_local2])){
this.cardStateList[dun].splice(_local2,1);
}
_local2=(_local2 + 1);
_local2=0;
}
}
        
if((_local0 != this.publicCard)){
this.cardStateList.DOWN.push(_local0);
} else if((cardId > -1)){
if((this.cardStateList.DOWN.indexOf(cardId) < 0)){
this.cardStateList.DOWN.push(cardId);
} else {
}}
}
console.error("failed to clear because down has this cardType %s",_local0);
}
        
postEvent("EVT_DUN_UPDATE");
console.log("ClearOneCard",this.cardStateList);
---------------------------------------
==========================================================================E
==================================24==================================S
------------------Argv------------------
isShow,bAllRanked
---------------------------------------
----------------Content----------------
_local0=true;
if(this.isSixteen){
if((this.cardStateList.DOWN.length > 3)){
_local0=false;
}
} else 
if((this.cardStateList.DOWN.length > 0)){
_local0=false;
}
} else 
_local0=false;
}
return _local0;
---------------------------------------
==========================================================================E
==================================25==================================S
------------------Argv------------------
cardType,downPos
---------------------------------------
----------------Content----------------
_local0=this.cardStateList.SELECTED.indexOf(cardType);
if((-1 == _local0)){
return false;
}
return true;
---------------------------------------
==========================================================================E
==================================26==================================S
------------------Argv------------------
cardIdx,downPos
---------------------------------------
----------------Content----------------
print(("++++++++++++++++++++++++++++++++++++++ this._selectCardArr = " + JSON.stringify(this._selectCardArr)));
_local0=this._selectCardArr.indexOf(cardIdx);
if((-1 == _local0)){
return false;
}
return true;
---------------------------------------
==========================================================================E
==================================27==================================S
------------------Argv------------------
special,sendPack,first,second,third
---------------------------------------
----------------Content----------------
_local0={cmd:"doWaitBegin",isSpecial:(special > 0),card:{first:exchangeTo10Data_thirteenGX(this.cardStateList.DUN1),second:exchangeTo10Data_thirteenGX(this.cardStateList.DUN2),third:exchangeTo10Data_thirteenGX(this.cardStateList.DUN3)}};
_local1=_local0.card.first;
_local2=_local0.card.second;
_local3=_local0.card.third;
MjClient.selfMjhand=_local1.concat(_local2).concat(_local3);
MjClient.gamenet.request("pkroom.handler.tableMsg",_local0,function () { __FUNC_28__ });
this.ClearDun("DUN1",true);
this.ClearDun("DUN2",true);
this.ClearDun("DUN3",true);
return true;
---------------------------------------
==========================================================================E
==================================28==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
---------------------------------------
==========================================================================E
==================================29==================================S
------------------Argv------------------
downList,duizis
---------------------------------------
----------------Content----------------
this.cardStateList.SELECTED=[];
_local0=this.cardStateList.DOWN;
_local1=this.LogicGame.GetDuiZi(_local0,true);
if((this.duiziLen != _local1.length)){
this.duiziCount=0;
}
this.duiziLen=_local1.length;
if((_local1.length > 0)){
_local2=undefined;
_local2=[];
if((this.duiziCount >= _local1.length)){
this.duiziCount=0;
}
if((1 == _local1.length)){
_local2=_local1[0];
} else {
}
_local2=_local1[this.duiziCount];
this.duiziCount=(this.duiziCount + 1);
this.cardStateList.SELECTED=_local2;
postEvent("EVT_DUN_UPDATE");
}
        
}
---------------------------------------
==========================================================================E
==================================30==================================S
------------------Argv------------------
downList,duizis
---------------------------------------
----------------Content----------------
this.cardStateList.SELECTED=[];
_local0=this.cardStateList.DOWN;
_local1=this.LogicGame.GetDuiZi(_local0,true);
if((_local1.length < 2)){
MjClient.showToast("");
return undefined;
}
print(("++++++++++++++++ duizis.length = " + _local1.length));
if((this.liangduiLen != _local1.length)){
this.liangduiCount=0;
this.liangduiCount2=(this.liangduiCount + 1);
}
this.liangduiLen=_local1.length;
if((this.liangduiCount2 >= _local1.length)){
this.liangduiCount=(this.liangduiCount + 1);
this.liangduiCount2=(this.liangduiCount + 1);
}
if((this.liangduiCount >= _local1.length)){
this.liangduiCount=0;
this.liangduiCount2=(this.liangduiCount + 1);
}
if((_local1.length >= 2)){
do{
_local3=undefined;
_local2=undefined;
_local2=_local1[this.liangduiCount];
_local3=_local1[this.liangduiCount2];
this.liangduiCount2=(this.liangduiCount2 + 1);
if((this.liangduiCount2 >= _local1.length)){
this.liangduiCount=(this.liangduiCount + 1);
this.liangduiCount2=(this.liangduiCount + 1);
}
if((this.liangduiCount >= _local1.length)){
this.liangduiCount=0;
this.liangduiCount2=(this.liangduiCount + 1);
}
if((this.LogicGame.CheckSameValue(_local2,_local3) == false)){
_local4=undefined;
_local4=_local2.concat(_local3);
this.cardStateList.SELECTED=_local4;
}
        
}
        
}
        
}
        
}
} while(true)
}
if((this.cardStateList.SELECTED.length < 4)){
MjClient.showToast("");
return undefined;
}
postEvent("EVT_DUN_UPDATE");
---------------------------------------
==========================================================================E
==================================31==================================S
------------------Argv------------------
downList,santiaos,newSanTiaos,guipai
---------------------------------------
----------------Content----------------
this.cardStateList.SELECTED=[];
_local0=this.cardStateList.DOWN;
_local1=this.LogicGame.GetSanTiao(_local0,true);
_local2=[];
_local3=this.LogicGame.GetGuiPai(_local0);
_local4=0;
break;
while((_local4 < _local1.length)){
if((_local1[_local4].length == 3)){
_local5=0;
break;
while((_local5 < _local0.length)){
if((_local1[_local4].indexOf(_local0[_local5]) != -1)){
break;
_local6=(_local5 + 1);
while((_local6 < _local0.length)){
_local7=undefined;
} else if((_local1[_local4].indexOf(_local0[_local6]) != -1)){
}
        
_local7=_local1[_local4].concat();
_local7.push(_local0[_local5]);
_local7.push(_local0[_local6]);
} else if((this.LogicGame.CheckContainList(_local2,_local7) == false)){
_local2[_local2.length]=_local7;
}
        
}
_local6=(_local6 + 1);
_local6=(_local5 + 1);
}
}
        
_local5=(_local5 + 1);
_local5=0;
}
}
        
} else if((_local1[_local4].length == 5)){
if((this.LogicGame.CheckContainList(_local2,_local1[_local4]) == false)){
_local2[_local2.length]=_local1[_local4];
}}
_local4=(_local4 + 1);
_local4=0;
}
}
        
_local1=_local2;
if((this.santiaoLen != _local1.length)){
this.santiaoCount=0;
}
this.santiaoLen=_local1.length;
if(_local1.length){
_local4=undefined;
_local4=[];
if((1 == _local1.length)){
_local4=_local1[0];
} else {
}
_local4=_local1[this.santiaoCount];
this.santiaoCount=(this.santiaoCount + 1);
if((this.santiaoCount >= _local1.length)){
this.santiaoCount=0;
}
this.cardStateList.SELECTED=_local4;
}
        
} else {
}
MjClient.showToast("");
postEvent("EVT_DUN_UPDATE");
---------------------------------------
==========================================================================E
==================================32==================================S
------------------Argv------------------
shunzis,downList
---------------------------------------
----------------Content----------------
this.cardStateList.SELECTED=[];
_local0=[];
_local1=this.cardStateList.DOWN;
_local0=this.LogicGame.GetShunzi(_local1);
if((this.shunziLen != _local0.length)){
this.shunziCount=0;
}
this.shunziLen=_local0.length;
if(_local0.length){
_local2=undefined;
_local2=[];
if((this.shunziCount >= _local0.length)){
this.shunziCount=0;
}
if((1 == _local0.length)){
_local2=_local0[0];
} else {
}
_local2=_local0[this.shunziCount];
this.shunziCount=(this.shunziCount + 1);
this.cardStateList.SELECTED=_local2;
postEvent("EVT_DUN_UPDATE");
}
        
} else {
}
MjClient.showToast("");
postEvent("EVT_DUN_UPDATE");
---------------------------------------
==========================================================================E
==================================33==================================S
------------------Argv------------------
downList,tonghuas
---------------------------------------
----------------Content----------------
this.cardStateList.SELECTED=[];
_local0=this.cardStateList.DOWN;
_local1=this.LogicGame.GetTonghua(_local0);
if((this.tonghuaLen != _local1.length)){
this.tonghuaCount=0;
}
this.tonghuaLen=_local1.length;
if(_local1.length){
_local2=undefined;
_local2=[];
if((this.tonghuaCount >= _local1.length)){
this.tonghuaCount=0;
}
if((1 == _local1.length)){
_local2=_local1[0];
} else {
}
_local2=_local1[this.tonghuaCount];
this.tonghuaCount=(this.tonghuaCount + 1);
this.cardStateList.SELECTED=_local2;
postEvent("EVT_DUN_UPDATE");
}
        
}
---------------------------------------
==========================================================================E
==================================34==================================S
------------------Argv------------------
downList,duizis,santiaos,hulus
---------------------------------------
----------------Content----------------
this.cardStateList.SELECTED=[];
_local0=this.cardStateList.DOWN;
_local1=this.LogicGame.GetDuiZi(_local0,true);
_local2=this.LogicGame.GetSanTiao(_local0,true);
_local3=[];
_local4=0;
break;
while((_local4 < _local1.length)){
_local5=undefined;
_local5=_local1[_local4];
_local6=0;
while((_local6 < _local2.length)){
_local7=undefined;
_local7=_local2[_local6];
if(!(this.LogicGame.CheckSameValue(_local5,_local7))){
_local8=undefined;
_local8=_local5.concat(_local7);
if((this.LogicGame.CheckContainList(_local3,_local8) == false)){
_local3[_local3.length]=_local8;
}
        
}
}
        
}
_local6=(_local6 + 1);
_local6=0;
}
}
        
}
        
_local4=(_local4 + 1);
_local4=0;
}
}
        
this.huluCount=(this.huluCount + 1);
if((this.huluCount >= _local3.length)){
this.huluCount=0;
}
if((this.huluLen != _local3.length)){
this.huluCount=0;
}
this.huluLen=_local3.length;
this.cardStateList.SELECTED=_local3[this.huluCount];
postEvent("EVT_DUN_UPDATE");
---------------------------------------
==========================================================================E
==================================35==================================S
------------------Argv------------------
downList,zhadangs
---------------------------------------
----------------Content----------------
this.cardStateList.SELECTED=[];
_local0=this.cardStateList.DOWN;
_local1=this.LogicGame.GetZhaDang(_local0,true);
if((this.zhadanLen != _local1.length)){
this.zhadanCount=0;
}
this.zhadanLen=_local1.length;
if((_local1.length > 0)){
_local2=undefined;
_local2=[];
if((this.zhadanCount >= _local1.length)){
this.zhadanCount=0;
}
if((1 == _local1.length)){
_local2=_local1[0];
} else {
}
_local2=_local1[this.zhadanCount];
this.zhadanCount=(this.zhadanCount + 1);
this.cardStateList.SELECTED=_local2;
postEvent("EVT_DUN_UPDATE");
}
        
} else {
}
MjClient.showToast("");
postEvent("EVT_DUN_UPDATE");
---------------------------------------
==========================================================================E
==================================36==================================S
------------------Argv------------------
downList,tonghuaShuns
---------------------------------------
----------------Content----------------
this.cardStateList.SELECTED=[];
_local0=this.cardStateList.DOWN;
_local1=this.LogicGame.GetTongHuaShunEx(_local0);
if((this.tonghuaShunLen != _local1.length)){
this.tonghuaShunCount=0;
}
this.tonghuaShunLen=_local1.length;
if(_local1.length){
_local2=undefined;
_local2=[];
if((this.tonghuaShunCount >= _local1.length)){
this.tonghuaShunCount=0;
}
if((1 == _local1.length)){
_local2=_local1[0];
} else {
}
_local2=_local1[this.tonghuaShunCount];
this.tonghuaShunCount=(this.tonghuaShunCount + 1);
this.cardStateList.SELECTED=_local2;
postEvent("EVT_DUN_UPDATE");
}
        
} else {
}
MjClient.showToast("");
postEvent("EVT_DUN_UPDATE");
---------------------------------------
==========================================================================E
==================================37==================================S
------------------Argv------------------
downList,wutongs
---------------------------------------
----------------Content----------------
this.cardStateList.SELECTED=[];
_local0=this.cardStateList.DOWN;
_local1=this.LogicGame.GetWuTong(_local0);
if((this.wutongLen != _local1.length)){
this.wutongCount=0;
}
this.wutongLen=_local1.length;
if(_local1.length){
_local2=undefined;
_local2=[];
if((this.wutongCount >= _local1.length)){
this.wutongCount=0;
}
if((1 == _local1.length)){
_local2=_local1[0];
} else {
}
_local2=_local1[this.wutongCount];
this.wutongCount=(this.wutongCount + 1);
this.cardStateList.SELECTED=_local2;
postEvent("EVT_DUN_UPDATE");
}
        
} else {
}
MjClient.showToast("");
postEvent("EVT_DUN_UPDATE");
---------------------------------------
==========================================================================E
