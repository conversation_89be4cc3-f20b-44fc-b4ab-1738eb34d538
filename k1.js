==================================0==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
var thirteenGX_getCard;
thirteenGX_getCard = cc.Layer.extend({ctor:function () { __FUNC_1__ },initUI:function () { __FUNC_2__ },onBtnShowLayer:function () { __FUNC_3__ },onBlockClick:function () { __FUNC_4__ },onBackClick:function () { __FUNC_5__ },onBtnClose:function () { __FUNC_6__ },onBtnSure:function () { __FUNC_7__ },onRequestCallback:function () { __FUNC_8__ },onCardClick:function () { __FUNC_9__ },getData:function () { __FUNC_10__ },setData:function () { __FUNC_11__ },onExit:function () { __FUNC_12__ }});
---------------------------------------
==========================================================================E
==================================1==================================S
------------------Argv------------------
UI
---------------------------------------
----------------Content----------------
this._super();
_local0=ccs.load("thirteenGX_getCard.json");
this._node=_local0.node;
this.addChild(this._node);
MjClient.getCardLayer=this;
this._nMaxCardCnt=13;
this._clickCnt=0;
this.initUI();
---------------------------------------
==========================================================================E
==================================2==================================S
------------------Argv------------------
btn_showLayer,block,back,card,btnClose,btnSure,allCardsArray1,i,cardNode,tag,szCardName
---------------------------------------
----------------Content----------------
_local0=this._node.getChildByName("btn_showLayer");
_aliased1028=this._node.getChildByName("block");
_aliased3438=this._node.getChildByName("back");
_local3=_aliased3438.getChildByName("card");
this.cardClone=_local3;
_local4=_aliased3438.getChildByName("Button_Close");
_local5=_aliased3438.getChildByName("Button_Sure");
_aliased1028.visible=false;
_aliased3438.visible=false;
this.block=_aliased1028;
this.back=_aliased3438;
this.isAuth=true;
this.cardSelectList=[];
this.cardNodeList=[];
setWgtLayout(_local0,[0.15625,0.11111111111111],[0,1],[0,0]);
setWgtLayout(_aliased1028,[1,1],[0.5,0.5],[0,0],true);
setWgtLayout(_aliased3438,[1,1],[0.5,0.5],[0,0]);
COMMON_UI.setNodeTextAdapterSize(_aliased3438);
_local0.addTouchEventListener(function () { __FUNC_3__ },this);
_local0.visible=false;
_aliased1028.addTouchEventListener(function () { __FUNC_4__ },this);
_aliased3438.addTouchEventListener(function () { __FUNC_5__ },this);
_local4.addTouchEventListener(function () { __FUNC_6__ },this);
_local5.addTouchEventListener(function () { __FUNC_7__ },this);
_local3.addTouchEventListener(function () { __FUNC_9__ },this);
_local6=[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54];
this.allCardsArray=_local6;
_local7=0;
while((_local7 < this.allCardsArray.length)){
_local8=this.cardClone.clone();
_local9=this.allCardsArray[_local7];
_local8.visible=true;
_local8.setName(("card" + _local7));
_local8.setTag(_local9);
_aliased3438.addChild(_local8);
_local10=getCardName_thirteenGX(_local9);
_local8.loadTexture(_local10);
_local8.getChildByName("light").visible=false;
_local8.setScale(0.6);
_local8.setPosition((60 + ((_local7 % 12) * 100)),(75 + (Math.floor((_local7 / 12)) * 135)));
this.cardNodeList[_local7]=_local8;
_local7=(_local7 + 1);
_local7=0;
}
---------------------------------------
==========================================================================E
==================================3==================================S
------------------Argv------------------
sender,type,i
---------------------------------------
----------------Content----------------
if((type == 2)){
print("");
if(this.isAuth){
_local0=0;
while((_local0 < this.cardNodeList.length)){
if(this.cardNodeList[_local0]){
if(this.cardNodeList[_local0].getChildByName("light")){
this.cardNodeList[_local0].getChildByName("light").visible=false;
}}
_local0=(_local0 + 1);
_local0=0;
}
this.cardSelectList=[];
this.getData();
}}
---------------------------------------
==========================================================================E
==================================4==================================S
------------------Argv------------------
sender,type
---------------------------------------
----------------Content----------------
if((type == 2)){
}
---------------------------------------
==========================================================================E
==================================5==================================S
------------------Argv------------------
sender,type
---------------------------------------
----------------Content----------------
if((type == 2)){
_aliased1028.visible=false;
_aliased3438.visible=false;
}
---------------------------------------
==========================================================================E
==================================6==================================S
------------------Argv------------------
sender,type
---------------------------------------
----------------Content----------------
if((type == 2)){
_aliased1028.visible=false;
_aliased3438.visible=false;
}
---------------------------------------
==========================================================================E
==================================7==================================S
------------------Argv------------------
sender,type,sendInfo,i
---------------------------------------
----------------Content----------------
if((type == 2)){
if((this.cardSelectList.length != this._nMaxCardCnt)){
MjClient.showToast((("" + this._nMaxCardCnt) + "张扑克"));
return undefined;
}
_local0={cmd:"MJGetCardNextRoundCards",cardlist:this.cardSelectList};
MjClient.gamenet.request("pkroom.handler.tableMsg",_local0,function () { __FUNC_8__ });
_local1=0;
while((_local1 < this.cardNodeList.length)){
if(this.cardNodeList[_local1]){
if(this.cardNodeList[_local1].getChildByName("light")){
this.cardNodeList[_local1].getChildByName("light").visible=false;
}}
_local1=(_local1 + 1);
_local1=0;
}
this.cardSelectList=[];
_aliased1028.visible=false;
_aliased3438.visible=false;
}
---------------------------------------
==========================================================================E
==================================8==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
---------------------------------------
==========================================================================E
==================================9==================================S
------------------Argv------------------
sender,type,index
---------------------------------------
----------------Content----------------
if((type == 2)){
_local0=this.cardSelectList.indexOf(sender.tag);
if((_local0 > -1)){
this.cardSelectList.splice(_local0,1);
sender.getChildByName("light").visible=false;
print(("取消扑克" + sender.tag));
} else if((this.cardSelectList.length >= this._nMaxCardCnt)){
MjClient.showToast((("" + this._nMaxCardCnt) + "张扑克"));
return undefined;
}
this.cardSelectList.push(sender.tag);
sender.getChildByName("light").visible=true;
print(("" + sender.tag));
}
---------------------------------------
==========================================================================E
==================================10==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
if(this.isAuth){
this._clickCnt=(this._clickCnt + 1);
if((this._clickCnt >= 5)){
this._clickCnt=0;
cc.log("");
MjClient.gamenet.request("pkroom.handler.tableMsg",{cmd:"PlayerIsAuth"});
}}
---------------------------------------
==========================================================================E
==================================11==================================S
------------------Argv------------------
data
---------------------------------------
----------------Content----------------
cc.log("",JSON.stringify(data));
this.isAuth=data.isAuth;
if((data.isAuth == false)){
return undefined;
}
this.block.visible=true;
this.back.visible=true;
---------------------------------------
==========================================================================E
==================================12==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
this._super();
MjClient.getCardLayer=null;
---------------------------------------
==========================================================================E
