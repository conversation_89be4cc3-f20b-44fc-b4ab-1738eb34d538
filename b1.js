==================================0==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
var CreateRoomNode_thirteenGX;
CreateRoomNode_thirteenGX = CreateRoomNode.extend({initAll:function () { __FUNC_1__ },initRoundNode:function () { __FUNC_2__ },initPlayNode:function () { __FUNC_3__ },onRenshuRadioCallback:function () { __FUNC_4__ },onMapaiRadioCallback:function () { __FUNC_5__ },onLaiziRadioCallback:function () { __FUNC_6__ },onTuoguanTimeCallback:function () { __FUNC_7__ },onTuoguanTypeCallback:function () { __FUNC_8__ },onStartModeCallback:function () { __FUNC_9__ },onSubButtonCallback:function () { __FUNC_10__ },onAddButtonCallback:function () { __FUNC_11__ },setPlayNodeCurrentSelect:function () { __FUNC_12__ },getSelectedPara:function () { __FUNC_13__ }});
---------------------------------------
==========================================================================E
==================================1==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
this.localStorageKey.KEY_thirteengx_renshu="_THIRTEEN_GX_LUN_RENSHU";
this.localStorageKey.KEY_thirteengx_diZhu="_THIRTEEN_GX_DI_ZHU";
this.localStorageKey.KEY_thirteengx_wanglai="_THIRTEEN_GX_WANG_LAI";
this.localStorageKey.KEY_thirteengx_mapai="_THIRTEEN_GX_MA_PAI";
this.localStorageKey.KEY_thirteengx_tuizhu="_THIRTEEN_GX_TUI_ZHU";
this.localStorageKey.KEY_thirteengx_tuoguantime="_THIRTEEN_GX_TUO_GUAN_TIME";
this.localStorageKey.KEY_thirteengx_tuoguantype="_THIRTEEN_GX_TUO_GUAN_TYPE";
this.localStorageKey.KEY_thirteengx_difen="_THIRTEEN_GX_DI_FEN";
this.localStorageKey.KEY_thirteengx_antiLastJoin="_THIRTEEN_GX_ANTI_LAST_JOIN";
this.localStorageKey.KEY_thirteengx_antiCenterJoin="_THIRTEEN_GX_ANTI_CENTER_JOIN";
this.localStorageKey.KEY_thirteengx_daqiangfanbei="_THIRTEEN_GX_QIANG_BEI";
this.localStorageKey.KEY_thirteengx_quanleida="_THIRTEEN_GX_LEI_DA";
this.localStorageKey.KEY_thirteengx_teshu="_THIRTEEN_GX_TE_SHU";
this.localStorageKey.KEY_thirteengx_jiaheitao="_THIRTEEN_GX_JIA_HEI_TAO";
this.localStorageKey.KEY_thirteengx_jiahongtao="_THIRTEEN_GX_JIA_HONG_TAO";
this.localStorageKey.KEY_thirteengx_jiameihua="_THIRTEEN_GX_JIA_MEI_HUA";
this.localStorageKey.KEY_thirteengx_jiafangkuai="_THIRTEEN_GX_JIA_FANG_KUAI";
this.localStorageKey.KEY_thirteengx_bipai="_THIRTEEN_GX_BI_PAI";
this.localStorageKey.KEY_thirteengx_shuangwang="_THIRTEEN_GX_SHUANG_WANG";
this.localStorageKey.KEY_thirteengx_startMode="_THIRTEEN_GX_START_MODE";
this.localStorageKey.KEY_thirteengx_antiDissRoom="_THIRTEEN_GX_ANTI_DISS_ROOM";
this.bgNode=ccs.load("bg_thirteenGX.json").node;
this.addChild(this.bgNode);
this.bg_node=this.bgNode.getChildByName("bg_sangong").getChildByName("view");
this.bg_node.setScrollBarEnabled(false);
---------------------------------------
==========================================================================E
==================================2==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
this._super();
---------------------------------------
==========================================================================E
==================================3==================================S
------------------Argv------------------
_bgNiuniuNode,_play,_parent,renshuNodeList,mapaiNodeList,laiziNodeList,that,tuoguanTimeList,tuoguanTypeList,startNodeList,ccDiFenParent
---------------------------------------
----------------Content----------------
_local0=this.bg_node;
_aliased6738=_local0.getChildByName("play");
_local2=_local0.getParent();
_aliased3395=[];
_aliased3395.push(_aliased6738.getChildByName("cbx_renshu_0"));
_aliased3395.push(_aliased6738.getChildByName("cbx_renshu_1"));
_aliased3395.push(_aliased6738.getChildByName("cbx_renshu_2"));
_aliased3395.push(_aliased6738.getChildByName("cbx_renshu_3"));
this._renshu_radio=createRadioBoxForCheckBoxs(_aliased3395,function () { __FUNC_4__ }.bind(this));
this.addListenerText(_aliased3395,this._renshu_radio);
this.renshuNodeList=_aliased3395;
_aliased6655=[];
_aliased6655.push(_aliased6738.getChildByName("cbx_mapai_0"));
_aliased6655.push(_aliased6738.getChildByName("cbx_mapai_1"));
_aliased6655.push(_aliased6738.getChildByName("cbx_mapai_2"));
_aliased6655.push(_aliased6738.getChildByName("cbx_mapai_3"));
_aliased6655.push(_aliased6738.getChildByName("cbx_mapai_4"));
this._play_mapai_radio=createRadioBoxForCheckBoxs(_aliased6655,function () { __FUNC_5__ }.bind(this));
this.addListenerText(_aliased6655,this._play_mapai_radio);
this.mapaiNodeList=_aliased6655;
_aliased5141=[];
_aliased5141.push(_aliased6738.getChildByName("cbx_laizi_0"));
_aliased5141.push(_aliased6738.getChildByName("cbx_laizi_1"));
_aliased5141.push(_aliased6738.getChildByName("cbx_laizi_2"));
_aliased5141.push(_aliased6738.getChildByName("cbx_laizi_3"));
_aliased5141.push(_aliased6738.getChildByName("cbx_laizi_4"));
this._laizi_radio=createRadioBoxForCheckBoxs(_aliased5141,function () { __FUNC_6__ }.bind(this));
this.addListenerText(_aliased5141,this._laizi_radio);
this.laiziNodeList=_aliased5141;
_aliased1357=this;
this.tuoguantimeCallBack=function () { __FUNC_7__ };
_aliased9178=[];
_aliased9178.push(_aliased6738.getChildByName("tuoguan0"));
_aliased9178.push(_aliased6738.getChildByName("tuoguan1"));
_aliased9178.push(_aliased6738.getChildByName("tuoguan2"));
_aliased9178.push(_aliased6738.getChildByName("tuoguan3"));
_aliased9178.push(_aliased6738.getChildByName("tuoguan4"));
_aliased9178.push(_aliased6738.getChildByName("tuoguan5"));
this._tuoguan_time_radio=createRadioBoxForCheckBoxs(_aliased9178,this.tuoguantimeCallBack);
this.addListenerText(_aliased9178,this._tuoguan_time_radio,this.tuoguantimeCallBack);
this.tuoguanTimeList=_aliased9178;
_aliased4298=[];
_aliased4298.push(_aliased6738.getChildByName("tuoguanType_0"));
_aliased4298.push(_aliased6738.getChildByName("tuoguanType_1"));
_aliased4298.push(_aliased6738.getChildByName("tuoguanType_2"));
this._tuoguan_type_radio=createRadioBoxForCheckBoxs(_aliased4298,function () { __FUNC_8__ }.bind(this));
this.addListenerText(_aliased4298,this._tuoguan_type_radio);
this.tuoguanTypeList=_aliased4298;
this.rule_last_join=_aliased6738.getChildByName("rule_last_join");
this.addListenerText(this.rule_last_join);
this.rule_last_join.addEventListener(this.clickCB,this.rule_last_join);
this.rule_anti_center_join=_aliased6738.getChildByName("rule_anti_center_join");
this.addListenerText(this.rule_anti_center_join);
this.rule_anti_center_join.addEventListener(this.clickCB,this.rule_anti_center_join);
this.rule_anti_dissroom=_aliased6738.getChildByName("rule_anti_dissroom");
this.addListenerText(this.rule_anti_dissroom);
this.rule_anti_dissroom.addEventListener(this.clickCB,this.rule_anti_dissroom);
this.rule_daqiangfanbei=_aliased6738.getChildByName("rule_daqiangfanbei");
this.addListenerText(this.rule_daqiangfanbei);
this.rule_daqiangfanbei.addEventListener(this.clickCB,this.rule_daqiangfanbei);
this.rule_quanleida=_aliased6738.getChildByName("rule_quanleida");
this.addListenerText(this.rule_quanleida);
this.rule_quanleida.addEventListener(this.clickCB,this.rule_quanleida);
this.rule_teshupai=_aliased6738.getChildByName("rule_teshupai");
this.addListenerText(this.rule_teshupai);
this.rule_teshupai.addEventListener(this.clickCB,this.rule_teshupai);
this.rule_jiaheitao=_aliased6738.getChildByName("rule_jiaheitao");
this.addListenerText(this.rule_jiaheitao);
this.rule_jiaheitao.addEventListener(this.clickCB,this.rule_jiaheitao);
this.rule_jiahongtao=_aliased6738.getChildByName("rule_jiahongtao");
this.addListenerText(this.rule_jiahongtao);
this.rule_jiahongtao.addEventListener(this.clickCB,this.rule_jiahongtao);
this.rule_jiameihua=_aliased6738.getChildByName("rule_jiameihua");
this.addListenerText(this.rule_jiameihua);
this.rule_jiameihua.addEventListener(this.clickCB,this.rule_jiameihua);
this.rule_jiafangkuai=_aliased6738.getChildByName("rule_jiafangkuai");
this.addListenerText(this.rule_jiafangkuai);
this.rule_jiafangkuai.addEventListener(this.clickCB,this.rule_jiafangkuai);
this.rule_bipai=_aliased6738.getChildByName("rule_bipai");
this.addListenerText(this.rule_bipai);
this.rule_bipai.addEventListener(this.clickCB,this.rule_bipai);
this.rule_shuangwang=_aliased6738.getChildByName("rule_shuangwang");
this.addListenerText(this.rule_shuangwang);
this.rule_shuangwang.addEventListener(this.clickCB,this.rule_shuangwang);
_aliased4188=[];
_aliased4188.push(_aliased6738.getChildByName("cbx_ready_0"));
_aliased4188.push(_aliased6738.getChildByName("cbx_ready_1"));
this._startNode_radio=createRadioBoxForCheckBoxs(_aliased4188,function () { __FUNC_9__ }.bind(this));
this.addListenerText(_aliased4188,this._startNode_radio);
this.startNodeList=_aliased4188;
_local10=this.bgNode.getChildByName("bg_sangong");
this._zhuIdx=1;
this._ZhuNum=_local10.getChildByName("txt_fen");
if(this._ZhuNum){
this._ZhuNum.setString(this._zhuIdx);
this._Button_sub=_local10.getChildByName("btn_sub");
this._Button_sub.addTouchEventListener(function () { __FUNC_10__ },this);
this._Button_add=_local10.getChildByName("btn_add");
this._Button_add.addTouchEventListener(function () { __FUNC_11__ },this);
}
---------------------------------------
==========================================================================E
==================================4==================================S
------------------Argv------------------
index
---------------------------------------
----------------Content----------------
print(("" + index));
this.radioBoxSelectCB(index,_aliased3395[index],_aliased3395);
---------------------------------------
==========================================================================E
==================================5==================================S
------------------Argv------------------
index
---------------------------------------
----------------Content----------------
this.radioBoxSelectCB(index,_aliased6655[index],_aliased6655);
---------------------------------------
==========================================================================E
==================================6==================================S
------------------Argv------------------
index
---------------------------------------
----------------Content----------------
print(("" + index));
this.radioBoxSelectCB(index,_aliased5141[index],_aliased5141);
---------------------------------------
==========================================================================E
==================================7==================================S
------------------Argv------------------
index,bShowTuoGuanType
---------------------------------------
----------------Content----------------
_aliased1357.radioBoxSelectCB(index,_aliased9178[index],_aliased9178);
print(("" + index));
_local0=(index != 0);
_aliased6738.getChildByName("tuoguanType_0").visible=_local0;
_aliased6738.getChildByName("tuoguanType_1").visible=_local0;
_aliased6738.getChildByName("tuoguanType_2").visible=_local0;
---------------------------------------
==========================================================================E
==================================8==================================S
------------------Argv------------------
index
---------------------------------------
----------------Content----------------
this.radioBoxSelectCB(index,_aliased4298[index],_aliased4298);
---------------------------------------
==========================================================================E
==================================9==================================S
------------------Argv------------------
index
---------------------------------------
----------------Content----------------
this.radioBoxSelectCB(index,_aliased4188[index],_aliased4188);
---------------------------------------
==========================================================================E
==================================10==================================S
------------------Argv------------------
sender,type,step
---------------------------------------
----------------Content----------------
if((type == 2)){
if((this._zhuIdx <= 0.1)){
this._zhuIdx=11;
}
if((this._zhuIdx > 0)){
_local0=0.1;
if((this._zhuIdx > 1)){
_local0=1;
} else if((this._zhuIdx > 0.5)){
_local0=0.5;
}
this._zhuIdx=(this._zhuIdx - _local0);
this._zhuIdx=correctAccuracy(this._zhuIdx,5);
this._ZhuNum.setString(this._zhuIdx);
this._Button_add.setTouchEnabled(true);
this._Button_add.setBright(true);
this.setRoomCardModeFree();
}}
---------------------------------------
==========================================================================E
==================================11==================================S
------------------Argv------------------
sender,type,step
---------------------------------------
----------------Content----------------
if((type == 2)){
if((this._zhuIdx == 10)){
this._zhuIdx=0;
}
if((this._zhuIdx < 10)){
_local0=0.1;
if((this._zhuIdx >= 1)){
_local0=1;
} else if((this._zhuIdx >= 0.5)){
_local0=0.5;
}
this._zhuIdx=(this._zhuIdx + _local0);
this._zhuIdx=correctAccuracy(this._zhuIdx,5);
this._ZhuNum.setString(this._zhuIdx);
this._Button_sub.setTouchEnabled(true);
this._Button_sub.setBright(true);
this.setRoomCardModeFree();
}}
---------------------------------------
==========================================================================E
==================================12==================================S
------------------Argv------------------
isClub,nRenshuIdx,nLaiZiIdx,nWangCnt,nMaPaiIdx,isAntiLastJoin,isAntiCenterJoin,isDaQiangFanBei,isQuanLeiDa,isTeShuPai,isJiaHeiTao,isJiaHongTao,isJiaMeiHua,isJiaFangKuai,isBiPai,isShuangWang,isAntiDissRoom,_trustTime,_trustType,nStartModeIdx
---------------------------------------
----------------Content----------------
_local0=0;
if(isClub){
_local0=[2,4,6,8].indexOf(this.getNumberItem("maxPlayer",2));
} else {
}
_local0=util.localStorageEncrypt.getNumberItem(this.localStorageKey.KEY_thirteengx_renshu,0);
this._renshu_radio.selectItem(_local0);
this.radioBoxSelectCB(_local0,this.renshuNodeList[_local0],this.renshuNodeList);
_local1=0;
if(isClub){
_local1=[0,2,4,6,8].indexOf(this.getNumberItem("wangLaizi",0));
} else {
}
_local2=util.localStorageEncrypt.getNumberItem(this.localStorageKey.KEY_thirteengx_wanglai,0);
_local1=[0,2,4,6,8].indexOf(_local2);
this._laizi_radio.selectItem(_local1);
this.radioBoxSelectCB(_local1,this.laiziNodeList[_local1],this.laiziNodeList);
undefined;
if(isClub){
_local3=this.getNumberItem("mapai",0);
} else {
}
_local3=util.localStorageEncrypt.getNumberItem(this.localStorageKey.KEY_thirteengx_mapai,0);
this._play_mapai_radio.selectItem(_local3);
this.radioBoxSelectCB(_local3,this.mapaiNodeList[_local3],this.mapaiNodeList);
undefined;
if(isClub){
_local4=this.getBoolItem("antiLastJoin",false);
} else {
}
_local4=util.localStorageEncrypt.getBoolItem(this.localStorageKey.KEY_thirteengx_antiLastJoin,false);
this.rule_last_join.setSelected(_local4);
this.selectedCB(this.rule_last_join.getChildByName("text"),_local4);
undefined;
if(isClub){
_local5=this.getBoolItem("antiCenterJoin",false);
} else {
}
_local5=util.localStorageEncrypt.getBoolItem(this.localStorageKey.KEY_thirteengx_antiCenterJoin,false);
this.rule_anti_center_join.setSelected(_local5);
this.selectedCB(this.rule_anti_center_join.getChildByName("text"),_local5);
undefined;
if(isClub){
_local6=this.getBoolItem("tongguan",false);
} else {
}
_local6=util.localStorageEncrypt.getBoolItem(this.localStorageKey.KEY_thirteengx_daqiangfanbei,false);
this.rule_daqiangfanbei.setSelected(_local6);
this.selectedCB(this.rule_daqiangfanbei.getChildByName("text"),_local6);
undefined;
if(isClub){
_local7=this.getBoolItem("quanleida",false);
} else {
}
_local7=util.localStorageEncrypt.getBoolItem(this.localStorageKey.KEY_thirteengx_quanleida,false);
this.rule_quanleida.setSelected(_local7);
this.selectedCB(this.rule_quanleida.getChildByName("text"),_local7);
undefined;
if(isClub){
_local8=this.getBoolItem("teshupai",false);
} else {
}
_local8=util.localStorageEncrypt.getBoolItem(this.localStorageKey.KEY_thirteengx_teshu,false);
this.rule_teshupai.setSelected(_local8);
this.selectedCB(this.rule_teshupai.getChildByName("text"),_local8);
undefined;
if(isClub){
_local9=this.getBoolItem("jiaheitao",false);
} else {
}
_local9=util.localStorageEncrypt.getBoolItem(this.localStorageKey.KEY_thirteengx_jiaheitao,false);
this.rule_jiaheitao.setSelected(_local9);
this.selectedCB(this.rule_jiaheitao.getChildByName("text"),_local9);
_local10=this.bgNode.getChildByName("bg_sangong");
if(isClub){
_local10=this.getBoolItem("jiahongtao",false);
} else {
}
_local10=util.localStorageEncrypt.getBoolItem(this.localStorageKey.KEY_thirteengx_jiahongtao,false);
this.rule_jiahongtao.setSelected(_local10);
this.selectedCB(this.rule_jiahongtao.getChildByName("text"),_local10);
undefined;
if(isClub){
_local11=this.getBoolItem("jiameihua",false);
} else {
}
_local11=util.localStorageEncrypt.getBoolItem(this.localStorageKey.KEY_thirteengx_jiameihua,false);
this.rule_jiameihua.setSelected(_local11);
this.selectedCB(this.rule_jiameihua.getChildByName("text"),_local11);
undefined;
if(isClub){
_local12=this.getBoolItem("jiafangkuai",false);
} else {
}
_local12=util.localStorageEncrypt.getBoolItem(this.localStorageKey.KEY_thirteengx_jiafangkuai,false);
this.rule_jiafangkuai.setSelected(_local12);
this.selectedCB(this.rule_jiafangkuai.getChildByName("text"),_local12);
undefined;
if(isClub){
_local13=this.getBoolItem("bipai",false);
} else {
}
_local13=util.localStorageEncrypt.getBoolItem(this.localStorageKey.KEY_thirteengx_bipai,false);
this.rule_bipai.setSelected(_local13);
this.selectedCB(this.rule_bipai.getChildByName("text"),_local13);
undefined;
if(isClub){
_local14=this.getBoolItem("shuangwang",false);
} else {
}
_local14=util.localStorageEncrypt.getBoolItem(this.localStorageKey.KEY_thirteengx_shuangwang,false);
this.rule_shuangwang.setSelected(_local14);
this.selectedCB(this.rule_shuangwang.getChildByName("text"),_local14);
undefined;
if(isClub){
_local15=this.getBoolItem("antiDissRoom",false);
} else {
}
_local15=util.localStorageEncrypt.getBoolItem(this.localStorageKey.KEY_thirteengx_antiDissRoom,false);
this.rule_anti_dissroom.setSelected(_local15);
this.selectedCB(this.rule_anti_dissroom.getChildByName("text"),_local15);
undefined;
if(isClub){
_local16=[45,60,75,90,120,150].indexOf(this.getNumberItem("trustTime",1));
} else {
}
_local16=util.localStorageEncrypt.getNumberItem(this.localStorageKey.KEY_thirteengx_tuoguantime,1);
this._tuoguan_time_radio.selectItem(_local16);
this.radioBoxSelectCB(_local16,this.tuoguanTimeList[_local16],this.tuoguanTimeList);
this.tuoguantimeCallBack(_local16);
undefined;
if(isClub){
_local17=this.getNumberItem("trustWay",0);
} else {
}
_local17=util.localStorageEncrypt.getNumberItem(this.localStorageKey.KEY_thirteengx_tuoguantype,0);
this._tuoguan_type_radio.selectItem(_local17);
this.radioBoxSelectCB(_local17,this.tuoguanTypeList[_local17],this.tuoguanTypeList);
if(isClub){
this._zhuIdx=this.getNumberItem("difen",1);
} else {
}
this._zhuIdx=util.localStorageEncrypt.getNumberItem(this.localStorageKey.KEY_thirteengx_difen,1);
if(this._ZhuNum){
this._ZhuNum.setString((this._zhuIdx + ""));
}
undefined;
if(isClub){
_local18=this.getNumberItem("startmode",0);
} else {
}
_local18=util.localStorageEncrypt.getNumberItem(this.localStorageKey.KEY_thirteengx_startMode,0);
this.radioBoxSelectCB(_local18,this.startNodeList[_local18],this._startNode_radio);
---------------------------------------
==========================================================================E
==================================13==================================S
------------------Argv------------------
para,nPlayerIndex,nLaiZiIndex,nTuoguanIdx
---------------------------------------
----------------Content----------------
_local0={};
_local0.gameType=MjClient.GAME_TYPE.SHI_SAN_ZHANG_GX;
_local0.difen=this._zhuIdx;
_local1=this._renshu_radio.getSelectIndex();
_local0.maxPlayer=[2,4,6,8][(_local1 % 4)];
_local0.crazymode=false;
_local0.color=false;
_local0.mapai=this._play_mapai_radio.getSelectIndex();
_local2=this._laizi_radio.getSelectIndex();
_local0.wangLaizi=[0,2,4,6,8][(_local2 % 5)];
_local0.antiLastJoin=this.rule_last_join.isSelected();
_local0.antiCenterJoin=this.rule_anti_center_join.isSelected();
_local0.antiDissRoom=this.rule_anti_dissroom.isSelected();
_local0.tongguan=this.rule_daqiangfanbei.isSelected();
_local0.quanleida=this.rule_quanleida.isSelected();
_local0.teshupai=this.rule_teshupai.isSelected();
_local0.jiaheitao=this.rule_jiaheitao.isSelected();
_local0.jiahongtao=this.rule_jiahongtao.isSelected();
_local0.jiameihua=this.rule_jiameihua.isSelected();
_local0.jiafangkuai=this.rule_jiafangkuai.isSelected();
_local0.bipai=this.rule_bipai.isSelected();
_local0.shuangwang=this.rule_shuangwang.isSelected();
_local0.readytype=this._startNode_radio.getSelectIndex();
_local3=this._tuoguan_time_radio.getSelectIndex();
_local0.trustTime=[45,60,75,90,120,150][_local3];
_local0.trustWay=this._tuoguan_type_radio.getSelectIndex();
if(!(this._isFriendCard)){
util.localStorageEncrypt.setNumberItem(this.localStorageKey.KEY_thirteengx_wanglai,_local0.wangLaizi);
util.localStorageEncrypt.setNumberItem(this.localStorageKey.KEY_thirteengx_renshu,_local1);
util.localStorageEncrypt.setNumberItem(this.localStorageKey.KEY_thirteengx_tuoguantime,_local3);
util.localStorageEncrypt.setNumberItem(this.localStorageKey.KEY_thirteengx_tuoguantype,_local0.trustWay);
util.localStorageEncrypt.setNumberItem(this.localStorageKey.KEY_thirteengx_mapai,_local0.mapai);
util.localStorageEncrypt.setNumberItem(this.localStorageKey.KEY_thirteengx_difen,_local0.difen);
util.localStorageEncrypt.setNumberItem(this.localStorageKey.KEY_thirteengx_cardType,_local0.tongPaiXingDaXiao);
util.localStorageEncrypt.setBoolItem(this.localStorageKey.KEY_thirteengx_antiLastJoin,_local0.antiLastJoin);
util.localStorageEncrypt.setBoolItem(this.localStorageKey.KEY_thirteengx_antiCenterJoin,_local0.antiCenterJoin);
util.localStorageEncrypt.setBoolItem(this.localStorageKey.KEY_thirteengx_antiDissRoom,_local0.antiDissRoom);
util.localStorageEncrypt.setBoolItem(this.localStorageKey.KEY_thirteengx_daqiangfanbei,_local0.tongguan);
util.localStorageEncrypt.setBoolItem(this.localStorageKey.KEY_thirteengx_quanleida,_local0.quanleida);
util.localStorageEncrypt.setBoolItem(this.localStorageKey.KEY_thirteengx_teshu,_local0.teshupai);
util.localStorageEncrypt.setBoolItem(this.localStorageKey.KEY_thirteengx_jiaheitao,_local0.jiaheitao);
util.localStorageEncrypt.setBoolItem(this.localStorageKey.KEY_thirteengx_jiahongtao,_local0.jiahongtao);
util.localStorageEncrypt.setBoolItem(this.localStorageKey.KEY_thirteengx_jiameihua,_local0.jiameihua);
util.localStorageEncrypt.setBoolItem(this.localStorageKey.KEY_thirteengx_jiafangkuai,_local0.jiafangkuai);
util.localStorageEncrypt.setBoolItem(this.localStorageKey.KEY_thirteengx_bipai,_local0.bipai);
util.localStorageEncrypt.setBoolItem(this.localStorageKey.KEY_thirteengx_shuangwang,_local0.shuangwang);
util.localStorageEncrypt.setNumberItem(this.localStorageKey.KEY_thirteengx_startMode,_local0.readytype);
}
cc.log(("" + JSON.stringify(_local0)));
return _local0;
---------------------------------------
==========================================================================E
