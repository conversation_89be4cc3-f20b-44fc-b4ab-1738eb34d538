==================================0==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
function getTypeName( __ARGV_1__ ){ __FUNC_1__ }
var EndOneView_thirteenGX;
EndOneView_thirteenGX = cc.Layer.extend({jsBind:{block:{_layout:[[1,1],[0.5,0.5],[0,0],true]},back:{_layout:[[1,1],[0.538,0.5],[-0.035,0]],wintitle:{_visible:function () { __FUNC_2__ }},losetitle:{_visible:function () { __FUNC_3__ }},pingju:{_visible:function () { __FUNC_4__ }},xipai:{_click:function () { __FUNC_5__ },_visible:function () { __FUNC_7__ }},ready:{_click:function () { __FUNC_8__ },_visible:function () { __FUNC_9__ }},delText:{_run:function () { __FUNC_10__ }},info:{_visible:true,_text:function () { __FUNC_11__ }},dir:{_visible:true,_text:function () { __FUNC_12__ }}}},ctor:function () { __FUNC_13__ }});
---------------------------------------
==========================================================================E
==================================1==================================S
------------------Argv------------------
cardType,szTypeName
---------------------------------------
----------------Content----------------
_local0="";
if((cardType == 0)){
_local0="cardType_1.png";
} else if((cardType == 1)){
_local0="cardType_2.png";
} else if((cardType == 2)){
_local0="cardType_3.png";
} else if((cardType == 3)){
_local0="cardType_7.png";
} else if((cardType == 4)){
_local0="cardType_8.png";
} else if((cardType == 5)){
_local0="cardType_8.png";
} else if((cardType == 6)){
_local0="cardType_8.png";
} else if((cardType == 7)){
_local0="cardType_11.png";
} else if((cardType == 8)){
_local0="cardType_12.png";
} else if((cardType == 9)){
_local0="cardType_13.png";
} else if((cardType == 10)){
_local0="cardType_14.png";
} else if((cardType == -1)){
_local0="cardType_0.png";
}
return _local0;
---------------------------------------
==========================================================================E
==================================2==================================S
------------------Argv------------------
pl
---------------------------------------
----------------Content----------------
_local0=getUIPlayer(0);
if(_local0){
return (_local0.winone > 0);
}
return false;
---------------------------------------
==========================================================================E
==================================3==================================S
------------------Argv------------------
pl
---------------------------------------
----------------Content----------------
_local0=getUIPlayer(0);
if(_local0){
return (_local0.winone < 0);
}
return false;
---------------------------------------
==========================================================================E
==================================4==================================S
------------------Argv------------------
pl
---------------------------------------
----------------Content----------------
_local0=getUIPlayer(0);
if(_local0){
return (_local0.winone == 0);
}
return false;
---------------------------------------
==========================================================================E
==================================5==================================S
------------------Argv------------------
btn,eT
---------------------------------------
----------------Content----------------
playEffectInPlay_thirteenGX("sound-button");
MjClient.gamenet.request("pkroom.handler.tableMsg",{cmd:"MJShuffle"},function () { __FUNC_6__ });
---------------------------------------
==========================================================================E
==================================6==================================S
------------------Argv------------------
data
---------------------------------------
----------------Content----------------
if(data&&(data.code == -1)){
MjClient.showToast(data.message);
return undefined;
}
postEvent("clearCardUI");
MjClient.endoneui.removeFromParent(true);
MjClient.endoneui=null;
MjClient.gamenet.request("pkroom.handler.tableMsg",{cmd:"MJPass"});
---------------------------------------
==========================================================================E
==================================7==================================S
------------------Argv------------------
tData
---------------------------------------
----------------Content----------------
_local0=MjClient.data.sData.tData;
return !(MjClient.remoteCfg.guestLogin) && !(_local0.matchId);
---------------------------------------
==========================================================================E
==================================8==================================S
------------------Argv------------------
btn,eT,sData,tData
---------------------------------------
----------------Content----------------
playEffectInPlay_thirteenGX("sound-button");
_local0=MjClient.data.sData;
_local1=_local0.tData;
postEvent("clearCardUI");
MjClient.endoneui.removeFromParent(true);
MjClient.endoneui=null;
if((MjClient.rePlayVideo >= 0)&&MjClient.replayui){
MjClient.replayui.replayEnd();
} else {
}
MjClient.gamenet.request("pkroom.handler.tableMsg",{cmd:"MJPass"});
---------------------------------------
==========================================================================E
==================================9==================================S
------------------Argv------------------
tData
---------------------------------------
----------------Content----------------
_local0=MjClient.data.sData.tData;
return !(_local0.matchId);
---------------------------------------
==========================================================================E
==================================10==================================S
------------------Argv------------------
sData,tData,id,pl,delStr
---------------------------------------
----------------Content----------------
if(MjClient.isDismiss){
_local0=MjClient.data.sData;
_local1=_local0.tData;
_local2=_local1.firstDel;
_local3=_local0.players[_local2];
_local4="";
if(!(_local3)){
_local3=getUIPlayer(0);
if(_local3){
_local4=_local3.mjdesc[0];
}}
this.setString(_local4);
} else {
}
this.setString("");
---------------------------------------
==========================================================================E
==================================11==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
if(MjClient.data.sData.tData.fieldId){
return "";
} else {
}
return getPlayingRoomInfo(5);
---------------------------------------
==========================================================================E
==================================12==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
if(MjClient.data.sData.tData.fieldId){
return "";
} else {
}
return getPlayingRoomInfo(0);
---------------------------------------
==========================================================================E
==================================13==================================S
------------------Argv------------------
msg,endoneui,_back,_time,tData,nReadyTime,_txtTime,tipCountDown,_listView,_headItem,players,playerKeyArr,newPlayerKeyArr,i,nWidth,nHeight,player,headItem,ccNick,_nameStr,ccID,ccWinNum,pre,ccStandNode,ccImgCardType1,newCards,cardType,szTypeName,ccImgCardType2,ccImgCardType3
---------------------------------------
----------------Content----------------
this._super();
_local0=ccs.load("endOne_thirteenGX.json");
BindUiAndLogic(_local0.node,this.jsBind);
this.addChild(_local0.node);
_local1=_local0.node.getChildByName("back");
_local2=_local1.getChildByName("time");
_local2.visible=true;
_local2.setString(MjClient.roundEndTime);
MjClient.endoneui=this;
_local3=MjClient.data.sData.tData;
_local4=_local3.areaSelectMode.readytime;
_aliased9429=_local1.getChildByName("img_clock").getChildByName("time");
_aliased9429.unscheduleAllCallbacks();
_aliased9429.getParent().visible=true;
_aliased9429.visible=true;
_aliased7678=(_local4 - 1);
_aliased9429.setString(_aliased7678);
_aliased9429.schedule(function () { __FUNC_14__ },1,cc.REPEAT_FOREVER,0);
print(("" + _local4));
_local7=_local1.getChildByName("ListView");
_local8=_local1.getChildByName("headItem");
_local9=MjClient.data.sData.players;
_local10=Object.keys(_local9);
_local11=[];
_local12=0;
while((_local12 < _local10.length)){
if((_local3.spectatorUids.indexOf(Number(_local10[_local12])) == -1)){
_local11.push(_local10[_local12]);
}
_local12=(_local12 + 1);
_local12=0;
}
_local13=(_local7.getContentSize().width * (_local11.length / 8));
_local14=_local7.getContentSize().height;
_local7.setContentSize(cc.size(_local13,_local14));
print(("" + JSON.stringify(_local3.spectatorUids)));
print(((_local11.length + "") + JSON.stringify(_local11)));
_local12=0;
break;
while((_local12 < _local11.length)){
_local15=_local9[_local11[_local12]];
print(((_local11[_local12] + "") + JSON.stringify(_local15)));
_local16=_local8.clone();
if((_local15.info.uid == SelfUid())){
_local16.loadTexture("poker/shisanzhang/endOne/itemBg3.png");
}
_local17=_local16.getChildByName("head").getChildByName("name");
_local17.ignoreContentAdaptWithSize(true);
_local18=(unescape(_local15.info.nickname) + "");
_local17.setString(getNewName(_local18));
_local19=_local16.getChildByName("head").getChildByName("id");
_local19.ignoreContentAdaptWithSize(true);
_local19.setString(("ID:" + _local15.info.uid.toString()));
_local20=_local16.getChildByName("winNum");
_local20.ignoreContentAdaptWithSize(true);
_local21="";
if((_local15.winone > 0)){
_local21="+";
}
_local20.setString((_local21 + _local15.winone));
CircularCuttingHeadImg(_local16.getChildByName("head"),_local15);
_local22=_local16.getChildByName("stand_node");
_local29=0;
while((_local29 < 3)){
createNewCard_thirteenGX(_local22,("hcard1" + (_local29 + 1)),_local15.fristCard[_local29]);
_local29=(_local29 + 1);
_local29=0;
}
}
        
_local29=0;
while((_local29 < 5)){
createNewCard_thirteenGX(_local22,("hcard2" + (_local29 + 1)),_local15.secendCard[_local29]);
_local29=(_local29 + 1);
_local29=0;
}
}
        
_local29=0;
while((_local29 < 5)){
createNewCard_thirteenGX(_local22,("hcard3" + (_local29 + 1)),_local15.lastCard[_local29]);
_local29=(_local29 + 1);
_local29=0;
}
}
        
_local23=_local22.getChildByName("img_cardType_1");
_local24=exchangeTo16Data_thirteenGX(_local15.fristCard);
_local25=MjClient.poker_thirteenGX.CheckCardType(_local24);
_local26=getTypeName(_local25);
_local23.loadTexture(("poker/shisanzhang/endOne/" + _local26));
_local27=_local22.getChildByName("img_cardType_2");
_local24=exchangeTo16Data_thirteenGX(_local15.secendCard);
_local25=MjClient.poker_thirteenGX.CheckCardType(_local24);
_local26=getTypeName(_local25);
_local27.loadTexture(("poker/shisanzhang/endOne/" + _local26));
_local28=_local22.getChildByName("img_cardType_3");
_local24=exchangeTo16Data_thirteenGX(_local15.lastCard);
_local25=MjClient.poker_thirteenGX.CheckCardType(_local24);
_local26=getTypeName(_local25);
_local28.loadTexture(("poker/shisanzhang/endOne/" + _local26));
_local7.pushBackCustomItem(_local16);
_local12=(_local12 + 1);
_local12=0;
}
UIEventBind(null,this,"doCompareCard",function () { __FUNC_15__ },this);
return true;
---------------------------------------
==========================================================================E
==================================14==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
if((_aliased7678 > 0)){
_aliased6114=(_aliased7678 - 1);
}
if((_aliased6114 <= 0)){
_aliased9429.getParent().visible=false;
_aliased9429.setVisible(false);
_aliased9429.unscheduleAllCallbacks();
postEvent("clearCardUI");
MjClient.endoneui.removeFromParent(true);
MjClient.endoneui=null;
} else {
}
_aliased9429.setString(_aliased6114);
---------------------------------------
==========================================================================E
==================================15==================================S
------------------Argv------------------
argDict
---------------------------------------
----------------Content----------------
this.removeFromParent();
---------------------------------------
==========================================================================E
