==================================0==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
var RankLayer_thirteenGX;
RankLayer_thirteenGX = cc.Layer.extend({ctor:function () { __FUNC_1__ },OnShow:function () { __FUNC_26__ },SetStartInit:function () { __FUNC_27__ },CallEverySecond:function () { __FUNC_28__ },TipBaiPai:function () { __FUNC_29__ },cardType2Name:function () { __FUNC_31__ },QuickSetDun:function () { __FUNC_32__ },InitCardPos:function () { __FUNC_33__ },NoticeSpecial:function () { __FUNC_34__ },ShowAllPlayerCard:function () { __FUNC_35__ },DealCardEffect:function () { __FUNC_37__ },ShowCard:function () { __FUNC_38__ },GetPokeCard:function () { __FUNC_39__ },disabledBtn:function () { __FUNC_40__ },UpdateScelectBtn:function () { __FUNC_41__ },isBtnHide:function () { __FUNC_42__ },ExchangeDun2To3:function () { __FUNC_43__ },reSortCards:function () { __FUNC_44__ },reSortCardsByColor:function () { __FUNC_45__ },GetSortCardsByColor:function () { __FUNC_46__ },PaiDunHuHuanPai:function () { __FUNC_48__ },RestDunCard:function () { __FUNC_49__ },HuHuanPai:function () { __FUNC_50__ },GetCardType:function () { __FUNC_51__ },ClearNodeSprite:function () { __FUNC_52__ },Click_card:function () { __FUNC_53__ },Click_dun:function () { __FUNC_54__ }});
---------------------------------------
==========================================================================E
==================================1==================================S
------------------Argv------------------
specialType,UI,block,back,nodeDun,node_btn_select,node_btn_unSelect,btnDownCards,btnCloseDun1,btnCloseDun2,btnCloseDun3,btnDun,btnCard
---------------------------------------
----------------Content----------------
this._super();
MjClient.rankLayer=this;
this._specialType=specialType;
_local0=ccs.load("thirteenGX_Rank.json");
this.addChild(_local0.node);
UIEventBind(null,this,"doCompareCard",function () { __FUNC_2__ },this);
UIEventBind(null,this,"roundEnd",function () { __FUNC_3__ },this);
UIEventBind(null,this,"endRoom",function () { __FUNC_4__ },this);
this.LogicRank=MjClient.rank_thirteenGX;
this.LogicGame=MjClient.poker_thirteenGX;
this.shouCardList=[];
_local1=_local0.node.getChildByName("block");
setWgtLayout(_local1,[1,1],[0.5,0.5],[0,0],true);
_local2=_local0.node.getChildByName("back");
setWgtLayout(_local2,[1,1],[0.5,0.5],[0,0]);
this.back=_local2;
this.node_btn_select=_local2.getChildByName("node_btn_select");
this.node_btn_unSelect=_local2.getChildByName("node_btn_unSelect");
_local3=_local2.getChildByName("bg_dun");
_local3.setSwallowTouches(false);
_local12=1;
while((_local12 <= 13)){
this[("btnDun" + _local12)]=_local3.getChildByName(("btnDun" + _local12));
_local12=(_local12 + 1);
_local12=1;
}
}
        
_local12=1;
while((_local12 <= 3)){
this[("btnCloseDun" + _local12)]=_local3.getChildByName(("btnCloseDun" + _local12));
this[("lb_cardType" + _local12)]=_local3.getChildByName(("lb_cardType" + _local12));
_local12=(_local12 + 1);
_local12=1;
}
}
        
_local4=_local2.getChildByName("node_btn_select");
this.check_duizi=_local4.getChildByName("check_duizi");
this.check_duizi.addTouchEventListener(function () { __FUNC_5__ },this);
this.check_liangdui=_local4.getChildByName("check_liangdui");
this.check_liangdui.addTouchEventListener(function () { __FUNC_6__ },this);
this.check_santiao=_local4.getChildByName("check_santiao");
this.check_santiao.addTouchEventListener(function () { __FUNC_7__ },this);
this.check_shunzi=_local4.getChildByName("check_shunzi");
this.check_shunzi.addTouchEventListener(function () { __FUNC_8__ },this);
this.check_tonghua=_local4.getChildByName("check_tonghua");
this.check_tonghua.addTouchEventListener(function () { __FUNC_9__ },this);
this.check_hulu=_local4.getChildByName("check_hulu");
this.check_hulu.addTouchEventListener(function () { __FUNC_10__ },this);
this.check_zhadang=_local4.getChildByName("check_zhadang");
this.check_zhadang.addTouchEventListener(function () { __FUNC_11__ },this);
this.check_tonghuashun=_local4.getChildByName("check_tonghuashun");
this.check_tonghuashun.addTouchEventListener(function () { __FUNC_12__ },this);
this.check_wutong=_local4.getChildByName("check_wutong");
this.check_wutong.addTouchEventListener(function () { __FUNC_13__ },this);
_local5=_local2.getChildByName("node_btn_unSelect");
this.no_duizi=_local5.getChildByName("no_duizi");
this.no_liangdui=_local5.getChildByName("no_liangdui");
this.no_santiao=_local5.getChildByName("no_santiao");
this.no_shunzi=_local5.getChildByName("no_shunzi");
this.no_tonghua=_local5.getChildByName("no_tonghua");
this.no_hulu=_local5.getChildByName("no_hulu");
this.no_zhadang=_local5.getChildByName("no_zhadang");
this.no_tonghuashun=_local5.getChildByName("no_tonghuashun");
this.no_wutong=_local5.getChildByName("no_wutong");
this.lbl_count=_local2.getChildByName("sp_clock").getChildByName("lbl_count");
this.lb_cardType1=_local2.getChildByName("bg_dun").getChildByName("lb_cardType1");
this.lb_cardType2=_local2.getChildByName("bg_dun").getChildByName("lb_cardType2");
this.lb_cardType3=_local2.getChildByName("bg_dun").getChildByName("lb_cardType3");
this.card=_local2.getChildByName("card");
this.card.setSwallowTouches(false);
this.cardPrefab=_local0.node.getChildByName("cardPrefab");
this.cardPrefab.visible=false;
this.cardPrefab2=_local0.node.getChildByName("cardPrefab2");
this.cardPrefab2.visible=false;
_local6=_local2.getChildByName("BtnDownCards");
_local6.addTouchEventListener(function () { __FUNC_14__ },this);
_local7=_local3.getChildByName("btnCloseDun1");
_local7.addTouchEventListener(function () { __FUNC_15__ },this);
_local8=_local3.getChildByName("btnCloseDun2");
_local8.addTouchEventListener(function () { __FUNC_16__ },this);
_local9=_local3.getChildByName("btnCloseDun3");
_local9.addTouchEventListener(function () { __FUNC_17__ },this);
this.btnOK=_local2.getChildByName("btnOK");
this.btnOK.addTouchEventListener(function () { __FUNC_18__ },this);
this.btnCancel=_local2.getChildByName("btnCancel");
this.btnCancel.addTouchEventListener(function () { __FUNC_19__ },this);
this.btnClose=_local2.getChildByName("btnClose");
this.btnClose.visible=false;
this.btnClose.addTouchEventListener(function () { __FUNC_20__ },this);
this.btnQuick=_local2.getChildByName("btnQuick");
this.btnQuick.visible=false;
this.btnQuick.addTouchEventListener(function () { __FUNC_21__ },this);
this.check_Special=_local2.getChildByName("check_special");
this.check_Special.addTouchEventListener(function () { __FUNC_22__ },this);
this.check_Special.visible=false;
UIEventBind(null,this,"EVT_DUN_UPDATE",function () { __FUNC_23__ },this);
this.isBtnHide(false);
_local3=_local2.getChildByName("bg_dun");
_local12=1;
while((_local12 <= 13)){
_local13=undefined;
_local13=null;
_local10=_local3.getChildByName(("btnDun" + _local12));
_local13=this.cardPrefab.clone();
_local13.setName("cardPrefab");
_local10.addChild(_local13);
_local13.setPosition((_local10.width * 0.5),(_local10.height * 0.5));
_local10.addTouchEventListener(function () { __FUNC_24__ },this);
_local11=this.card.getChildByName(("btn_" + _local12));
_local13=this.cardPrefab2.clone();
_local13.setName("cardPrefab2");
_local11.addChild(_local13);
_local13.setPosition((_local11.width * 0.5),(_local11.height * 0.5));
_local11.tag=_local12;
_local11.addTouchEventListener(function () { __FUNC_25__ },this);
}
        
_local12=(_local12 + 1);
_local12=1;
}
}
        
this.btnDunToDun={btnDun1:"DUN1",btnDun2:"DUN1",btnDun3:"DUN1",btnDun4:"DUN2",btnDun5:"DUN2",btnDun6:"DUN2",btnDun7:"DUN2",btnDun8:"DUN2",btnDun9:"DUN3",btnDun10:"DUN3",btnDun11:"DUN3",btnDun12:"DUN3",btnDun13:"DUN3"};
this.btnDunToDunNum={btnDun1:0,btnDun2:1,btnDun3:2,btnDun4:0,btnDun5:1,btnDun6:2,btnDun7:3,btnDun8:4,btnDun9:0,btnDun10:1,btnDun11:2,btnDun12:3,btnDun13:4};
this.clickDunDict={};
_local12=0;
while((_local12 < 13)){
this[("btnDun" + (_local12 + 1))].clickNum=0;
this.clickDunDict[("btnDun" + (_local12 + 1))]=[];
this.clickDunDict[("btnDun" + (_local12 + 1))].push(this.btnDunToDun[("btnDun" + (_local12 + 1))],this.btnDunToDunNum[("btnDun" + (_local12 + 1))],this[("btnDun" + (_local12 + 1))],false);
_local12=(_local12 + 1);
_local12=0;
}
}
        
---------------------------------------
==========================================================================E
==================================2==================================S
------------------Argv------------------
argDict
---------------------------------------
----------------Content----------------
print("");
this.removeFromParent();
---------------------------------------
==========================================================================E
==================================3==================================S
------------------Argv------------------
argDict
---------------------------------------
----------------Content----------------
this.removeFromParent();
---------------------------------------
==========================================================================E
==================================4==================================S
------------------Argv------------------
argDict
---------------------------------------
----------------Content----------------
this.removeFromParent();
---------------------------------------
==========================================================================E
==================================5==================================S
------------------Argv------------------
sender,type
---------------------------------------
----------------Content----------------
if((type == 2)){
this.LogicRank.CheckDuiZi();
}
---------------------------------------
==========================================================================E
==================================6==================================S
------------------Argv------------------
sender,type
---------------------------------------
----------------Content----------------
if((type == 2)){
this.LogicRank.CheckLiangDui();
}
---------------------------------------
==========================================================================E
==================================7==================================S
------------------Argv------------------
sender,type
---------------------------------------
----------------Content----------------
if((type == 2)){
this.LogicRank.CheckSanTiao();
}
---------------------------------------
==========================================================================E
==================================8==================================S
------------------Argv------------------
sender,type
---------------------------------------
----------------Content----------------
if((type == 2)){
this.LogicRank.CheckShunzi();
}
---------------------------------------
==========================================================================E
==================================9==================================S
------------------Argv------------------
sender,type
---------------------------------------
----------------Content----------------
if((type == 2)){
this.LogicRank.CheckTonghua();
}
---------------------------------------
==========================================================================E
==================================10==================================S
------------------Argv------------------
sender,type
---------------------------------------
----------------Content----------------
if((type == 2)){
this.LogicRank.CheckHulu();
}
---------------------------------------
==========================================================================E
==================================11==================================S
------------------Argv------------------
sender,type
---------------------------------------
----------------Content----------------
if((type == 2)){
this.LogicRank.CheckZhaDang();
}
---------------------------------------
==========================================================================E
==================================12==================================S
------------------Argv------------------
sender,type
---------------------------------------
----------------Content----------------
if((type == 2)){
this.LogicRank.CheckTongHuaShun();
}
---------------------------------------
==========================================================================E
==================================13==================================S
------------------Argv------------------
sender,type
---------------------------------------
----------------Content----------------
if((type == 2)){
this.LogicRank.CheckWuTong();
}
---------------------------------------
==========================================================================E
==================================14==================================S
------------------Argv------------------
sender,type
---------------------------------------
----------------Content----------------
if((type == 2)){
this.LogicRank.clearSelectedCards();
}
---------------------------------------
==========================================================================E
==================================15==================================S
------------------Argv------------------
sender,type
---------------------------------------
----------------Content----------------
if((type == 2)){
if(this.LogicRank.getDunListByType("DUN1").length){
this.LogicRank.ClearDun("DUN1",true);
this.reSortCards();
this.UpdateScelectBtn();
}}
---------------------------------------
==========================================================================E
==================================16==================================S
------------------Argv------------------
sender,type
---------------------------------------
----------------Content----------------
if((type == 2)){
if(this.LogicRank.getDunListByType("DUN2").length){
this.LogicRank.ClearDun("DUN2",true);
this.reSortCards();
this.UpdateScelectBtn();
}}
---------------------------------------
==========================================================================E
==================================17==================================S
------------------Argv------------------
sender,type
---------------------------------------
----------------Content----------------
if((type == 2)){
if(this.LogicRank.getDunListByType("DUN3").length){
this.LogicRank.ClearDun("DUN3",true);
this.reSortCards();
this.UpdateScelectBtn();
}}
---------------------------------------
==========================================================================E
==================================18==================================S
------------------Argv------------------
sender,type
---------------------------------------
----------------Content----------------
if((type == 2)){
_local2=undefined;
_local1=undefined;
_local0=undefined;
_local0=this.LogicRank.getDunListByType("DOWN");
_local1=(this.publicCardId == _local0[0]);
_local2=this.LogicRank.CheckAllRanked(_local1);
if(_local2){
_local3=undefined;
_local3=this.LogicRank.SendRankList(0);
print("");
if(_local3){
this.removeFromParent();
}
        
}
} else {
}
MjClient.showToast("还没排序好");
}
        
}
---------------------------------------
==========================================================================E
==================================19==================================S
------------------Argv------------------
sender,type
---------------------------------------
----------------Content----------------
if((type == 2)){
this.LogicRank.ClearDun("DUN1",true);
this.LogicRank.ClearDun("DUN2",true);
this.LogicRank.ClearDun("DUN3",true);
this.reSortCards();
this.UpdateScelectBtn();
}
---------------------------------------
==========================================================================E
==================================20==================================S
------------------Argv------------------
sender,type
---------------------------------------
----------------Content----------------
if((type == 2)){
this.removeFromParent();
}
---------------------------------------
==========================================================================E
==================================21==================================S
------------------Argv------------------
sender,type
---------------------------------------
----------------Content----------------
if((type == 2)){
this.QuickSetDun(0);
}
---------------------------------------
==========================================================================E
==================================22==================================S
------------------Argv------------------
sender,type
---------------------------------------
----------------Content----------------
if((type == 2)){
_local0=undefined;
this.QuickSetDun(0);
_local0=this.LogicRank.SendRankList(this._specialType);
print("");
if(_local0){
this.removeFromParent();
}
        
}
}
---------------------------------------
==========================================================================E
==================================23==================================S
------------------Argv------------------
argDict
---------------------------------------
----------------Content----------------
this.ShowAllPlayerCard(argDict);
---------------------------------------
==========================================================================E
==================================24==================================S
------------------Argv------------------
sender,type
---------------------------------------
----------------Content----------------
if((type == 2)){
this.Click_dun(sender.getName(),sender);
}
---------------------------------------
==========================================================================E
==================================25==================================S
------------------Argv------------------
sender,type
---------------------------------------
----------------Content----------------
if((type == 2)){
this.Click_card(sender.getName(),sender);
print("");
}
---------------------------------------
==========================================================================E
==================================26==================================S
------------------Argv------------------
shouCardList,nCountDown,newPorkList,SPECIALCARDTPYE,szSpecialName
---------------------------------------
----------------Content----------------
print(("" + JSON.stringify(shouCardList)));
_local0=this.LogicGame.DeepCopy(shouCardList);
shouCardList = this.LogicGame.GetSortCardsEx(_local0);
print(("" + JSON.stringify(shouCardList)));
this.shouCardList=shouCardList;
this.cardCount=13;
this.countDown=nCountDown||60;
this.lastShouCard=[];
this.SetStartInit(shouCardList);
this.lbl_count.setString(("" + this.countDown));
this.schedule(this.CallEverySecond,1);
this.DealCardEffect();
this.TipBaiPai(shouCardList);
this.lb_cardType1.visible=false;
this.lb_cardType2.visible=false;
this.lb_cardType3.visible=false;
this.outDownCardList=[];
this.ChangeDun="";
this.ChangeCard="";
if((this._specialType > 0)){
_local1={0:"非特殊牌",17:"",18:"",19:"",20:"",21:"",22:"半大",23:"",24:"六对半",25:"",26:"",27:"",28:"",29:"全黑",30:"全红",31:"",32:"",33:"全大",34:"",35:"",36:"",37:"",38:"",39:"",40:"",41:"十二皇族",42:"",43:"",44:""};
this.check_Special.visible=true;
_local2=_local1[this._specialType]||"非特殊牌";
this.check_Special.getChildByName("Text").setString(_local2);
}
---------------------------------------
==========================================================================E
==================================27==================================S
------------------Argv------------------
shouCardList
---------------------------------------
----------------Content----------------
this.card.visible=true;
this.LogicRank.InitDunState(shouCardList);
this.card.children[(this.card.children.length - 1)].visible=false;
this.card.children[(this.card.children.length - 2)].visible=false;
this.card.children[(this.card.children.length - 3)].visible=false;
this.cardCount=13;
this.InitCardPos();
this.ClearNodeSprite();
this.UpdateScelectBtn();
this.disabledBtn();
this.ChangeDun="";
this.ChangeCard="";
---------------------------------------
==========================================================================E
==================================28==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
this.countDown=(this.countDown - 1);
this.lbl_count.setString(("" + this.countDown));
if((this.countDown <= 0)){
this.lbl_count.setString("0");
this.unschedule(this.CallEverySecond);
if((this.DataList.length > 0)){
_local0=undefined;
this.QuickSetDun(0);
_local0=this.LogicRank.SendRankList(this._specialType);
print("");
if(_local0){
this.removeFromParent();
}
        
}
}}
---------------------------------------
==========================================================================E
==================================29==================================S
------------------Argv------------------
shouCardList
---------------------------------------
----------------Content----------------
this.quick=this.back.getChildByName("quick");
this.quick.setSwallowTouches(false);
this.DataList=this.LogicGame.GetAllCardType(shouCardList);
_local0=0;
while((_local0 < 5)){
_local1=undefined;
_local1=this.quick.getChildByName("layout").getChildByName(("btn_quick" + (_local0 + 1)));
_local1.visible=false;
}
        
_local0=(_local0 + 1);
_local0=0;
}
}
        
if((this.DataList[0][0].cardList.length == 5) && (this.DataList[0][1].cardList.length == 5) && (this.DataList[0][2].cardList.length == 3)){
this.btnQuick.visible=true;
}
_local0=0;
while((_local0 < this.DataList.length)){
_local1=undefined;
_local1=this.quick.getChildByName("layout").getChildByName(("btn_quick" + (_local0 + 1)));
cc.log((_local0 + ""),JSON.stringify(this.DataList[_local0]));
if((this.DataList[_local0][0].cardList.length == 5) && (this.DataList[_local0][1].cardList.length == 5) && (this.DataList[_local0][2].cardList.length == 3)){
_local4=undefined;
_local3=undefined;
_local2=undefined;
_local1.visible=true;
_local2=this.DataList[_local0][0].cardType;
_local3=this.DataList[_local0][1].cardType;
_local4=this.DataList[_local0][2].cardType;
_local1.getChildByName("lb3").setString(this.cardType2Name(_local2));
_local1.getChildByName("lb2").setString(this.cardType2Name(_local3));
_local1.getChildByName("lb1").setString(this.cardType2Name(_local4));
_local1.tag=_local0;
_local1.addTouchEventListener(function () { __FUNC_30__ },this);
}
        
}
        
}
_local0=(_local0 + 1);
_local0=0;
}
}
        
---------------------------------------
==========================================================================E
==================================30==================================S
------------------Argv------------------
sender,type
---------------------------------------
----------------Content----------------
if((type == 2)){
_local0=undefined;
_local0=sender.tag;
this.QuickSetDun(_local0);
}
        
}
---------------------------------------
==========================================================================E
==================================31==================================S
------------------Argv------------------
cardType
---------------------------------------
----------------Content----------------
if((cardType == 10)){
return "";
} else if((cardType == 9)){
return "";
} else if((cardType == 8)){
return "铁支";
} else if((cardType == 7)){
return "葫芦";
} else if((cardType == 4)){
return "";
} else if((cardType == 3)){
return "顺子";
} else if((cardType == 2)){
return "";
} else if((cardType == 1)){
return "两对";
} else if((cardType == 0)){
return "对子";
}
return "乌龙";
---------------------------------------
==========================================================================E
==================================32==================================S
------------------Argv------------------
quickID,downList,key,dun3,dun2,dun1,argDict,nIdx
---------------------------------------
----------------Content----------------
_local0=this.shouCardList;
this.LogicRank.cardStateList.DOWN=_local0;
this.reSortCards();
_local1=parseInt(quickID);
if((_local1 < 0)){
_local1=0;
}
_local2=[].concat(this.DataList[_local1][0].cardList);
_local3=[].concat(this.DataList[_local1][1].cardList);
_local4=[].concat(this.DataList[_local1][2].cardList);
this.LogicRank.QuickSetDunEx("DUN3",_local2);
this.LogicRank.QuickSetDunEx("DUN2",_local3);
this.LogicRank.QuickSetDunEx("DUN1",_local4);
this.LogicRank.cardStateList.SELECTED=[];
_local5={publicID:""};
if((this.publicCardId != "")){
_local7=undefined;
_local7=[].concat(_local4,_local3,_local2);
_local8=0;
while((_local8 < _local0.length)){
_local9=undefined;
_local9=_local0[_local8];
if((_local7.indexOf(_local9) > -1)){
_local6=this.LogicRank.cardStateList.DOWN.indexOf(_local9);
if((_local6 > -1)){
this.LogicRank.cardStateList.DOWN.splice(_local6,1);
}
        
}}
_local8=(_local8 + 1);
_local8=0;
}
}
        
}
        
} else {
}
this.LogicRank.cardStateList.DOWN=[];
this.ShowAllPlayerCard(_local5);
this.disabledBtn();
---------------------------------------
==========================================================================E
==================================33==================================S
------------------Argv------------------
lastX
---------------------------------------
----------------Content----------------
this.ChooseCardAddOffY=45;
this.InitBtnCardPosY=70;
this.cardSpcedX=0;
this.fristCardX=0;
this.startIndex=-1;
this.endIndex=-1;
_local0=-1;
_local1=1;
while((_local1 <= 13)){
_local2=undefined;
_local2=this.card.getChildByName(("btn_" + (_local1 + 1)));
if(_local2){
_local2.isMoveEnter=false;
if((1 == _local1)){
this.fristCardX=_local2.x;
}
if((2 == _local1)){
this.cardSpcedX=(_local2.x - _local0);
if((this.cardSpcedX < 0)){
this.cardSpcedX=(- this.cardSpcedX);
}}
_local0=_local2.x;
}
        
}
_local1=(_local1 + 1);
_local1=1;
}
}
        
---------------------------------------
==========================================================================E
==================================34==================================S
------------------Argv------------------
setPosList,special_btn1,special_btn2,special_btn3
---------------------------------------
----------------Content----------------
_local0=this.RoomSet.GetRoomSetProperty("setPosList");
_local1=this.GetWndNode("special_btn1");
_local2=this.GetWndNode("special_btn2");
_local3=this.GetWndNode("special_btn3");
_local1.removeAllChildren();
_local2.removeAllChildren();
_local3.removeAllChildren();
_local4=0;
break;
while((_local4 < _local0.length)){
_local6=undefined;
_local5=undefined;
_local5=_local0[_local4];
_local6=_local5.posID;
if((this.clientPos == _local6)){
_local7=undefined;
_local7=_local5.special;
if((_local7.length > 0)){
_local11=undefined;
_local10=undefined;
_local9=undefined;
_local8=undefined;
_local8="special_btn1/";
_local9="special_btn2/";
_local10="special_btn3/";
_local11=_local7.length;
_local12=0;
while((_local12 < _local11)){
_local16=undefined;
_local15=undefined;
_local14=undefined;
_local13=undefined;
_local13=_local7[_local12];
_local14=_local13.specialType;
_local15=(_local8 + _local14);
if((_local11 < 9)){
_local15=(_local8 + _local14);
} else if((_local11 > 8)&&(_local11 < 17)){
_local15=(_local9 + _local14);
} else {
}
_local15=(_local10 + _local14);
_local16=this.GetWndNode(_local15);
this.SetWndProperty(_local15,"visible",true);
_local16.specialData=_local13;
}
        
_local12=(_local12 + 1);
_local12=0;
}
}
        
}
        
}
        
}
}
        
}
_local4=(_local4 + 1);
_local4=0;
}
}
        
---------------------------------------
==========================================================================E
==================================35==================================S
------------------Argv------------------
argDict,downList,DUN1,DUN2,DUN3,self
---------------------------------------
----------------Content----------------
this.lb_cardType1.visible=false;
this.lb_cardType2.visible=false;
this.lb_cardType3.visible=false;
this.ClearNodeSprite();
_local0=this.LogicRank.getDunListByType("DOWN");
_local1=this.LogicRank.getDunListByType("DUN1");
_local2=this.LogicRank.getDunListByType("DUN2");
_local3=this.LogicRank.getDunListByType("DUN3");
_local5=0;
while((_local5 < _local0.length)){
_local8=undefined;
_local7=undefined;
_local6=undefined;
_local6=_local0[_local5];
_local7=this.back.getChildByName("card").getChildByName(("btn_" + (_local5 + 1)));
if(!(_local7)){
console.error("ShowAllPlayerCard not find:%s",_local7);
}
        
_local7.visible=true;
_local7.y=this.InitBtnCardPosY;
_local8=this.LogicRank.CheckSelected(_local6);
} else if(_local8){
_local7.y=(_local7.y + this.ChooseCardAddOffY);
}
this.ShowCard(_local6,_local7.getChildByName("cardPrefab2"));
}
        
_local5=(_local5 + 1);
_local5=0;
}
}
        
_local5=1;
break;
while((_local5 <= 3)){
_local7=undefined;
_local6=undefined;
_local6=this.LogicRank.getDunListByType(("DUN" + _local5));
_aliased8981=this;
_local6.sort(function () { __FUNC_36__ });
_local7=0;
this.GetCardType(("DUN" + _local5));
if((2 == _local5)){
_local7=3;
}
if((3 == _local5)){
_local7=8;
}
_local8=0;
while((_local8 < _local6.length)){
_local10=undefined;
_local9=undefined;
_local9=((_local7 + _local8) + 1);
_local10=this.back.getChildByName("bg_dun").getChildByName(("btnDun" + _local9));
if(!(_local10)){
print("ShowAllPlayerCard not find:%s",("btnDun" + _local9));
}
        
} else {
}
this.ShowCard(_local6[_local8],_local10.getChildByName("cardPrefab"));
}
        
_local8=(_local8 + 1);
_local8=0;
}
}
        
}
        
_local5=(_local5 + 1);
_local5=1;
}
}
        
---------------------------------------
==========================================================================E
==================================36==================================S
------------------Argv------------------
a,b
---------------------------------------
----------------Content----------------
return (_aliased8981.LogicGame.GetCardValue(b) - _aliased8981.LogicGame.GetCardValue(a));
---------------------------------------
==========================================================================E
==================================37==================================S
------------------Argv------------------
downList
---------------------------------------
----------------Content----------------
_local0=this.LogicRank.getDunListByType("DOWN");
console.log("downList",_local0);
_local1=1;
while((_local1 <= this.cardCount)){
_local2=undefined;
_local2=this.card.getChildByName(("btn_" + _local1));
if(!(_local2)){
this.ErrLog("DealCardEffect not find:%s",_local2);
}
        
} else {
}
_local2.visible=true;
this.ShowCard(_local0[(_local1 - 1)],_local2.getChildByName("cardPrefab2"));
_local2.visible=true;
}
        
_local1=(_local1 + 1);
_local1=1;
}
}
        
---------------------------------------
==========================================================================E
==================================38==================================S
------------------Argv------------------
cardType,btnNode,maPaiList,nMaPai
---------------------------------------
----------------Content----------------
if(cardType){
_local2=undefined;
_local2=this.LogicGame.SubCardValue(cardType);
this.GetPokeCard(_local2,btnNode);
_local0=exchangeTo16Data_thirteenGX([MjClient.SSS_GX_MaPai]);
_local1=_local0[0];
if((cardType == _local1)){
btnNode.setColor(cc.color(255,255,63));
} else {
}
btnNode.setColor(cc.color(255,255,255));
btnNode.getChildByName("poker_back").visible=false;
btnNode.visible=true;
}
        
} else {
}
console.error("ShowCard",cardType,btnNode.name,btnNode);
---------------------------------------
==========================================================================E
==================================39==================================S
------------------Argv------------------
poker,cardNode,type,type1,type2,num,cardColor,cardValue,numNode,iconSp,icon1_Sp
---------------------------------------
----------------Content----------------
if((0 == poker)){
return undefined;
}
_local0="";
_local1="";
_local2="";
_local3="";
_local4=this.LogicGame.GetCardColor(poker);
_local5=this.LogicGame.GetCardValue(poker);
_local6=cardNode.getChildByName("num");
_local6.visible=true;
if((_local4 == 0)){
_local0="bg_diamond1_";
_local1=(_local0 + 1);
_local2=(_local0 + 2);
_local3=("red_" + _local5);
} else if((_local4 == 16)){
_local0="bg_club1_";
_local1=(_local0 + 1);
_local2=(_local0 + 2);
_local3=("black_" + _local5);
} else if((_local4 == 32)){
_local0="bg_heart1_";
_local1=(_local0 + 1);
_local2=(_local0 + 2);
_local3=("red_" + _local5);
} else if((_local4 == 48)){
_local0="bg_spade1_";
_local1=(_local0 + 1);
_local2=(_local0 + 2);
_local3=("black_" + _local5);
} else if((_local4 == 64)){
_local6.visible=false;
_local1="icon_small_king_01";
_local2="icon_small_king";
} else if((_local4 == 80)){
_local6.visible=false;
_local1="icon_big_king_01";
_local2="icon_big_king";
}
if((_local3 != "")){
_local9=undefined;
_local9=cardNode.getChildByName("num");
_local9.loadTexture((("poker/13zhang/new_poker/" + _local3) + ".png"));
}
        
}
_local7=cardNode.getChildByName("icon");
_local7.loadTexture((("poker/13zhang/new_poker/" + _local1) + ".png"));
_local8=cardNode.getChildByName("icon_1");
_local8.loadTexture((("poker/13zhang/new_poker/" + _local2) + ".png"));
---------------------------------------
==========================================================================E
==================================40==================================S
------------------Argv------------------
shouCardList
---------------------------------------
----------------Content----------------
_local0=this.LogicRank.getDunListByType("DOWN");
if(!(_local0.length)){
this.isBtnHide(true);
console.log("isBtnHide1",this.LogicRank.cardStateList);
this.node_btn_select.visible=false;
this.node_btn_unSelect.visible=false;
}
---------------------------------------
==========================================================================E
==================================41==================================S
------------------Argv------------------
shouCardList,duizis,liangdui,santiaos,shunzi,tonghua,zhadan,tonghuashun,wutong
---------------------------------------
----------------Content----------------
this.isBtnHide(false);
_local0=this.LogicRank.getDunListByType("DOWN");
this.node_btn_select.visible=true;
this.node_btn_unSelect.visible=true;
_local1=this.LogicGame.GetDuiZi(_local0,true);
_local2=this.LogicGame.GetLiangDui(_local0);
_local3=this.LogicGame.GetSanTiao(_local0,true);
_local4=this.LogicGame.GetShunzi(_local0);
_local5=this.LogicGame.GetTonghua(_local0);
_local6=this.LogicGame.GetZhaDang(_local0,true);
_local7=this.LogicGame.GetTongHuaShunEx(_local0);
_local8=this.LogicGame.GetWuTong(_local0);
if(!(_local1.length)){
this.check_duizi.visible=false;
this.no_duizi.visible=true;
} else {
}
this.check_duizi.visible=true;
this.no_duizi.visible=false;
if(!(_local2.length)){
this.check_liangdui.visible=false;
this.no_liangdui.visible=true;
} else if((_local2.length == 2)){
_local9=undefined;
_local9=this.LogicGame.CheckSameValue(_local2[0],_local2[1]);
if(_local9){
this.check_liangdui.visible=false;
this.no_liangdui.visible=true;
} else {
}
this.check_liangdui.visible=true;
this.no_liangdui.visible=false;
}
        
} else {
}
this.check_liangdui.visible=true;
this.no_liangdui.visible=false;
if(!(_local3.length)){
this.check_santiao.visible=false;
this.no_santiao.visible=true;
} else {
}
this.check_santiao.visible=true;
this.no_santiao.visible=false;
if(!(_local4.length)){
this.check_shunzi.visible=false;
this.no_shunzi.visible=true;
} else {
}
this.check_shunzi.visible=true;
this.no_shunzi.visible=false;
if(!(_local5.length)){
this.check_tonghua.visible=false;
this.no_tonghua.visible=true;
} else {
}
this.check_tonghua.visible=true;
this.no_tonghua.visible=false;
if((_local1.length != 0)&&(_local3.length != 0)){
_local9=undefined;
_local9=false;
_local10=0;
break;
while((_local10 < _local1.length)){
_local11=undefined;
_local11=_local1[_local10];
_local12=0;
while((_local12 < _local3.length)){
_local14=undefined;
_local13=undefined;
_local13=_local3[_local12];
_local14=this.LogicGame.CheckSameValue(_local13,_local11);
if(!(_local14)){
_local9=true;
}
        
break;
}
        
}
_local12=(_local12 + 1);
_local12=0;
}
}
        
if(_local9){
}
        
break;
}
        
}
_local10=(_local10 + 1);
_local10=0;
}
}
        
if(_local9){
this.check_hulu.visible=true;
this.no_hulu.visible=false;
} else {
}
this.check_hulu.visible=false;
this.no_hulu.visible=true;
}
        
} else {
}
this.check_hulu.visible=false;
this.no_hulu.visible=true;
if(!(_local6.length)){
this.check_zhadang.visible=false;
this.no_zhadang.visible=true;
} else {
}
this.check_zhadang.visible=true;
this.no_zhadang.visible=false;
if(!(_local7.length)){
this.check_tonghuashun.visible=false;
this.no_tonghuashun.visible=true;
} else {
}
this.check_tonghuashun.visible=true;
this.no_tonghuashun.visible=false;
if(!(_local8.length)){
this.check_wutong.visible=false;
this.no_wutong.visible=true;
} else {
}
this.check_wutong.visible=true;
this.no_wutong.visible=false;
---------------------------------------
==========================================================================E
==================================42==================================S
------------------Argv------------------
show
---------------------------------------
----------------Content----------------
if(!(show)){
this.btnOK.visible=false;
this.btnCancel.visible=false;
this.node_btn_select.visible=false;
this.node_btn_unSelect.visible=false;
return undefined;
}
if((this.LogicGame.CheckCardBigOrSmall(this.LogicRank.cardStateList.DUN1,this.LogicRank.cardStateList.DUN2) == 0)){
MjClient.showToast("");
this.btnOK.visible=false;
this.btnCancel.visible=false;
this.node_btn_select.visible=false;
this.node_btn_unSelect.visible=false;
return undefined;
}
MjClient.poker_thirteenGX.printCards(this.LogicRank.cardStateList.DUN2,"");
MjClient.poker_thirteenGX.printCards(this.LogicRank.cardStateList.DUN3,"");
if((this.LogicGame.CheckCardBigOrSmall(this.LogicRank.cardStateList.DUN2,this.LogicRank.cardStateList.DUN3) == 0)){
this.btnOK.visible=false;
this.btnCancel.visible=false;
this.node_btn_select.visible=false;
this.node_btn_unSelect.visible=false;
this.ExchangeDun2To3();
return undefined;
}
if((this.LogicGame.CheckCardBigOrSmall(this.LogicRank.cardStateList.DUN1,this.LogicRank.cardStateList.DUN3) == 0)){
MjClient.showToast("");
this.btnOK.visible=false;
this.btnCancel.visible=false;
this.node_btn_select.visible=false;
this.node_btn_unSelect.visible=false;
return undefined;
}
this.btnOK.visible=true;
this.btnCancel.visible=true;
---------------------------------------
==========================================================================E
==================================43==================================S
------------------Argv------------------
dunArr1,dunArr2,dunArr3,dun3,dun2,dun1
---------------------------------------
----------------Content----------------
_local0=this.LogicRank.getDunListByType("DUN1");
_local1=this.LogicRank.getDunListByType("DUN2");
_local2=this.LogicRank.getDunListByType("DUN3");
_local3=[].concat(_local1);
_local4=[].concat(_local2);
_local5=[].concat(_local0);
this.LogicRank.QuickSetDunEx("DUN3",_local3);
this.LogicRank.QuickSetDunEx("DUN2",_local4);
this.LogicRank.QuickSetDunEx("DUN1",_local5);
this.LogicRank.cardStateList.SELECTED=[];
this.LogicRank.cardStateList.DOWN=[];
this.ShowAllPlayerCard();
this.disabledBtn();
---------------------------------------
==========================================================================E
==================================44==================================S
------------------Argv------------------
pokers,sortCards
---------------------------------------
----------------Content----------------
_local0=this.LogicRank.getDunListByType("DOWN");
_local1=this.LogicGame.GetSortCards(_local0);
if(_local1&&_local1.length){
this.LogicRank.pushDownCards(_local1);
}
---------------------------------------
==========================================================================E
==================================45==================================S
------------------Argv------------------
pokers,sortCards
---------------------------------------
----------------Content----------------
_local0=this.LogicRank.getDunListByType("DOWN");
if((this.sortType == 1)){
this.sortType=2;
} else {
}
this.sortType=1;
this.check_huase.getComponent(cc.Sprite).spriteFrame=this.icon_sortType[this.sortType];
_local1=this.LogicGame.GetSortCardsByColor(_local0,this.sortType);
if(_local1&&_local1.length){
this.LogicRank.pushDownCards(_local1);
}
---------------------------------------
==========================================================================E
==================================46==================================S
------------------Argv------------------
pokers,sortType,array,self
---------------------------------------
----------------Content----------------
if(!(pokers.length)){
return undefined;
}
_local0=[];
_local1=this;
_local2=0;
while((_local2 < pokers.length)){
_local4=undefined;
_local3=undefined;
_local3=pokers[_local2];
_local4={};
_local4.cardValue=this.LogicGame.GetCardValue(_local3);
_local4.cardColor=this.LogicGame.GetCardColor(_local3);
_local4.cardX16=_local3;
_local0.push(_local4);
}
        
_local2=(_local2 + 1);
_local2=0;
}
}
        
_local0.sort(function () { __FUNC_47__ });
return _local0;
---------------------------------------
==========================================================================E
==================================47==================================S
------------------Argv------------------
a,b
---------------------------------------
----------------Content----------------
if((a.cardColor == b.cardColor)){
return (b.cardValue - a.cardValue);
} else if((_aliased8981 == 1)){
return (b.cardColor - a.cardColor);
} else if((_aliased8981 == 2)){
return (a.cardColor - b.cardColor);
}
---------------------------------------
==========================================================================E
==================================48==================================S
------------------Argv------------------
btnName,clickDunDict
---------------------------------------
[error] JSOP_IFEQ Unknown Type