==================================0==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
var ShowCardLayer_thirteenGX;
ShowCardLayer_thirteenGX = cc.Layer.extend({ctor:function () { __FUNC_1__ },onDoCompareCard:function () { __FUNC_2__ },onRoundEnd:function () { __FUNC_3__ },onEndRoom:function () { __FUNC_4__ },onBtnClose:function () { __FUNC_5__ },ShowAllPlayerCard:function () { __FUNC_6__ },sortCardsByValue:function () { __FUNC_7__ },ShowCard:function () { __FUNC_8__ },GetPokeCard:function () { __FUNC_9__ },GetCardType:function () { __FUNC_10__ },ClearNodeSprite:function () { __FUNC_11__ }});
---------------------------------------
==========================================================================E
==================================1==================================S
------------------Argv------------------
shouCardList,UI,block,back,nodeDun,btnDun
---------------------------------------
----------------Content----------------
this._super();
this.shouCardList=shouCardList;
this.LogicRank=MjClient.rank_thirteenGX;
this.LogicGame=MjClient.poker_thirteenGX;
this.cardList=[];
this.cardList.DUN1=[shouCardList[0],shouCardList[1],shouCardList[2]];
this.cardList.DUN2=[shouCardList[3],shouCardList[4],shouCardList[5],shouCardList[6],shouCardList[7]];
this.cardList.DUN3=[shouCardList[8],shouCardList[9],shouCardList[10],shouCardList[11],shouCardList[12]];
_local0=ccs.load("thirteenGX_showCard.json");
this.addChild(_local0.node);
UIEventBind(null,this,"doCompareCard",function () { __FUNC_2__ },this);
UIEventBind(null,this,"roundEnd",function () { __FUNC_3__ },this);
UIEventBind(null,this,"endRoom",function () { __FUNC_4__ },this);
_local1=_local0.node.getChildByName("block");
setWgtLayout(_local1,[1,1],[0.5,0.5],[0,0],true);
_local2=_local0.node.getChildByName("back");
setWgtLayout(_local2,[1,1],[0.5,0.5],[0,0]);
this.back=_local2;
this.lb_cardType1=_local2.getChildByName("bg_dun").getChildByName("lb_cardType1");
this.lb_cardType2=_local2.getChildByName("bg_dun").getChildByName("lb_cardType2");
this.lb_cardType3=_local2.getChildByName("bg_dun").getChildByName("lb_cardType3");
this.cardPrefab=_local0.node.getChildByName("cardPrefab");
this.cardPrefab.visible=false;
this.btnClose=_local2.getChildByName("btnClose");
this.btnClose.addTouchEventListener(function () { __FUNC_5__ },this);
_local3=_local2.getChildByName("bg_dun");
_local5=1;
while((_local5 <= 13)){
_local6=undefined;
_local4=_local3.getChildByName(("btnDun" + _local5));
_local6=this.cardPrefab.clone();
_local6.setName("cardPrefab");
_local4.addChild(_local6);
_local6.setPosition((_local4.width * 0.5),(_local4.height * 0.5));
}
        
_local5=(_local5 + 1);
_local5=1;
}
}
        
this.ShowAllPlayerCard();
---------------------------------------
==========================================================================E
==================================2==================================S
------------------Argv------------------
argDict
---------------------------------------
----------------Content----------------
this.removeFromParent();
---------------------------------------
==========================================================================E
==================================3==================================S
------------------Argv------------------
argDict
---------------------------------------
----------------Content----------------
this.removeFromParent();
---------------------------------------
==========================================================================E
==================================4==================================S
------------------Argv------------------
argDict
---------------------------------------
----------------Content----------------
this.removeFromParent();
---------------------------------------
==========================================================================E
==================================5==================================S
------------------Argv------------------
sender,type
---------------------------------------
----------------Content----------------
if((type == 2)){
this.removeFromParent();
}
---------------------------------------
==========================================================================E
==================================6==================================S
------------------Argv------------------
self
---------------------------------------
----------------Content----------------
this.lb_cardType1.visible=false;
this.lb_cardType2.visible=false;
this.lb_cardType3.visible=false;
this.ClearNodeSprite();
_local1=1;
break;
while((_local1 <= 3)){
_local3=undefined;
_local2=undefined;
_local2=this.cardList[("DUN" + _local1)];
_aliased4837=this;
_local2.sort(function () { __FUNC_7__ });
_local3=0;
this.GetCardType(("DUN" + _local1));
if((2 == _local1)){
_local3=3;
}
if((3 == _local1)){
_local3=8;
}
_local4=0;
while((_local4 < _local2.length)){
_local6=undefined;
_local5=undefined;
_local5=((_local3 + _local4) + 1);
_local6=this.back.getChildByName("bg_dun").getChildByName(("btnDun" + _local5));
if(!(_local6)){
print("ShowAllPlayerCard not find:%s",("btnDun" + _local5));
}
        
} else {
}
this.ShowCard(_local2[_local4],_local6.getChildByName("cardPrefab"));
}
        
_local4=(_local4 + 1);
_local4=0;
}
}
        
}
        
_local1=(_local1 + 1);
_local1=1;
}
}
        
---------------------------------------
==========================================================================E
==================================7==================================S
------------------Argv------------------
a,b
---------------------------------------
----------------Content----------------
return (_aliased4837.LogicGame.GetCardValue(b) - _aliased4837.LogicGame.GetCardValue(a));
---------------------------------------
==========================================================================E
==================================8==================================S
------------------Argv------------------
cardType,btnNode,maPaiList,nMaPai
---------------------------------------
----------------Content----------------
if(cardType){
_local2=undefined;
_local2=this.LogicGame.SubCardValue(cardType);
this.GetPokeCard(_local2,btnNode);
_local0=exchangeTo16Data_thirteenGX([MjClient.SSS_GX_MaPai]);
_local1=_local0[0];
if((cardType == _local1)){
btnNode.setColor(cc.color(255,255,63));
} else {
}
btnNode.setColor(cc.color(255,255,255));
btnNode.getChildByName("poker_back").visible=false;
btnNode.visible=true;
}
        
} else {
}
console.error("ShowCard",cardType,btnNode.name,btnNode);
---------------------------------------
==========================================================================E
==================================9==================================S
------------------Argv------------------
poker,cardNode,type,type1,type2,num,cardColor,cardValue,numNode,iconSp,icon1_Sp
---------------------------------------
----------------Content----------------
if((0 == poker)){
return undefined;
}
_local0="";
_local1="";
_local2="";
_local3="";
_local4=this.LogicGame.GetCardColor(poker);
_local5=this.LogicGame.GetCardValue(poker);
_local6=cardNode.getChildByName("num");
_local6.visible=true;
if((_local4 == 0)){
_local0="bg_diamond1_";
_local1=(_local0 + 1);
_local2=(_local0 + 2);
_local3=("red_" + _local5);
} else if((_local4 == 16)){
_local0="bg_club1_";
_local1=(_local0 + 1);
_local2=(_local0 + 2);
_local3=("black_" + _local5);
} else if((_local4 == 32)){
_local0="bg_heart1_";
_local1=(_local0 + 1);
_local2=(_local0 + 2);
_local3=("red_" + _local5);
} else if((_local4 == 48)){
_local0="bg_spade1_";
_local1=(_local0 + 1);
_local2=(_local0 + 2);
_local3=("black_" + _local5);
} else if((_local4 == 64)){
_local6.visible=false;
_local1="icon_small_king_01";
_local2="icon_small_king";
} else if((_local4 == 80)){
_local6.visible=false;
_local1="icon_big_king_01";
_local2="icon_big_king";
}
if((_local3 != "")){
_local9=undefined;
_local9=cardNode.getChildByName("num");
_local9.loadTexture((("poker/13zhang/new_poker/" + _local3) + ".png"));
}
        
}
_local7=cardNode.getChildByName("icon");
_local7.loadTexture((("poker/13zhang/new_poker/" + _local1) + ".png"));
_local8=cardNode.getChildByName("icon_1");
_local8.loadTexture((("poker/13zhang/new_poker/" + _local2) + ".png"));
---------------------------------------
==========================================================================E
==================================10==================================S
------------------Argv------------------
dun,len,typNode,bShow
---------------------------------------
----------------Content----------------
_local0=this.cardList[dun].length;
_local1={};
_local2=false;
if((dun == "DUN1")&&(_local0 == 3)){
_local1=this.lb_cardType1;
_local1.visible=true;
_local2=true;
} else if((dun == "DUN2")&&(_local0 == 5)){
_local1=this.lb_cardType2;
_local1.visible=true;
_local2=true;
} else if((dun == "DUN3")&&(_local0 == 5)){
_local1=this.lb_cardType3;
_local1.visible=true;
_local2=true;
}
if(_local2){
_local4=undefined;
_local3=undefined;
_local3=this.cardList[dun];
print(("+++++++++++++++++ dunList = " + JSON.stringify(_local3)));
_local4=this.LogicGame.CheckCardType(_local3);
if((_local4 == 0)){
_local1.setString("对子");
} else if((_local4 == 1)){
_local1.setString("两对");
} else if((_local4 == 2)){
_local1.setString("");
} else if((_local4 == 3)){
_local1.setString("顺子");
} else if((_local4 == 4)){
_local1.setString("");
} else if((_local4 == 5)){
_local1.setString("");
} else if((_local4 == 6)){
_local1.setString("");
} else if((_local4 == 7)){
_local1.setString("葫芦");
} else if((_local4 == 8)){
_local1.setString("铁支");
} else if((_local4 == 9)){
_local1.setString("");
} else if((_local4 == 10)){
_local1.setString("");
} else if((_local4 == -1)){
_local1.setString("乌龙");
}
        
}
}
---------------------------------------
==========================================================================E
==================================11==================================S
------------------Argv------------------
nodeDun
---------------------------------------
----------------Content----------------
_local1=1;
while((_local1 <= 13)){
_local3=undefined;
_local2=undefined;
_local0=this.back.getChildByName("bg_dun");
_local2=_local0.getChildByName(("btnDun" + _local1));
if(!(_local2)){
print("ClearNodeSprite not find:%s",("btnDun" + _local1));
}
        
} else {
}
_local3=_local2.getChildByName("cardPrefab");
_local3.visible=false;
}
        
_local1=(_local1 + 1);
_local1=1;
}
}
        
---------------------------------------
==========================================================================E
