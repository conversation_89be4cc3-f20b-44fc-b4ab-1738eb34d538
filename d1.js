==================================0==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
function ThirteenGX( __ARGV_1__ ){ __FUNC_1__ }
ThirteenGX.prototype.GetCardTypeName=function () { __FUNC_2__ };
ThirteenGX.prototype.GetSpecialType=function () { __FUNC_3__ };
ThirteenGX.prototype.IsZhiZunQingLong=function () { __FUNC_4__ };
ThirteenGX.prototype.IsYiTiaoLong=function () { __FUNC_5__ };
ThirteenGX.prototype.IsShiErHuangZu=function () { __FUNC_6__ };
ThirteenGX.prototype.IsSanTongHuaShun=function () { __FUNC_7__ };
ThirteenGX.prototype.IsSanFenTianXia=function () { __FUNC_8__ };
ThirteenGX.prototype.IsQuanDa=function () { __FUNC_9__ };
ThirteenGX.prototype.IsQuanXiao=function () { __FUNC_10__ };
ThirteenGX.prototype.IsCouYiSe=function () { __FUNC_11__ };
ThirteenGX.prototype.IsShuangGuaiChongSan=function () { __FUNC_12__ };
ThirteenGX.prototype.IsSiTaoSanTiao=function () { __FUNC_13__ };
ThirteenGX.prototype.IsWuDuiSanTiao=function () { __FUNC_14__ };
ThirteenGX.prototype.IsLiuDuiBan=function () { __FUNC_15__ };
ThirteenGX.prototype.IsSanShunZi=function () { __FUNC_16__ };
ThirteenGX.prototype.sortShunziByLength=function () { __FUNC_17__ };
ThirteenGX.prototype.joinGuiToShunzi=function () { __FUNC_18__ };
ThirteenGX.prototype.IsSanTongHua=function () { __FUNC_19__ };
ThirteenGX.prototype.sortTonghuaByLength=function () { __FUNC_20__ };
ThirteenGX.prototype.joinGuiToTonghua=function () { __FUNC_21__ };
ThirteenGX.prototype.DeepCopy=function () { __FUNC_22__ };
ThirteenGX.prototype.SubCardValue=function () { __FUNC_23__ };
ThirteenGX.prototype.sortFun=function () { __FUNC_24__ };
ThirteenGX.prototype.printCards=function () { __FUNC_25__ };
ThirteenGX.prototype.gatHandScore=function () { __FUNC_26__ };
ThirteenGX.prototype.sortCardsByValue=function () { __FUNC_27__ };
ThirteenGX.prototype.CheckCardType=function () { __FUNC_28__ };
ThirteenGX.prototype.GetMaxCardType=function () { __FUNC_29__ };
ThirteenGX.prototype.GetAllCardType=function () { __FUNC_30__ };
ThirteenGX.prototype.RemoveArrFormAll=function () { __FUNC_31__ };
ThirteenGX.prototype.GetSingleCardFormArr=function () { __FUNC_32__ };
ThirteenGX.prototype.CheckUsedCardType=function () { __FUNC_33__ };
ThirteenGX.prototype.CheckUsedCardType2=function () { __FUNC_34__ };
ThirteenGX.prototype.CheckDuiziByGui=function () { __FUNC_35__ };
ThirteenGX.prototype.sortDuiziByValue=function () { __FUNC_36__ };
ThirteenGX.prototype.CheckCardBigOrSmall=function () { __FUNC_37__ };
ThirteenGX.prototype.sortCardValue=function () { __FUNC_38__ };
ThirteenGX.prototype.GetCompleteShun=function () { __FUNC_39__ };
ThirteenGX.prototype.GetGuiPai=function () { __FUNC_40__ };
ThirteenGX.prototype.PokerCombination=function () { __FUNC_41__ };
ThirteenGX.prototype.GetDuiZi=function () { __FUNC_42__ };
ThirteenGX.prototype.GetLiangDui=function () { __FUNC_43__ };
ThirteenGX.prototype.GetLiangDuiEX=function () { __FUNC_44__ };
ThirteenGX.prototype.GetSanTiao=function () { __FUNC_45__ };
ThirteenGX.prototype.GetSanTiaoEx=function () { __FUNC_46__ };
ThirteenGX.prototype.isSave=function () { __FUNC_47__ };
ThirteenGX.prototype.isContain=function () { __FUNC_48__ };
ThirteenGX.prototype.GetShunzi=function () { __FUNC_49__ };
ThirteenGX.prototype.GetShunziByGui=function () { __FUNC_50__ };
ThirteenGX.prototype.sortFunEx=function () { __FUNC_51__ };
ThirteenGX.prototype.GetShunziEx=function () { __FUNC_52__ };
ThirteenGX.prototype.isSameColor=function () { __FUNC_53__ };
ThirteenGX.prototype.isSameColorEx=function () { __FUNC_54__ };
ThirteenGX.prototype.GetTonghua=function () { __FUNC_55__ };
ThirteenGX.prototype.GetTonghuaByGui=function () { __FUNC_56__ };
ThirteenGX.prototype.GetTonghuaEx=function () { __FUNC_57__ };
ThirteenGX.prototype.GetYiDuiTongHua=function () { __FUNC_58__ };
ThirteenGX.prototype.GetLiangDuiTongHua=function () { __FUNC_59__ };
ThirteenGX.prototype.GetHulu=function () { __FUNC_60__ };
ThirteenGX.prototype.GetHuluEx=function () { __FUNC_61__ };
ThirteenGX.prototype.GetZhaDang=function () { __FUNC_62__ };
ThirteenGX.prototype.GetTongHuaShun=function () { __FUNC_63__ };
ThirteenGX.prototype.GetTongHuaShunEx=function () { __FUNC_64__ };
ThirteenGX.prototype.GetTongHuaShunByGui=function () { __FUNC_65__ };
ThirteenGX.prototype.GetWuTong=function () { __FUNC_66__ };
ThirteenGX.prototype.SortCardByMax=function () { __FUNC_67__ };
ThirteenGX.prototype.SortCardByMaxEx=function () { __FUNC_68__ };
ThirteenGX.prototype.SortCardByMin=function () { __FUNC_69__ };
ThirteenGX.prototype.SortCardByMinEx=function () { __FUNC_70__ };
ThirteenGX.prototype.GetSortCardsByColor=function () { __FUNC_71__ };
ThirteenGX.prototype.GetSortCardsByColorEx=function () { __FUNC_72__ };
ThirteenGX.prototype.isHaveGui=function () { __FUNC_73__ };
ThirteenGX.prototype.isRepeatCard=function () { __FUNC_74__ };
ThirteenGX.prototype.CheckSameColor=function () { __FUNC_75__ };
ThirteenGX.prototype.CheckLine=function () { __FUNC_76__ };
ThirteenGX.prototype.CheckLineEx=function () { __FUNC_77__ };
ThirteenGX.prototype.CheckContainList=function () { __FUNC_78__ };
ThirteenGX.prototype.CheckContainListEx=function () { __FUNC_79__ };
ThirteenGX.prototype.CheckContainListByGui=function () { __FUNC_80__ };
ThirteenGX.prototype.CheckPokerInList=function () { __FUNC_81__ };
ThirteenGX.prototype.CheckPokerInListEX=function () { __FUNC_82__ };
ThirteenGX.prototype.GetContinueValue=function () { __FUNC_83__ };
ThirteenGX.prototype.GetNextValue=function () { __FUNC_84__ };
ThirteenGX.prototype.CheckSameValueInArr=function () { __FUNC_85__ };
ThirteenGX.prototype.GetSameValue=function () { __FUNC_86__ };
ThirteenGX.prototype.GetSameValueEX=function () { __FUNC_87__ };
ThirteenGX.prototype.GetSameColor=function () { __FUNC_88__ };
ThirteenGX.prototype.GetCardValue=function () { __FUNC_89__ };
ThirteenGX.prototype.GetCardColor=function () { __FUNC_90__ };
ThirteenGX.prototype.GetSortCards=function () { __FUNC_91__ };
ThirteenGX.prototype.GetSortCardsByGui=function () { __FUNC_92__ };
ThirteenGX.prototype.GetSortCardsEx=function () { __FUNC_93__ };
ThirteenGX.prototype.GetSortCardsExByGui=function () { __FUNC_94__ };
ThirteenGX.prototype.CheckSameValue=function () { __FUNC_95__ };
ThirteenGX.prototype.SortCardArrByMin=function () { __FUNC_96__ };
ThirteenGX.prototype.SortCardArrByMinEx=function () { __FUNC_97__ };
ThirteenGX.prototype.SortCardArrByMax=function () { __FUNC_98__ };
ThirteenGX.prototype.SortCardArrByMaxEx=function () { __FUNC_99__ };
ThirteenGX.prototype.copyArr=function () { __FUNC_100__ };
MjClient.poker_thirteenGX=new ThirteenGX();
---------------------------------------
==========================================================================E
==================================1==================================S
------------------Argv------------------

---------------------------------------
----------------Content----------------
this.pokerType=[2,3,4,5,6,7,8,9,10,11,12,13,14,18,19,20,21,22,23,24,25,26,27,28,29,30,34,35,36,37,38,39,40,41,42,43,44,45,46,50,51,52,53,54,55,56,57,58,59,60,61,62,65,66,67,81,82,83];
this.LOGIC_MASK_COLOR=240;
this.LOGIC_MASK_VALUE=15;
---------------------------------------
==========================================================================E
==================================2==================================S
------------------Argv------------------
cardType
---------------------------------------
----------------Content----------------
if((cardType == 0)){
return "对子";
} else if((cardType == 1)){
return "两对";
} else if((cardType == 2)){
return "";
} else if((cardType == 3)){
return "顺子";
} else if((cardType == 4)){
return "";
} else if((cardType == 5)){
return "";
} else if((cardType == 6)){
return "";
} else if((cardType == 7)){
return "葫芦";
} else if((cardType == 8)){
return "铁支";
} else if((cardType == 9)){
return "";
} else if((cardType == 10)){
return "";
} else if((cardType == -1)){
return "乌龙";
} else if((cardType == 20)){
return "";
} else if((cardType == 21)){
return "";
} else if((cardType == 22)){
return "十二皇族";
} else if((cardType == 23)){
return "";
} else if((cardType == 24)){
return "";
} else if((cardType == 25)){
return "全大";
} else if((cardType == 26)){
return "";
} else if((cardType == 27)){
return "";
} else if((cardType == 28)){
return "";
} else if((cardType == 29)){
return "";
} else if((cardType == 30)){
return "";
} else if((cardType == 31)){
return "六对半";
} else if((cardType == 32)){
return "";
} else if((cardType == 33)){
return "";
} else {
}
return "乌龙";
---------------------------------------
==========================================================================E
==================================3==================================S
------------------Argv------------------
pokers
---------------------------------------
----------------Content----------------
if(this.IsZhiZunQingLong(pokers)){
return 20;
} else if(this.IsYiTiaoLong(pokers)){
return 21;
} else if(this.IsShiErHuangZu(pokers)){
return 22;
} else if(this.IsSanTongHuaShun(pokers)){
return 23;
} else if(this.IsSanFenTianXia(pokers)){
return 24;
} else if(this.IsQuanDa(pokers)){
return 25;
} else if(this.IsQuanXiao(pokers)){
return 26;
} else if(this.IsCouYiSe(pokers)){
return 27;
} else if(this.IsShuangGuaiChongSan(pokers)){
return 28;
} else if(this.IsSiTaoSanTiao(pokers)){
return 29;
} else if(this.IsWuDuiSanTiao(pokers)){
return 30;
} else if(this.IsLiuDuiBan(pokers)){
return 31;
} else if(this.IsSanShunZi(pokers)){
return 32;
} else if(this.IsSanTongHua(pokers)){
return 33;
}
return -1;
---------------------------------------
==========================================================================E
==================================4==================================S
------------------Argv------------------
pokers,bSameColor,bAllLine
---------------------------------------
----------------Content----------------
if((pokers.length != 13)){
return false;
}
_local0=this.CheckSameColor(pokers);
if((_local0 == false)){
return false;
}
_local1=this.CheckLine(pokers);
if((_local1 == false)){
return false;
}
return true;
---------------------------------------
==========================================================================E
==================================5==================================S
------------------Argv------------------
pokers,bAllLine
---------------------------------------
----------------Content----------------
if((pokers.length != 13)){
return false;
}
_local0=this.CheckLine(pokers);
if((_local0 == false)){
return false;
}
return true;
---------------------------------------
==========================================================================E
==================================6==================================S
------------------Argv------------------
pokers,guipai,nBigCardCnt
---------------------------------------
----------------Content----------------
if((pokers.length != 13)){
return false;
}
_local0=this.GetGuiPai(pokers);
_local1=_local0.length;
_local2=0;
while((_local2 < pokers.length)){
if((_local0.indexOf(pokers[_local2]) != -1)){
} else if((this.GetCardValue(pokers[_local2]) > 10)){
_local1=(_local1 + 1);
_local1=_local0.length;
}
_local2=(_local2 + 1);
_local2=0;
}
}
        
if((_local1 >= 12)){
return true;
}
return false;
---------------------------------------
==========================================================================E
==================================7==================================S
------------------Argv------------------
pokers
---------------------------------------
----------------Content----------------
if((pokers.length != 13)){
return false;
}
return false;
---------------------------------------
==========================================================================E
==================================8==================================S
------------------Argv------------------
pokers,guipai,newPorks,zhadans,santiaos,duizis,dazhan
---------------------------------------
----------------Content----------------
if((pokers.length != 13)){
return false;
}
_local0=this.GetGuiPai(pokers);
_local1=[];
_local6=0;
while((_local6 < pokers.length)){
if((_local0.indexOf(pokers[_local6]) != -1)){
} else {
}
_local1.push(pokers[_local6]);
_local6=(_local6 + 1);
_local6=0;
}
}
        
_local2=[];
_local6=0;
while((_local6 < _local1.length)){
_local9=undefined;
_local8=undefined;
_local7=undefined;
_local7=_local1[_local6];
_local8=this.GetSameValue(_local1,_local7);
_local9=this.CheckPokerInListEX(_local2,_local7);
if((_local8.length == 4) && !(_local9)){
_local2[_local2.length]=_local8;
}
        
}
_local6=(_local6 + 1);
_local6=0;
}
}
        
_local3=this.GetSanTiao(_local1,false);
_local6=0;
while((_local6 < _local3.length)){
_local8=undefined;
_local7=undefined;
if((_local0.length < 1)){
}
        
_local7=_local3[_local6];
_local8=this.CheckPokerInListEX(_local2,_local7[0]);
} else if(!(_local8)){
_local7.push(_local0[0]);
_local0.splice(0,1);
_local2[_local2.length]=_local7;
}
        
}
_local6=(_local6 + 1);
_local6=0;
}
}
        
_local4=this.GetDuiZi(_local1,false);
_local6=0;
while((_local6 < _local4.length)){
_local8=undefined;
_local7=undefined;
if((_local0.length < 2)){
}
        
_local7=_local4[_local6];
_local8=this.CheckPokerInListEX(_local2,_local7[0]);
} else if(!(_local8)){
_local7.push(_local0[0]);
_local0.splice(0,1);
_local7.push(_local0[0]);
_local0.splice(0,1);
_local2[_local2.length]=_local7;
}
        
}
_local6=(_local6 + 1);
_local6=0;
}
}
        
_local6=0;
while((_local6 < _local1.length)){
_local7=undefined;
if((_local0.length < 3)){
}
        
_local7=this.CheckPokerInListEX(_local2,_local1[_local6]);
} else if(!(_local7)){
_local5=[];
_local5.push(_local1[_local6]);
_local5.push(_local0[0]);
_local0.splice(0,1);
_local5.push(_local0[0]);
_local0.splice(0,1);
_local5.push(_local0[0]);
_local0.splice(0,1);
_local2[_local2.length]=_local5;
}
        
}
_local6=(_local6 + 1);
_local6=0;
}
}
        
if((_local2.length == 3)){
return true;
}
return false;
---------------------------------------
==========================================================================E
==================================9==================================S
------------------Argv------------------
pokers,guipai
---------------------------------------
----------------Content----------------
if((pokers.length != 13)){
return false;
}
_local0=this.GetGuiPai(pokers);
_local1=0;
while((_local1 < pokers.length)){
if((_local0.indexOf(pokers[_local1]) != -1)){
} else if((this.GetCardValue(pokers[_local1]) < 10)){
false;
}
        
}
_local1=(_local1 + 1);
_local1=0;
}
}
        
return true;
---------------------------------------
==========================================================================E
==================================10==================================S
------------------Argv------------------
pokers,guipai
---------------------------------------
----------------Content----------------
if((pokers.length != 13)){
return false;
}
_local0=this.GetGuiPai(pokers);
_local1=0;
while((_local1 < pokers.length)){
if((_local0.indexOf(pokers[_local1]) != -1)){
} else if((this.GetCardValue(pokers[_local1]) > 10)){
false;
}
        
}
_local1=(_local1 + 1);
_local1=0;
}
}
        
return true;
---------------------------------------
==========================================================================E
==================================11==================================S
------------------Argv------------------
pokers,bSameColor
---------------------------------------
----------------Content----------------
if((pokers.length != 13)){
return false;
}
_local0=this.CheckSameColor(pokers);
if((_local0 == false)){
return false;
}
return true;
---------------------------------------
==========================================================================E
==================================12==================================S
------------------Argv------------------
pokers,hulus,huluArr
---------------------------------------
----------------Content----------------
if((pokers.length != 13)){
return false;
}
_local0=this.GetHuluEx(pokers);
if((_local0.length <= 1)){
return false;
}
_local1=[];
_local2=0;
break;
while((_local2 < _local0.length)){
_local3=(_local2 + 1);
break;
while((_local3 < _local0.length)){
if(!(this.isRepeatCard(_local0[_local2],_local0[_local3]))){
_local5=undefined;
_local4=undefined;
_local4=[];
_local6=0;
while((_local6 < pokers.length)){
if((_local0[_local2].indexOf(pokers[_local6]) != -1)||(_local0[_local3].indexOf(pokers[_local6]) != -1)){
} else {
}
_local4.push(pokers[_local6]);
_local6=(_local6 + 1);
_local6=0;
}
}
        
_local5=this.GetDuiZi(_local4,true);
if((_local5.length >= 1)){
true;
}
        
}
        
}
        
}
        
}
}
_local3=(_local3 + 1);
_local3=(_local2 + 1);
}
}
        
_local2=(_local2 + 1);
_local2=0;
}
}
        
return false;
---------------------------------------
==========================================================================E
==================================13==================================S
------------------Argv------------------
pokers,santiaos,notRepeatArr
---------------------------------------
----------------Content----------------
if((pokers.length != 13)){
return false;
}
_local0=this.GetSanTiao(pokers,true);
_local2=0;
break;
while((_local2 < _local0.length)){
_local1=[];
_local1.push(_local0[_local2]);
_local3=(_local2 + 1);
while((_local3 < _local0.length)){
if(!(this.isRepeatCard(_local0[_local2],_local0[_local3]))){
_local1.push(_local0[_local3]);
if((_local1.length == 4)){
true;
}
        
}
        
}}
_local3=(_local3 + 1);
_local3=(_local2 + 1);
}
}
        
_local2=(_local2 + 1);
_local2=0;
}
}
        
return false;
---------------------------------------
==========================================================================E
==================================14==================================S
------------------Argv------------------
pokers,duizis,notRepeatArr
---------------------------------------
----------------Content----------------
if((pokers.length != 13)){
return false;
}
_local0=this.GetDuiZi(pokers,true);
_local2=0;
break;
while((_local2 < _local0.length)){
_local1=[];
_local1.push(_local0[_local2]);
_local3=(_local2 + 1);
break;
while((_local3 < _local0.length)){
if(!(this.isRepeatCard(_local0[_local2],_local0[_local3]))){
_local1.push(_local0[_local3]);
if((_local1.length == 5)){
_local6=undefined;
_local5=undefined;
_local4=undefined;
_local4=[];
_local5=-1;
_local7=0;
while((_local7 < pokers.length)){
if((_local1[0].indexOf(pokers[_local7]) != -1) || (_local1[1].indexOf(pokers[_local7]) != -1) || (_local1[2].indexOf(pokers[_local7]) != -1) || (_local1[3].indexOf(pokers[_local7]) != -1) || (_local1[4].indexOf(pokers[_local7]) != -1)){
_local4.push(pokers[_local7]);
} else if((pokers[_local7] < 65)){
_local5=pokers[_local7];
}
_local7=(_local7 + 1);
_local7=0;
}
}
        
_local6=true;
_local7=0;
while((_local7 < _local4.length)){
if((pokers[_local7] < 65)&&(this.GetCardValue(pokers[_local7]) != this.GetCardValue(_local5))){
_local6=false;
break;
}
_local7=(_local7 + 1);
_local7=0;
}
}
        
if(_local6){
true;
}
        
}
        
}
        
}
        
}
}}
_local3=(_local3 + 1);
_local3=(_local2 + 1);
}
}
        
_local2=(_local2 + 1);
_local2=0;
}
}
        
return false;
---------------------------------------
==========================================================================E
==================================15==================================S
------------------Argv------------------
pokers,duizis,notRepeatArr
---------------------------------------
----------------Content----------------
if((pokers.length != 13)){
return false;
}
_local0=this.GetDuiZi(pokers,true);
_local2=0;
break;
while((_local2 < _local0.length)){
_local1=[];
_local1.push(_local0[_local2]);
_local3=(_local2 + 1);
while((_local3 < _local0.length)){
if(!(this.isRepeatCard(_local0[_local2],_local0[_local3]))){
_local1.push(_local0[_local3]);
if((_local1.length == 6)){
true;
}
        
}
        
}}
_local3=(_local3 + 1);
_local3=(_local2 + 1);
}
}
        
_local2=(_local2 + 1);
_local2=0;
}
}
        
return false;
---------------------------------------
==========================================================================E
==================================16==================================S
------------------Argv------------------
pokers,gui,newPorks,shunzis,JoinGuiPia
---------------------------------------
----------------Content----------------
return false;
if((pokers.length != 13)){
return false;
}
_local0=this.GetGuiPai(pokers);
_local1=[];
_local4=0;
while((_local4 < pokers.length)){
if((_local0.indexOf(pokers[_local4]) != -1)){
} else {
}
_local1.push(pokers[_local4]);
_local4=(_local4 + 1);
_local4=0;
}
}
        
_local2=[];
_local4=0;
while((_local4 < _local1.length)){
_local6=undefined;
_local5=undefined;
_local5=_local1[_local4];
_local6=this.CheckPokerInList(tonghuas,poker);
if(!(_local6)){
_local7=undefined;
_local7=this.GetContinueValue(_local2,_local5);
_local2[_local2.length]=_local7;
}
        
}
        
}
_local4=(_local4 + 1);
_local4=0;
}
}
        
_local2.sort(function () { __FUNC_17__ });
_local3=function () { __FUNC_18__ };
if(_local0.length){
_local3(_local2[0],_local0,3);
_local3(_local2[1],_local0,5);
_local3(_local2[2],_local0,5);
}
if((_local2[0].length != 3) || (_local2[1].length != 5) || (_local2[2].length != 5)){
return false;
}
return this.CheckLine(tonghuas[0]) && this.CheckLine(tonghuas[1]) && this.CheckLine(tonghuas[2]);
---------------------------------------
==========================================================================E
==================================17==================================S
------------------Argv------------------
a,b
---------------------------------------
----------------Content----------------
return (a.length - b.length);
---------------------------------------
==========================================================================E
==================================18==================================S
------------------Argv------------------
shunzi,gui,len
---------------------------------------
----------------Content----------------
if((shunzi.length == len)){
return undefined;
}
_local0=0;
while((_local0 < gui.length)){
if((gui[_local0] == "undefined")){
shunzi.push(gui[_local0]);
gui[_local0]="undefined";
} else if((shunzi.length == len)){
break;
}
_local0=(_local0 + 1);
_local0=0;
}
}
        
---------------------------------------
==========================================================================E
==================================19==================================S
------------------Argv------------------
pokers,gui,tonghuas,JoinGuiPia
---------------------------------------
----------------Content----------------
if((pokers.length != 13)){
return false;
}
_local0=this.GetGuiPai(pokers);
_local1=[];
_local3=0;
while((_local3 < pokers.length)){
_local6=undefined;
_local5=undefined;
_local4=undefined;
_local4=pokers[_local3];
if((_local0.indexOf(_local4) != -1)){
}
        
_local5=this.GetSameColor(pokers,_local4);
_local6=this.CheckPokerInList(_local1,_local4);
} else if(!(_local6)){
_local1[_local1.length]=_local5;
}
        
}
_local3=(_local3 + 1);
_local3=0;
}
}
        
_local1.sort(function () { __FUNC_20__ });
_local2=function () { __FUNC_21__ };
if(_local0.length){
_local2(_local1[0],_local0,3);
_local2(_local1[1],_local0,5);
_local2(_local1[2],_local0,5);
}
if((_local1[0].length != 3) || (_local1[1].length != 5) || (_local1[2].length != 5)){
return false;
}
return this.isSameColorEx(_local1[0]) && this.isSameColorEx(_local1[1]) && this.isSameColorEx(_local1[2]);
---------------------------------------
==========================================================================E
==================================20==================================S
------------------Argv------------------
a,b
---------------------------------------
----------------Content----------------
return (a.length - b.length);
---------------------------------------
==========================================================================E
==================================21==================================S
------------------Argv------------------
tonghua,gui,len
---------------------------------------
----------------Content----------------
if((tonghua.length == len)){
return undefined;
}
_local0=0;
while((_local0 < gui.length)){
if((gui[_local0] == "undefined")){
tonghua.push(gui[_local0]);
gui[_local0]="undefined";
} else if((tonghua.length == len)){
break;
}
_local0=(_local0 + 1);
_local0=0;
}
}
        
---------------------------------------
==========================================================================E
==================================22==================================S
------------------Argv------------------
target
---------------------------------------
----------------Content----------------
return JSON.parse(JSON.stringify(target));
---------------------------------------
==========================================================================E
==================================23==================================S
------------------Argv------------------
poker,temp
---------------------------------------
----------------Content----------------
return poker;
_local0="";
if((poker.length > 4)){
_local0=poker;
_local0=_local0.substring(0,(_local0.length - 1));
} else {
}
_local0=poker;
return _local0;
---------------------------------------
==========================================================================E
==================================24==================================S
------------------Argv------------------
a,b
---------------------------------------
----------------Content----------------
return (a - b);
---------------------------------------
==========================================================================E
==================================25==================================S
------------------Argv------------------
cards,name,ColorArr,szNewCards,i,val,col
---------------------------------------
----------------Content----------------
_local0=["方块","梅花","红桃","黑桃","","大鬼"];
_local1=[];
_local2=0;
while((_local2 < cards.length)){
_local3=this.GetCardValue(cards[_local2]);
_local4=parseInt((this.GetCardColor(cards[_local2]) / 16));
if((_local3 == 11)){
_local3="J";
} else if((_local3 == 12)){
_local3="Q";
} else if((_local3 == 13)){
_local3="K";
} else if((_local3 == 14)){
_local3="A";
}
_local1.push(((_local0[_local4] + " ") + _local3));
_local2=(_local2 + 1);
_local2=0;
}
print(((("++++++++++++++++++++++++++ " + name) + " = ") + JSON.stringify(_local1)));
---------------------------------------
==========================================================================E
==================================26==================================S
------------------Argv------------------
cards,_this,cardtype,valueScore,colorScore
---------------------------------------
----------------Content----------------
_aliased8982=this;
_local1=this.CheckCardType(cards);
this.printCards(cards,"");
cards.sort(function () { __FUNC_27__ });
this.printCards(cards,"");
_local2=this.GetCardValue(cards[0]);
_local3=parseInt((this.GetCardColor(cards[0]) / 16));
return ((((_local1 + 1) * 1000) + (_local2 * 10)) + _local3);
---------------------------------------
==========================================================================E
==================================27==================================S
------------------Argv------------------
a,b
---------------------------------------
----------------Content----------------
return (_aliased8982.GetCardValue(b) - _aliased8982.GetCardValue(a));
---------------------------------------
==========================================================================E
==================================28==================================S
------------------Argv------------------
cards
---------------------------------------
----------------Content----------------
if((this.GetWuTong(cards).length != 0)){
return 10;
} else if((this.GetTongHuaShun(cards).length != 0)){
return 9;
} else if((this.GetZhaDang(cards,true).length != 0)){
return 8;
} else if((this.GetHulu(cards).length != 0)){
return 7;
} else if((this.GetLiangDuiTongHua(cards).length != 0)){
return 6;
} else if((this.GetYiDuiTongHua(cards).length != 0)){
return 5;
} else if((this.GetTonghuaEx(cards).length != 0)){
return 4;
} else if((this.GetShunziEx(cards).length != 0)){
return 3;
} else if((this.GetSanTiaoEx(cards,true).length != 0)){
return 2;
} else if((this.GetLiangDui(cards).length != 0)){
return 1;
} else if((this.GetDuiZi(cards,true).length != 0)){
return 0;
} else {
}
return -1;
---------------------------------------
==========================================================================E
==================================29==================================S
------------------Argv------------------
cards,allCardType
---------------------------------------
----------------Content----------------
this.lastMaxCardType_1=[];
this.lastMaxCardType_2=[];
this.lastMaxCardType_3=[];
_local0=[];
_local1=0;
break;
while((_local1 < 1)){
_local4=undefined;
_local3=undefined;
_local2=undefined;
_local2={};
_local3=[];
_local4=this.copyArr(cards);
if((this.GetWuTong(_local4).length != 0) && this.CheckUsedCardType(_local3,10) && (_local3.length < 2)){
_local5=0;
while((_local5 < this.GetWuTong(_local4).length)){
_local6=undefined;
_local6=this.GetWuTong(_local4)[0];
_local3.push({cardType:10,cardList:_local6});
this.RemoveArrFormAll(_local4,_local6);
}
        
_local5=(_local5 + 1);
_local5=0;
}
}
        
}
if((this.GetTongHuaShunEx(_local4).length != 0) && this.CheckUsedCardType(_local3,9) && (_local3.length < 2)){
_local5=0;
while((_local5 < this.GetTongHuaShunEx(_local4).length)){
_local6=undefined;
_local6=this.GetTongHuaShunEx(_local4)[0];
_local3.push({cardType:9,cardList:_local6});
this.RemoveArrFormAll(_local4,_local6);
}
        
_local5=(_local5 + 1);
_local5=0;
}
}
        
}
if((this.GetZhaDang(_local4).length != 0) && this.CheckUsedCardType(_local3,8) && (_local3.length < 2)){
_local5=0;
while((_local5 < this.GetZhaDang(_local4).length)){
_local6=undefined;
_local6=this.GetZhaDang(_local4)[0];
_local3.push({cardType:8,cardList:_local6});
this.RemoveArrFormAll(_local4,_local6);
}
        
_local5=(_local5 + 1);
_local5=0;
}
}
        
}
if((this.GetHuluEx(_local4).length != 0) && this.CheckUsedCardType(_local3,7) && (_local3.length < 2)){
_local5=0;
while((_local5 < this.GetHuluEx(_local4).length)){
_local6=undefined;
_local6=this.GetHuluEx(_local4)[0];
_local3.push({cardType:7,cardList:_local6});
this.RemoveArrFormAll(_local4,_local6);
}
        
_local5=(_local5 + 1);
_local5=0;
}
}
        
}
if((this.GetTonghua(_local4).length != 0) && this.CheckUsedCardType(_local3,4) && (_local3.length < 2)){
_local5=0;
while((_local5 < this.GetTonghua(_local4).length)){
_local6=undefined;
_local6=this.GetTonghua(_local4)[0];
_local3.push({cardType:4,cardList:_local6});
this.RemoveArrFormAll(_local4,_local6);
}
        
_local5=(_local5 + 1);
_local5=0;
}
}
        
}
if((this.GetShunzi(_local4).length != 0) && this.CheckUsedCardType(_local3,3) && (_local3.length < 2)){
_local5=0;
while((_local5 < this.GetShunzi(_local4).length)){
_local6=undefined;
_local6=this.GetShunzi(_local4)[0];
if((this.GetTongHuaShun(_local6).length > 0)){
}
        
} else {
}
_local3.push({cardType:3,cardList:_local6});
this.RemoveArrFormAll(_local4,_local6);
}
        
_local5=(_local5 + 1);
_local5=0;
}
}
        
}
if((this.GetSanTiao(_local4).length != 0)&&this.CheckUsedCardType(_local3,2)){
_local5=0;
while((_local5 < this.GetSanTiao(_local4).length)){
_local6=undefined;
_local6=this.GetSanTiao(_local4)[0];
_local3.push({cardType:2,cardList:_local6});
this.RemoveArrFormAll(_local4,_local6);
}
        
_local5=(_local5 + 1);
_local5=0;
}
}
        
}
if((this.GetLiangDuiEX(_local4).length != 0) && this.CheckUsedCardType(_local3,1) && (_local3.length < 2)){
_local5=0;
while((_local5 < this.GetLiangDuiEX(_local4).length)){
_local6=undefined;
_local6=this.GetLiangDuiEX(_local4)[0];
_local3.push({cardType:1,cardList:_local6});
this.RemoveArrFormAll(_local4,_local6);
}
        
_local5=(_local5 + 1);
_local5=0;
}
}
        
}
if((this.GetDuiZi(_local4).length != 0)&&this.CheckUsedCardType(_local3,0)){
_local5=0;
while((_local5 < this.GetDuiZi(_local4).length)){
_local6=undefined;
_local6=this.GetDuiZi(_local4)[0];
_local3.push({cardType:0,cardList:_local6});
this.RemoveArrFormAll(_local4,_local6);
}
        
_local5=(_local5 + 1);
_local5=0;
}
}
        
}
this.SortCardByMin(_local4);
_local5=0;
while((_local5 < _local3.length)){
_local6=undefined;
_local6=_local3[_local5].cardType;
if((_local6 == 8)){
if((_local4.length > 0)){
_local7=undefined;
_local7=this.GetSingleCardFormArr(_local4,1,_local3[_local5].cardList);
_local3[_local5].cardList=_local3[_local5].cardList.concat(_local7);
this.RemoveArrFormAll(_local4,_local7);
}
        
} else {
}
} else 
if((_local4.length >= 2)){
_local7=undefined;
_local7=this.GetSingleCardFormArr(_local4,2,_local3[_local5].cardList);
_local3[_local5].cardList=_local3[_local5].cardList.concat(_local7);
this.RemoveArrFormAll(_local4,_local7);
}
        
} else {
}
} else 
if((_local4.length >= 1)){
_local7=undefined;
_local7=this.GetSingleCardFormArr(_local4,1,_local3[_local5].cardList);
_local3[_local5].cardList=_local3[_local5].cardList.concat(_local7);
this.RemoveArrFormAll(_local4,_local7);
}
        
} else {
}
} else 
if((_local4.length >= 1)){
_local7=undefined;
_local7=this.GetSingleCardFormArr(_local4,1,_local3[_local5].cardList);
_local3[_local5].cardList=_local3[_local5].cardList.concat(_local7);
this.RemoveArrFormAll(_local4,_local7);
}
        
} else {
}
} else 
if((_local4.length >= 3)){
_local7=undefined;
_local7=this.GetSingleCardFormArr(_local4,3,_local3[_local5].cardList);
_local3[_local5].cardList=_local3[_local5].cardList.concat(_local7);
this.RemoveArrFormAll(_local4,_local7);
}
        
} else {
}
        
}}
_local5=(_local5 + 1);
_local5=0;
}
}
        
this.SortCardByMax(_local4);
if((_local3.length == 1)){
_local6=undefined;
_local5=undefined;
_local5=this.GetSingleCardFormArr(_local4,5,[]);
_local3.push({cardType:-1,cardList:_local5});
this.RemoveArrFormAll(_local4,_local5);
_local6=this.GetSingleCardFormArr(_local4,3,[]);
_local3.push({cardType:-1,cardList:_local6});
this.RemoveArrFormAll(_local4,_local6);
}
        
}
if((_local3.length == 2)){
_local5=undefined;
_local5=this.GetSingleCardFormArr(_local4,3,[]);
_local3.push({cardType:-1,cardList:_local5});
this.RemoveArrFormAll(_local4,_local5);
}
        
}
if((_local3.length > 0)&&(_local4.length == 0)){
_local0.push(_local3);
}
        
}
_local1=(_local1 + 1);
_local1=0;
}
}
        
return _local0;
---------------------------------------
==========================================================================E
==================================30==================================S
------------------Argv------------------
cards,allCardType,nTongHuaShunCnt,hulus,nTongHuaCnt,nShunZiCnt,nLen,flag1,flag2
---------------------------------------
----------------Content----------------
this.lastMaxCardType_1=[];
this.lastMaxCardType_2=[];
this.lastMaxCardType_3=[];
_local0=[];
_local8=0;
break;
while((_local8 < 5)){
_local11=undefined;
_local10=undefined;
_local9=undefined;
print(("" + _local8));
_local9={};
_local10=[];
_local11=this.copyArr(cards);
if((this.GetWuTong(_local11).length != 0) && this.CheckUsedCardType2(_local10,10) && (_local10.length < 2)){
_local12=0;
while((_local12 < this.GetWuTong(_local11).length)){
_local13=undefined;
_local13=this.GetWuTong(_local11)[0];
print(("++++++++++++++++++++ wutong = " + JSON.stringify(_local13)));
_local10.push({cardType:10,cardList:_local13});
this.RemoveArrFormAll(_local11,_local13);
}
        
_local12=(_local12 + 1);
_local12=0;
}
}
        
}
_local1=this.GetTongHuaShunEx(_local11).length;
if((this.GetTongHuaShunEx(_local11).length != 0) && this.CheckUsedCardType2(_local10,9) && (_local10.length < 2)){
_local12=0;
while((_local12 < this.GetTongHuaShunEx(_local11).length)){
_local13=undefined;
_local13=this.GetTongHuaShunEx(_local11)[0];
_local10.push({cardType:9,cardList:_local13});
this.RemoveArrFormAll(_local11,_local13);
}
        
_local12=(_local12 + 1);
_local12=0;
}
}
        
}
if((this.GetZhaDang(_local11).length != 0) && this.CheckUsedCardType2(_local10,8) && (_local10.length < 2)){
_local12=0;
while((_local12 < this.GetZhaDang(_local11).length)){
_local13=undefined;
_local13=this.GetZhaDang(_local11)[0];
print(("++++++++++++++++++++ zhaDan = " + JSON.stringify(_local13)));
_local10.push({cardType:8,cardList:_local13});
this.RemoveArrFormAll(_local11,_local13);
}
        
_local12=(_local12 + 1);
_local12=0;
}
}
        
}
if((this.GetHuluEx(_local11).length != 0) && this.CheckUsedCardType2(_local10,7) && (_local10.length < 2)){
_local2=this.GetHuluEx(_local11);
_local12=0;
while((_local12 < this.GetHuluEx(_local11).length)){
_local13=undefined;
_local13=this.GetHuluEx(_local11)[0];
_local10.push({cardType:7,cardList:_local13});
this.RemoveArrFormAll(_local11,_local13);
}
        
_local12=(_local12 + 1);
_local12=0;
}
}
        
}
_local3=this.GetTonghua(_local11).length;
if((this.GetTonghua(_local11).length != 0) && this.CheckUsedCardType2(_local10,4) && (_local10.length < 2)){
_local12=0;
while((_local12 < this.GetTonghua(_local11).length)){
_local13=undefined;
_local13=this.GetTonghua(_local11)[0];
_local10.push({cardType:4,cardList:_local13});
this.RemoveArrFormAll(_local11,_local13);
}
        
_local12=(_local12 + 1);
_local12=0;
}
}
        
}
_local4=this.GetShunzi(_local11).length;
if((this.GetShunzi(_local11).length != 0) && this.CheckUsedCardType2(_local10,3) && (_local10.length < 2)){
_local12=0;
while((_local12 < this.GetShunzi(_local11).length)){
_local13=undefined;
_local13=this.GetShunzi(_local11)[0];
if((this.GetTongHuaShun(_local13).length > 0)){
}
        
} else {
}
_local10.push({cardType:3,cardList:_local13});
this.RemoveArrFormAll(_local11,_local13);
}
        
_local12=(_local12 + 1);
_local12=0;
}
}
        
}
if((this.GetSanTiao(_local11).length != 0)&&this.CheckUsedCardType2(_local10,2)){
_local12=0;
while((_local12 < this.GetSanTiao(_local11).length)){
_local13=undefined;
_local13=this.GetSanTiao(_local11)[_local12];
print(("++++++++++++++++++++ sanTiao = " + JSON.stringify(_local13)));
_local10.push({cardType:2,cardList:_local13});
this.RemoveArrFormAll(_local11,_local13);
}
        
_local12=(_local12 + 1);
_local12=0;
}
}
        
}
if((this.GetLiangDuiEX(_local11).length != 0) && this.CheckUsedCardType2(_local10,1) && (_local10.length < 2)){
_local12=0;
while((_local12 < this.GetLiangDuiEX(_local11).length)){
_local13=undefined;
_local13=this.GetLiangDuiEX(_local11)[0];
_local10.push({cardType:1,cardList:_local13});
this.RemoveArrFormAll(_local11,_local13);
}
        
_local12=(_local12 + 1);
_local12=0;
}
}
        
}
_local5=this.GetDuiZi(_local11).length;
if((this.GetDuiZi(_local11).length != 0)&&this.CheckUsedCardType2(_local10,0)){
_local12=0;
while((_local12 < this.GetDuiZi(_local11).length)){
_local13=undefined;
_local13=this.GetDuiZi(_local11)[0];
_local10.push({cardType:0,cardList:_local13});
this.RemoveArrFormAll(_local11,_local13);
}
        
_local12=(_local12 + 1);
_local12=0;
}
}
        
}
print(((_local8 + "") + JSON.stringify(_local10)));
print(((_local8 + "") + JSON.stringify(_local11)));
this.SortCardByMin(_local11);
_local12=0;
while((_local12 < _local10.length)){
_local13=undefined;
_local13=_local10[_local12].cardType;
if((_local13 == 8)){
if((_local11.length > 0)){
_local14=undefined;
_local14=this.GetSingleCardFormArr(_local11,1,_local10[_local12].cardList);
_local10[_local12].cardList=_local10[_local12].cardList.concat(_local14);
this.RemoveArrFormAll(_local11,_local14);
}
        
} else {
}
} else 
if((_local11.length >= 2)){
_local14=undefined;
_local14=this.GetSingleCardFormArr(_local11,2,_local10[_local12].cardList);
if((_local14.length >= 2)){
_local10[_local12].cardList=_local10[_local12].cardList.concat(_local14);
this.RemoveArrFormAll(_local11,_local14);
}
        
}
} else {
}
console.log("");
} else if((_local13 == 1)){
if((_local11.length >= 1)){
_local14=undefined;
_local14=this.GetSingleCardFormArr(_local11,1,_local10[_local12].cardList);
_local10[_local12].cardList=_local10[_local12].cardList.concat(_local14);
this.RemoveArrFormAll(_local11,_local14);
}
        
} else {
}
} else 
if((_local11.length >= 1)){
_local14=undefined;
_local14=this.GetSingleCardFormArr(_local11,1,_local10[_local12].cardList);
_local10[_local12].cardList=_local10[_local12].cardList.concat(_local14);
this.RemoveArrFormAll(_local11,_local14);
}
        
} else {
}
} else 
if((_local11.length >= 3)){
_local14=undefined;
_local14=this.GetSingleCardFormArr(_local11,3,_local10[_local12].cardList);
_local10[_local12].cardList=_local10[_local12].cardList.concat(_local14);
this.RemoveArrFormAll(_local11,_local14);
}
        
} else {
}
        
}}
_local12=(_local12 + 1);
_local12=0;
}
}
        
this.SortCardByMax(_local11);
if((_local10.length == 1)){
_local13=undefined;
_local12=undefined;
_local12=this.GetSingleCardFormArr(_local11,5,[]);
_local10.push({cardType:-1,cardList:_local12});
this.RemoveArrFormAll(_local11,_local12);
_local13=this.GetSingleCardFormArr(_local11,3,[]);
_local10.push({cardType:-1,cardList:_local13});
this.RemoveArrFormAll(_local11,_local13);
}
        
}
if((_local10.length == 2)){
_local12=undefined;
_local12=this.GetSingleCardFormArr(_local11,3,[]);
_local10.push({cardType:-1,cardList:_local12});
this.RemoveArrFormAll(_local11,_local12);
}
        
}
if((_local10.length > 2)){
_local6=this.CheckCardBigOrSmall(_local10[1].cardList,_local10[2].cardList);
_local7=this.CheckCardBigOrSmall(_local10[0].cardList,_local10[1].cardList);
}
if((_local10.length > 0)){
print(("===++++++++++ curCardList[0].cardList = " + JSON.stringify(_local10[0].cardList)));
print(("===++++++++++ curCardList[1].cardList = " + JSON.stringify(_local10[1].cardList)));
print(("===++++++++++ curCardList[2].cardList = " + JSON.stringify(_local10[2].cardList)));
}
if((_local10.length > 0) && (_local11.length == 0) || (_local11.length == 1)){
if((_local10[2].cardList.length < 3)&&(_local11.length == 1)){
_local10[2].cardList.push(_local11[0]);
_local11.splice(0,1);
}
if((this.CheckCardBigOrSmall(_local10[1].cardList,_local10[2].cardList) == 0)&&(this.CheckCardBigOrSmall(_local10[0].cardList,_local10[1].cardList) == 0)){
_local0.push(_local10);
}
        
}}
_local8=(_local8 + 1);
_local8=0;
}
}
        
return _local0;
---------------------------------------
==========================================================================E
==================================31==================================S
------------------Argv------------------
targetArr,curArr
---------------------------------------
----------------Content----------------
_local0=0;
while((_local0 < curArr.length)){
_local1=undefined;
_local1=targetArr.indexOf(curArr[_local0]);
if((_local1 > -1)){
targetArr.splice(_local1,1);
}
        
}
_local0=(_local0 + 1);
_local0=0;
}
}
        
---------------------------------------
==========================================================================E
==================================32==================================S
------------------Argv------------------
cardArr,cardNum,concatArr,singleCards
---------------------------------------
----------------Content----------------
_local0=[];
_local1=0;
while((_local1 < cardArr.length)){
_local3=undefined;
_local2=undefined;
_local2=this.CheckSameValueInArr(_local0,cardArr[_local1]);
_local3=this.CheckSameValueInArr(concatArr,cardArr[_local1]);
if(!(_local2) && !(_local3)){
_local0.push(cardArr[_local1]);
if((_local0.length == cardNum)){
_local0=[];
}
        
}
        
}
        
}}
_local1=(_local1 + 1);
_local1=0;
}
}
        
return _local0;
---------------------------------------
==========================================================================E
==================================33==================================S
------------------Argv------------------
curCardList,cardType
---------------------------------------
----------------Content----------------
if((curCardList.length == 0)){
if((this.lastMaxCardType_1.indexOf(cardType) < 0)){
this.lastMaxCardType_1.push(cardType);
return true;
}
} else 
if((this.lastMaxCardType_2.indexOf(cardType) < 0)){
this.lastMaxCardType_2.push(cardType);
return true;
}
} else 
if((this.lastMaxCardType_3.indexOf(cardType) < 0)){
this.lastMaxCardType_3.push(cardType);
return true;
}}
return false;
---------------------------------------
==========================================================================E
==================================34==================================S
------------------Argv------------------
curCardList,cardType
---------------------------------------
----------------Content----------------
if((curCardList.length == 0)){
if((this.lastMaxCardType_1.indexOf(cardType) < 0)){
this.lastMaxCardType_1.push(cardType);
return true;
}
} else 
if((this.lastMaxCardType_2.indexOf(cardType) < 0)){
this.lastMaxCardType_2.push(cardType);
return true;
}
} else 
if((this.lastMaxCardType_3.indexOf(cardType) < 0)){
this.lastMaxCardType_3.push(cardType);
return true;
}}
return false;
---------------------------------------
==========================================================================E
==================================35==================================S
------------------Argv------------------
pokers,guiList,duizis,aList
---------------------------------------
----------------Content----------------
_local0=[];
_local1=0;
while((_local1 < pokers.length)){
_local3=undefined;
_local2=undefined;
if((guiList.indexOf(pokers[_local1]) != -1)){
}
        
} else if(duizis[0]){
if((duizis[0].indexOf(pokers[_local1]) != -1)){
}
        
} else {
}}
_local2={};
_local3=this.GetCardValue(pokers[_local1]);
_local2.value=_local3;
_local2.valueX16=pokers[_local1];
_local0.push(_local2);
}
        
_local1=(_local1 + 1);
_local1=0;
}
}
        
_local0.sort(function () { __FUNC_36__ });
if(_local0.length&&guiList.length){
_local1=undefined;
_local1=[];
_local1.push(_local0[0].valueX16);
_local1.push(guiList[0]);
duizis[duizis.length]=_local1;
}
        
}
---------------------------------------
==========================================================================E
==================================36==================================S
------------------Argv------------------
a,b
---------------------------------------
----------------Content----------------
return (b.value - a.value);
---------------------------------------
==========================================================================E
==================================37==================================S
------------------Argv------------------
aCards,bCards,tempSortAcards,tempSortBcards,aValue,bValue,duiziAs,duiziBs,guiAs,guiBs,nLen
---------------------------------------
----------------Content----------------
_local0=[];
_local1=[];
_local9=0;
while((_local9 < aCards.length)){
_local0.push(this.GetCardValue(aCards[_local9]));
_local9=(_local9 + 1);
_local9=0;
}
}
        
_local9=0;
while((_local9 < bCards.length)){
_local1.push(this.GetCardValue(bCards[_local9]));
_local9=(_local9 + 1);
_local9=0;
}
}
        
_local0.sort(this.sortFun);
_local1.sort(this.sortFun);
_local2=-1;
_local3=-1;
_local4=this.GetDuiZi(aCards,false);
_local5=this.GetDuiZi(bCards,false);
_local6=this.GetGuiPai(aCards);
_local7=this.GetGuiPai(bCards);
if((this.GetWuTong(aCards).length != 0)){
if((this.GetWuTong(bCards).length != 0)){
_local9=0;
while((_local9 < aCards.length)){
if((_local6.indexOf(aCards[_local9]) != -1)){
} else {
}
_local2=this.GetCardValue(aCards[_local9]);
break;
_local9=(_local9 + 1);
_local9=0;
}
}
        
_local9=0;
while((_local9 < bCards.length)){
if((_local7.indexOf(bCards[_local9]) != -1)){
} else {
}
_local3=this.GetCardValue(bCards[_local9]);
break;
_local9=(_local9 + 1);
_local9=0;
}
}
        
if((_local2 > _local3)){
return 0;
} else if((_local2 < _local3)){
return 1;
} else if((_local2 > _local3)){
return 0;
} else if((_local2 < _local3)){
return 1;
} else if(!(_local6.length)&&_local7.length){
return 1;
} else if(_local6.length && !(_local7.length)){
return 0;
} else {
}
return 2;
} else {
}
return 0;
} else if((this.GetWuTong(bCards).length != 0)){
return 1;
}
if((this.GetTongHuaShun(aCards).length != 0)){
if((this.CheckCardType(bCards) > 9)){
return 1;
break;
if((this.CheckCardType(bCards) == 9)){
_local14=undefined;
_local13=undefined;
_local12=undefined;
_local11=undefined;
_local10=undefined;
_local9=undefined;
_local9=[];
_local10=[];
_local11=-1;
_local12=-1;
_local15=0;
while((_local15 < aCards.length)){
_local16=undefined;
} else if((_local6.indexOf(aCards[_local15]) != -1)){
}
        
_local16=this.GetCardValue(aCards[_local15]);
_local9.push(_local16);
} else if((_local11 == -1)){
_local11=parseInt((this.GetCardColor(aCards[_local15]) / 16));
}
        
}
_local15=(_local15 + 1);
_local15=0;
}
}
        
_local15=0;
while((_local15 < bCards.length)){
_local16=undefined;
if((_local7.indexOf(bCards[_local15]) != -1)){
}
        
_local16=this.GetCardValue(bCards[_local15]);
_local10.push(_local16);
} else if((_local12 == -1)){
_local12=parseInt((this.GetCardColor(bCards[_local15]) / 16));
}
        
}
_local15=(_local15 + 1);
_local15=0;
}
}
        
_local9.sort(this.sortFun);
_local10.sort(this.sortFun);
_local0=this.GetCompleteShun(_local9);
_local1=this.GetCompleteShun(_local10);
_local2=_local0[(_local0.length - 1)];
_local13=_local0[0];
_local3=_local1[(_local1.length - 1)];
_local14=_local1[0];
print(("++++++++ aValue = " + _local2));
print(("++++++++ bValue = " + _local3));
print(("++++++++ aFirValue = " + _local13));
print(("++++++++ bFirValue = " + _local14));
print(("++++++++ aColor = " + _local11));
print(("++++++++ bColor = " + _local12));
if((_local2 > _local3)){
if((14 == _local3)&&(2 == _local14)){
1;
}
        
}
}
        
} else if((_local2 < _local3)){
if((14 == _local2)&&(2 == _local13)){
}
        
}
1;
}
        
} else if((_local11 > _local12)){
}
        
}
1;
}
        
}
        
} else {
}
return 0;
} else if((this.GetTongHuaShun(bCards).length != 0)){
return 1;
}
_local8=this.GetZhaDang(aCards,true).length;
print(("" + _local8));
if((this.GetZhaDang(aCards,true).length != 0)){
if((this.CheckCardType(bCards) > 8)){
return 1;
break;
if((this.GetZhaDang(bCards,true).length != 0)){
_local9=0;
while((_local9 < aCards.length)){
_local11=undefined;
_local10=undefined;
_local10=aCards[_local9];
_local11=this.GetSameValue(aCards,_local10);
} else if((_local11.length >= 2)){
_local2=this.GetCardValue(_local11[0]);
}
        
break;
}
        
_local9=(_local9 + 1);
_local9=0;
}
}
        
_local9=0;
while((_local9 < bCards.length)){
_local11=undefined;
_local10=undefined;
_local10=bCards[_local9];
_local11=this.GetSameValue(bCards,_local10);
} else if((_local11.length >= 2)){
_local3=this.GetCardValue(_local11[0]);
}
        
break;
}
        
_local9=(_local9 + 1);
_local9=0;
}
}
        
if((_local2 > _local3)){
return 0;
} else if((_local2 < _local3)){
return 1;
} else if(!(_local6.length)&&_local7.length){
return 1;
} else if(_local6.length && !(_local7.length)){
return 0;
} else {
}
return 2;
} else {
}
return 0;
} else if((this.GetZhaDang(bCards,true).length != 0)){
return 1;
}
if((this.GetHulu(aCards).length != 0)){
if((this.CheckCardType(bCards) > 7)){
return 1;
break;
if((this.CheckCardType(bCards) == 7)){
if(_local6.length&&_local7.length){
_local10=undefined;
_local9=undefined;
_local9=[];
_local10=[];
_local11=0;
while((_local11 < aCards.length)){
_local12=undefined;
} else if((_local6.indexOf(aCards[_local11]) != -1)){
}
        
} else {
}
_local12=this.GetCardValue(aCards[_local11]);
_local9.push(_local12);
}
        
_local11=(_local11 + 1);
_local11=0;
}
}
        
_local11=0;
while((_local11 < bCards.length)){
_local12=undefined;
if((_local7.indexOf(bCards[_local11]) != -1)){
}
        
} else {
}
_local12=this.GetCardValue(bCards[_local11]);
_local10.push(_local12);
}
        
_local11=(_local11 + 1);
_local11=0;
}
}
        
_local9.sort(this.sortCardValue);
_local10.sort(this.sortCardValue);
_local2=this.GetCardValue(_local9[0]);
_local3=this.GetCardValue(_local10[0]);
if((_local2 > _local3)){
}
        
} else if((_local2 < _local3)){
1;
}
        
} else {
}
2;
}
        
}
        
break;
if(!(_local6.length)&&_local7.length){
_local10=undefined;
_local9=undefined;
_local9=[];
_local10=this.GetSanTiaoEx(aCards,false);
_local11=0;
while((_local11 < bCards.length)){
_local12=undefined;
} else if((_local7.indexOf(bCards[_local11]) != -1)){
}
        
} else {
}
_local12=this.GetCardValue(bCards[_local11]);
_local9.push(_local12);
}
        
_local11=(_local11 + 1);
_local11=0;
}
}
        
_local9.sort(this.sortCardValue);
_local2=this.GetCardValue(_local10[0]);
_local3=this.GetCardValue(_local9[0]);
if((_local2 > _local3)){
}
        
} else if((_local2 < _local3)){
1;
}
        
} else {
}
}
        
}
        
break;
if(_local6.length && !(_local7.length)){
_local10=undefined;
_local9=undefined;
_local9=[];
_local10=this.GetSanTiaoEx(bCards,false);
_local11=0;
while((_local11 < aCards.length)){
_local12=undefined;
} else if((_local6.indexOf(aCards[_local11]) != -1)){
}
        
} else {
}
_local12=this.GetCardValue(aCards[_local11]);
_local9.push(_local12);
}
        
_local11=(_local11 + 1);
_local11=0;
}
}
        
_local9.sort(this.sortCardValue);
_local2=this.GetCardValue(_local9[0]);
_local3=this.GetCardValue(_local10[0]);
if((_local2 > _local3)){
}
        
} else if((_local2 < _local3)){
1;
}
        
} else {
}
1;
}
        
}
        
} else if(!(_local6.length) && !(_local7.length)){
_local10=undefined;
_local9=undefined;
_local9=this.GetSanTiaoEx(aCards,false);
_local10=this.GetSanTiaoEx(bCards,false);
_local2=this.GetCardValue(_local9[0]);
_local3=this.GetCardValue(_local10[0]);
if((_local2 > _local3)){
}
        
} else if((_local2 < _local3)){
1;
}
        
} else {
}
2;
}
        
}
        
} else {
}
}
return 0;
} else 
return 1;
}
if((this.GetLiangDuiTongHua(aCards).length != 0)){
if((this.CheckCardType(bCards) > 6)){
return 1;
break;
if((this.CheckCardType(bCards) == 6)){
_local12=undefined;
_local11=undefined;
_local10=undefined;
_local9=undefined;
_local9=[];
_local10=[];
_local13=0;
while((_local13 < aCards.length)){
_local14=undefined;
} else if((_local6.indexOf(aCards[_local13]) != -1)){
}
        
} else {
}
_local14=this.GetCardValue(aCards[_local13]);
_local9.push(_local14);
}
        
_local13=(_local13 + 1);
_local13=0;
}
}
        
_local13=0;
while((_local13 < bCards.length)){
_local14=undefined;
if((_local7.indexOf(bCards[_local13]) != -1)){
}
        
} else {
}
_local14=this.GetCardValue(bCards[_local13]);
_local10.push(_local14);
}
        
_local13=(_local13 + 1);
_local13=0;
}
}
        
_local9.sort(this.sortCardValue);
_local10.sort(this.sortCardValue);
_local11=[];
_local12=[];
_local13=undefined;
while(_local9 has _iternext){
_local13=_iternext;
if((_local11.indexOf(_local9[_local13]) == -1)){
_local11.push(_local9[_local13]);
}
}
}
        
_local13=undefined;
while(_local10 has _iternext){
_local13=_iternext;
if((_local12.indexOf(_local10[_local13]) == -1)){
_local12.push(_local10[_local13]);
}
}
}
        
_local2=this.GetCardValue(_local11[0]);
_local3=this.GetCardValue(_local12[0]);
if((_local2 > _local3)){
}
        
} else if((_local2 < _local3)){
1;
}
        
_local2=this.GetCardValue(_local11[1]);
_local3=this.GetCardValue(_local11[1]);
} else if((_local2 > _local3)){
}
        
} else if((_local2 < _local3)){
1;
}
        
_local2=this.GetCardValue(_local11[2]);
_local3=this.GetCardValue(_local11[2]);
} else if((_local2 > _local3)){
}
        
} else if((_local2 < _local3)){
1;
}
        
} else if(!(_local6.length)&&_local7.length){
}
        
} else if(_local6.length && !(_local7.length)){
1;
}
        
} else {
}
2;
}
        
}
        
} else {
}
return 0;
} else if((this.GetLiangDuiTongHua(bCards).length != 0)){
return 1;
}
if((this.GetYiDuiTongHua(aCards).length != 0)){
if((this.CheckCardType(bCards) > 5)){
return 1;
break;
if((this.CheckCardType(bCards) == 5)){
_local14=undefined;
_local13=undefined;
_local12=undefined;
_local11=undefined;
_local10=undefined;
_local9=undefined;
_local9=-1;
_local10=-1;
_local11=this.GetDuiZi(aCards,false);
_local12=this.GetDuiZi(bCards,false);
_local13=[];
_local14=[];
if(_local11.length){
_local9=this.GetCardValue(_local11[0][0]);
}
if(_local12.length){
_local10=this.GetCardValue(_local12[0][0]);
}
if((_local9 == -1)){
_local15=0;
while((_local15 < aCards.length)){
_local16=undefined;
} else if((_local6.indexOf(aCards[_local15]) != -1)){
}
        
} else {
}
_local16=this.GetCardValue(aCards[_local15]);
_local13.push(_local16);
}
        
_local15=(_local15 + 1);
_local15=0;
}
}
        
_local13.sort(this.sortCardValue);
_local9=_local13[0];
}
if((_local10 == -1)){
_local15=0;
while((_local15 < bCards.length)){
_local16=undefined;
if((_local7.indexOf(bCards[_local15]) != -1)){
}
        
} else {
}
_local16=this.GetCardValue(bCards[_local15]);
_local14.push(_local16);
}
        
_local15=(_local15 + 1);
_local15=0;
}
}
        
_local14.sort(this.sortCardValue);
_local10=_local14[0];
}
if((_local9 > _local10)){
}
        
break;
if((_local9 < _local10)){
1;
}
        
break;
_local16=undefined;
_local15=undefined;
_local15=[];
_local16=[];
_local17=0;
while((_local17 < aCards.length)){
_local18=undefined;
} else if((_local6.indexOf(aCards[_local17]) != -1)){
}
        
_local18=this.GetCardValue(aCards[_local17]);
} else if((_local9 == _local18)){
}
        
} else {
}
_local15.push(_local18);
}
        
_local17=(_local17 + 1);
_local17=0;
}
}
        
_local15.sort(this.sortCardValue);
_local17=0;
while((_local17 < bCards.length)){
_local18=undefined;
if((_local6.indexOf(bCards[_local17]) != -1)){
}
        
_local18=this.GetCardValue(bCards[_local17]);
} else if((_local10 == _local18)){
}
        
} else {
}
_local16.push(_local18);
}
        
_local17=(_local17 + 1);
_local17=0;
}
}
        
_local16.sort(this.sortCardValue);
_local17=0;
while((_local17 < _local15.length)){
_local2=_local15[_local17];
_local3=_local16[_local17];
if((_local2 > _local3)){
}
        
}
        
}
        
} else if((_local2 < _local3)){
1;
}
        
}
        
}
        
} else if((_local17 == (_local15.length - 1))){
if(!(_local6.length)&&_local7.length){
}
        
}
        
}
        
} else if(_local6.length && !(_local7.length)){
1;
}
        
}
        
}
        
} else {
}
2;
}
        
}
        
}
        
} else {
}
_local17=(_local17 + 1);
_local17=0;
}
}
        
}
        
}
        
} else {
}
return 0;
} else if((this.GetYiDuiTongHua(bCards).length != 0)){
return 1;
}
if((this.GetTonghuaEx(aCards).length != 0)){
if((this.CheckCardType(bCards) > 4)){
return 1;
break;
if((this.CheckCardType(bCards) == 4)){
_local2=_local0[(_local0.length - 1)];
_local3=_local1[(_local1.length - 1)];
if((_local2 > _local3)){
return 0;
break;
if((_local2 < _local3)){
return 1;
break;
_local9=undefined;
_local9=0;
if((_local0.length == _local1.length)){
_local9=_local0.length;
(_local0.length > _local1.length)?_local9=_local1.length:_local9=_local0.length;
_local10=_local9;
while((_local10 > 0)){
_local2=_local0[_local10];
_local3=_local1[_local10];
if((_local2 > _local3)){
}
        
}
        
} else if((_local2 < _local3)){
1;
}
        
}
        
} else if((0 == _local10)){
2;
}
        
}
        
}
_local10=(_local10 - 1);
_local10=_local9;
}
}
        
}
        
} else {
}
return 0;
} else if((this.GetTonghuaEx(bCards).length != 0)){
return 1;
}
if((this.GetShunziEx(aCards).length != 0)){
if((this.CheckCardType(bCards) > 3)){
return 1;
break;
if((this.CheckCardType(bCards) == 3)){
_local12=undefined;
_local11=undefined;
_local10=undefined;
_local9=undefined;
_local9=[];
_local10=[];
_local13=0;
while((_local13 < aCards.length)){
_local14=undefined;
} else if((_local6.indexOf(aCards[_local13]) != -1)){
}
        
} else {
}
_local14=this.GetCardValue(aCards[_local13]);
_local9.push(_local14);
}
        
_local13=(_local13 + 1);
_local13=0;
}
}
        
_local13=0;
while((_local13 < bCards.length)){
_local14=undefined;
if((_local7.indexOf(bCards[_local13]) != -1)){
}
        
} else {
}
_local14=this.GetCardValue(bCards[_local13]);
_local10.push(_local14);
}
        
_local13=(_local13 + 1);
_local13=0;
}
}
        
_local9.sort(this.sortFun);
_local10.sort(this.sortFun);
_local0=this.GetCompleteShun(_local9);
_local1=this.GetCompleteShun(_local10);
print(("++++++++++++++++++++ tempSortAcards = " + JSON.stringify(_local0)));
print(("++++++++++++++++++++ tempSortBcards = " + JSON.stringify(_local1)));
_local2=_local0[(_local0.length - 1)];
_local11=_local0[0];
_local3=_local1[(_local1.length - 1)];
_local12=_local1[0];
print(("++++++++++++++++++++ aValue = " + _local2));
print(("++++++++++++++++++++ bValue = " + _local3));
print(("++++++++++++++++++++ aFirValue = " + _local11));
print(("++++++++++++++++++++ bFirValue = " + _local12));
if((_local2 > _local3)){
if((14 == _local2)&&(2 == _local11)){
}
        
}
}
        
} else if((_local2 < _local3)){
if((14 == _local3)&&(2 == _local12)){
1;
}
        
}
1;
}
        
} else if((14 == _local2)&&(14 == _local3)){
_local2=_local0[(_local0.length - 2)];
_local3=_local1[(_local1.length - 2)];
if((_local2 > _local3)){
}
        
} else if((_local2 < _local3)){
1;
}
        
} else if(!(_local6.length)&&_local7.length){
}
        
} else if(_local6.length && !(_local7.length)){
1;
}
        
} else {
}
2;
}
        
} else if(!(_local6.length)&&_local7.length){
}
        
} else if(_local6.length && !(_local7.length)){
1;
}
        
} else {
}
2;
}
        
}
        
} else {
}
return 0;
} else if((this.GetShunziEx(bCards).length != 0)){
return 1;
}
if((this.GetSanTiaoEx(aCards).length != 0)){
if((this.CheckCardType(bCards) > 2)){
return 1;
break;
if((this.CheckCardType(bCards) == 2)){
if(_local6.length&&_local7.length){
_local12=undefined;
_local11=undefined;
_local10=undefined;
_local9=undefined;
_local9=this.GetDuiZi(aCards,false);
_local10=this.GetDuiZi(bCards,false);
_local11=0;
if(_local9[0]&&_local9[0][0]){
_local11=_local9[0][0];
}
_local12=0;
if(_local10[0]&&_local10[0][0]){
_local12=_local10[0][0];
}
_local2=this.GetCardValue(_local11);
_local3=this.GetCardValue(_local12);
if((_local2 > _local3)){
}
        
} else if((_local2 < _local3)){
1;
}
        
} else {
}
2;
}
        
}
        
break;
if(!(_local6.length)&&_local7.length){
_local9=undefined;
_local9=this.GetSanTiaoEx(aCards,false);
_local2=this.GetCardValue(_local9[0]);
if((_local7.length == 1)){
_local10=undefined;
_local10=this.GetDuiZi(bCards,false);
_local3=this.GetCardValue(_local10[0][0]);
}
        
break;
if((_local7.length == 2)){
_local10=undefined;
_local10=[];
_local11=0;
while((_local11 < bCards.length)){
} else if((_local7.indexOf(bCards[_local11]) != -1)){
} else {
}
_local10.push(bCards[_local11]);
_local11=(_local11 + 1);
_local11=0;
}
}
        
_local10.sort(this.sortCardValue);
_local3=this.GetCardValue(_local10[0]);
}
        
}
if((_local2 > _local3)){
}
        
} else if((_local2 < _local3)){
1;
}
        
} else {
}
}
        
}
        
break;
if(_local6.length && !(_local7.length)){
_local9=undefined;
_local9=this.GetSanTiaoEx(bCards,false);
_local3=this.GetCardValue(_local9[0]);
if((_local6.length == 1)){
_local10=undefined;
_local10=this.GetDuiZi(aCards,false);
_local2=this.GetCardValue(_local10[0][0]);
}
        
break;
if((_local6.length == 2)){
_local10=undefined;
_local10=[];
_local11=0;
while((_local11 < aCards.length)){
} else if((_local6.indexOf(aCards[_local11]) != -1)){
} else {
}
_local10.push(aCards[_local11]);
_local11=(_local11 + 1);
_local11=0;
}
}
        
_local10.sort(this.sortCardValue);
_local2=this.GetCardValue(_local10[0]);
}
        
}
if((_local2 > _local3)){
}
        
} else if((_local2 < _local3)){
1;
}
        
} else {
}
1;
}
        
}
        
} else if(!(_local6.length) && !(_local7.length)){
_local10=undefined;
_local9=undefined;
_local9=this.GetSanTiaoEx(aCards,false);
_local10=this.GetSanTiaoEx(bCards,false);
_local2=this.GetCardValue(_local9[0]);
_local3=this.GetCardValue(_local10[0]);
if((aCards.length < 3)&&(bCards.length == 5)){
1;
}
        
} else if((_local2 > _local3)){
}
        
} else {
}
2;
}
        
}
        
} else {
}
}
return 0;
} else 
return 1;
}
if(_local6.length && !(_local4.length)){
this.CheckDuiziByGui(aCards,_local6,_local4);
} else if(_local6.length&&(_local4.length == 1)){
this.CheckDuiziByGui(aCards,_local6,_local4);
}
if(_local7.length && !(_local5.length)){
this.CheckDuiziByGui(bCards,_local7,_local5);
} else if(_local7.length&&(_local5.length == 1)){
this.CheckDuiziByGui(bCards,_local7,_local5);
}
if((_local4.length == 2)){
if((this.CheckCardType(bCards) > 1)){
return 1;
break;
if((_local5.length == 2)){
_local12=undefined;
_local11=undefined;
_local10=undefined;
_local9=undefined;
_local9=[];
_local10=[];
_local9[0]=this.GetCardValue(_local4[0][0]);
_local9[1]=this.GetCardValue(_local4[1][0]);
_local10[0]=this.GetCardValue(_local5[0][0]);
_local10[1]=this.GetCardValue(_local5[1][0]);
_local9.sort(this.sortFun);
_local10.sort(this.sortFun);
_local2=_local9[0];
_local3=_local10[0];
_local11=_local9[1];
_local12=_local10[1];
if((_local2 == _local3)&&(_local11 == _local12)){
_local14=undefined;
_local13=undefined;
_local13=-1;
_local14=-1;
_local15=0;
while((_local15 < 5)){
} else if((this.GetCardValue(aCards[_local15]) != _local9[0])&&(this.GetCardValue(aCards[_local15]) != _local9[1])){
_local13=this.GetCardValue(aCards[_local15]);
}
if((this.GetCardValue(bCards[_local15]) != _local10[0])&&(this.GetCardValue(bCards[_local15]) != _local10[1])){
_local14=this.GetCardValue(bCards[_local15]);
}
_local15=(_local15 + 1);
_local15=0;
}
}
        
if((_local13 > _local14)){
}
        
}
        
} else {
}
1;
}
        
}
        
}
        
}
if((_local11 > _local12)){
}
        
} else if((_local11 == _local12)){
if((_local2 > _local3)){
}
        
} else {
}
1;
}
        
} else {
}
1;
}
        
}
        
} else {
}
return 0;
} else if((_local5.length == 2)){
return 1;
}
if((_local4.length == 1)){
if((this.CheckCardType(bCards) > 0)){
return 1;
break;
if((_local5.length == 1)){
_local14=undefined;
_local13=undefined;
_local12=undefined;
_local11=undefined;
_local10=undefined;
_local9=undefined;
_local9=-1;
_local10=-1;
_local11=this.GetDuiZi(aCards,false);
_local12=this.GetDuiZi(bCards,false);
_local13=[];
_local14=[];
if(_local11.length){
_local9=this.GetCardValue(_local11[0][0]);
}
if(_local12.length){
_local10=this.GetCardValue(_local12[0][0]);
}
if((_local9 == -1)){
_local15=0;
while((_local15 < aCards.length)){
_local16=undefined;
} else if((_local6.indexOf(aCards[_local15]) != -1)){
}
        
} else {
}
_local16=this.GetCardValue(aCards[_local15]);
_local13.push(_local16);
}
        
_local15=(_local15 + 1);
_local15=0;
}
}
        
_local13.sort(this.sortCardValue);
_local9=_local13[0];
}
if((_local10 == -1)){
_local15=0;
while((_local15 < bCards.length)){
_local16=undefined;
if((_local7.indexOf(bCards[_local15]) != -1)){
}
        
} else {
}
_local16=this.GetCardValue(bCards[_local15]);
_local14.push(_local16);
}
        
_local15=(_local15 + 1);
_local15=0;
}
}
        
_local14.sort(this.sortCardValue);
_local10=_local14[0];
}
if((_local9 > _local10)){
}
        
break;
if((_local9 < _local10)){
1;
}
        
break;
_local16=undefined;
_local15=undefined;
_local15=[];
_local16=[];
_local17=0;
while((_local17 < aCards.length)){
_local18=undefined;
} else if((_local6.indexOf(aCards[_local17]) != -1)){
}
        
_local18=this.GetCardValue(aCards[_local17]);
} else if((_local9 == _local18)){
}
        
} else {
}
_local15.push(_local18);
}
        
_local17=(_local17 + 1);
_local17=0;
}
}
        
_local15.sort(this.sortCardValue);
_local17=0;
while((_local17 < bCards.length)){
_local18=undefined;
if((_local6.indexOf(bCards[_local17]) != -1)){
}
        
_local18=this.GetCardValue(bCards[_local17]);
} else if((_local10 == _local18)){
}
        
} else {
}
_local16.push(_local18);
}
        
_local17=(_local17 + 1);
_local17=0;
}
}
        
_local16.sort(this.sortCardValue);
if((_local15.length == 1)){
_local2=_local15[0];
_local3=_local16[0];
if((_local2 > _local3)){
}
        
}
        
} else if((_local2 < _local3)){
1;
}
        
}
        
} else if(!(_local6.length)&&_local7.length){
}
        
}
        
} else if(_local6.length && !(_local7.length)){
1;
}
        
}
        
} else {
}
2;
}
        
}
        
break;
_local17=0;
while((_local17 < _local15.length)){
_local2=_local15[_local17];
_local3=_local16[_local17];
} else if((_local2 > _local3)){
}
        
}
        
}
        
} else if((_local2 < _local3)){
1;
}
        
}
        
}
        
} else if((_local17 == (_local15.length - 1))){
if(!(_local6.length)&&_local7.length){
}
        
}
        
}
        
} else if(_local6.length && !(_local7.length)){
1;
}
        
}
        
}
        
} else {
}
2;
}
        
}
        
}
        
} else {
}
_local17=(_local17 + 1);
_local17=0;
}
}
        
}
        
}
        
} else {
}
return 0;
} else if((_local5.length == 1)){
return 1;
}
if((this.CheckCardType(bCards) >= 0)){
return 1;
break;
_local2=_local0[(_local0.length - 1)];
_local3=_local1[(_local1.length - 1)];
if((_local2 > _local3)){
return 0;
break;
if((_local2 < _local3)){
return 1;
break;
_local10=undefined;
_local9=undefined;
_local9=(_local0.length - 1);
_local10=(_local1.length - 1);
_local11=5;
while((_local11 > 0)){
} else if((_local9 < 0)||(_local10 < 0)){
2;
}
        
}
        
}
_local2=_local0[_local9];
_local3=_local1[_local10];
if((_local2 > _local3)){
}
        
}
        
} else if((_local2 < _local3)){
1;
}
        
}
        
} else if((0 == _local11)){
2;
}
        
}
        
}
_local9=(_local9 - 1);
_local9=(_local0.length - 1);
_local10=(_local10 - 1);
_local10=(_local1.length - 1);
_local11=(_local11 - 1);
_local11=5;
}
}
        
}
        
---------------------------------------
==========================================================================E
==================================38==================================S
------------------Argv------------------
a,b
---------------------------------------
----------------Content----------------
return (b - a);
---------------------------------------
==========================================================================E
==================================39==================================S
------------------Argv------------------
list,OverFive
---------------------------------------
----------------Content----------------
_local0=false;
_local1=0;
while((_local1 < list.length)){
if((list[_local1] == 14)){
} else if((list[_local1] > 5)){
_local0=true;
}
_local1=(_local1 + 1);
_local1=0;
}
}
        
if(_local0){
_local3=undefined;
_local2=undefined;
_local1=undefined;
_local4=0;
break;
while((_local4 < (list.length - 1))){
_local5=undefined;
_local5=(list[(_local4 + 1)] - list[_local4]);
_local6=1;
while((_local6 < _local5)){
list.push((list[_local4] + _local6));
_local6=(_local6 + 1);
_local6=1;
}
}
        
}
        
_local4=(_local4 + 1);
_local4=0;
}
}
        
list.sort(this.sortFun);
_local1=list[(list.length - 1)];
_local2=1;
_local3=list.length;
_local4=1;
while((_local4 <= (5 - _local3))){
if((_local1 == 14)){
_local1=list[0];
_local2=-1;
}
_local1=(_local1 + _local2);
list.push(_local1);
_local4=(_local4 + 1);
_local4=1;
}
}
        
list.sort(this.sortFun);
}
        
} else {
}
list = [2,3,4,5,14];
return list;
---------------------------------------
==========================================================================E
==================================40==================================S
------------------Argv------------------
pokers,guipai
---------------------------------------
----------------Content----------------
_local0=[];
if(!(pokers)){
assert("");
}
_local1=0;
while((_local1 < pokers.length)){
_local2=undefined;
_local2=pokers[_local1];
if((_local2 >= 65)){
_local0.push(_local2);
}
        
}
_local1=(_local1 + 1);
_local1=0;
}
}
        
return _local0;
---------------------------------------
==========================================================================E
==================================41==================================S
------------------Argv------------------
index,list,lists,rs
---------------------------------------
----------------Content----------------
_local0=[];
if((index == 2)){
_local1=0;
break;
while((_local1 < list.length)){
_local2=_local1;
while((_local2 < list.length)){
_local3=undefined;
_local3={};
_local3.key=list[_local1];
if((_local2 != _local1)){
_local3.value=list[_local2];
_local0.push(_local3);
}
        
}
_local2=(_local2 + 1);
_local2=_local1;
}
}
        
_local1=(_local1 + 1);
_local1=0;
}
}
        
break;
if((index == 3)){
_local1=0;
break;
while((_local1 < list.length)){
_local2=_local1;
while((_local2 < list.length)){
_local3=undefined;
_local3={};
_local3.key=list[_local1];
} else if((_local2 != _local1)&&list[(_local2 + 1)]){
_local3.value=list[_local2];
_local3.x=list[(_local2 + 1)];
_local0.push(_local3);
}
        
}
_local2=(_local2 + 1);
_local2=_local1;
}
}
        
_local1=(_local1 + 1);
_local1=0;
}
}
        
break;
if((index == 4)){
_local1=0;
break;
while((_local1 < list.length)){
_local2=_local1;
while((_local2 < list.length)){
_local3=undefined;
_local3={};
_local3.key=list[_local1];
} else if((_local2 != _local1)&&list[(_local2 + 2)]){
_local3.value=list[_local2];
_local3.x=list[(_local2 + 1)];
_local3.x1=list[(_local2 + 2)];
_local0.push(_local3);
}
        
}
_local2=(_local2 + 1);
_local2=_local1;
}
}
        
_local1=(_local1 + 1);
_local1=0;
}
}
        
break;
if((index == 5)){
_local1=0;
break;
while((_local1 < list.length)){
_local2=_local1;
while((_local2 < list.length)){
_local3=undefined;
_local3={};
_local3.key=list[_local1];
} else if((_local2 != _local1)&&list[(_local2 + 3)]){
_local3.value=list[_local2];
_local3.x=list[(_local2 + 1)];
_local3.x1=list[(_local2 + 2)];
_local3.x2=list[(_local2 + 3)];
_local0.push(_local3);
}
        
}
_local2=(_local2 + 1);
_local2=_local1;
}
}
        
_local1=(_local1 + 1);
_local1=0;
}
}
        
}
_local1=0;
break;
while((_local1 < _local0.length)){
_local3=undefined;
_local2=undefined;
_local2=_local0[_local1];
_local3=[];
_local4=undefined;
while(_local2 has _iternext){
_local4=_iternext;
_local3.push(_local2[_local4]);
}
}
        
lists[lists.length]=_local3;
}
        
_local1=(_local1 + 1);
_local1=0;
}
}
        
---------------------------------------
==========================================================================E
==================================42==================================S
------------------Argv------------------
pokers,isGui,guipai,duizis
---------------------------------------
----------------Content----------------
_local0=this.GetGuiPai(pokers);
_local1=[];
_local2=0;
while((_local2 < pokers.length)){
_local5=undefined;
_local4=undefined;
_local3=undefined;
_local3=pokers[_local2];
if((_local0.indexOf(_local3) != -1)){
}
        
_local4=this.GetSameValue(pokers,_local3);
_local5=this.CheckPokerInList(_local1,_local3);
} else if((_local4.length >= 2) && !(_local5)){
this.PokerCombination(2,_local4,_local1);
}
        
}
_local2=(_local2 + 1);
_local2=0;
}
}
        
if(isGui){
if(_local0.length&&(_local0.length != 1)){
this.PokerCombination(2,_local0,_local1);
}
_local2=0;
break;
while((_local2 < pokers.length)){
_local3=undefined;
_local3=pokers[_local2];
if((_local0.indexOf(_local3) != -1)){
}
        
break;
}
_local4=0;
} else {
while((_local4 < _local0.length)){
_local6=undefined;
_local5=undefined;
_local5=_local0[_local4];
_local6=[];
_local6.push(_local3);
_local6.push(_local5);
this.PokerCombination(2,_local6,_local1);
}
        
_local4=(_local4 + 1);
_local4=0;
}
}
        
}
        
_local2=(_local2 + 1);
_local2=0;
}
}
        
}
return _local1;
---------------------------------------
==========================================================================E
==================================43==================================S
------------------Argv------------------
pokers,guipai,duizis
---------------------------------------
----------------Content----------------
_local0=this.GetGuiPai(pokers);
_local1=[];
_local2=0;
while((_local2 < pokers.length)){
_local5=undefined;
_local4=undefined;
_local3=undefined;
_local3=pokers[_local2];
if((_local0.indexOf(_local3) != -1)){
}
        
_local4=this.GetSameValue(pokers,_local3);
_local5=this.CheckPokerInList(_local1,_local3);
} else if((_local4.length >= 2) && !(_local5)){
_local1[_local1.length]=_local4;
}
if((_local0.length > 1)){
_local1[_local1.length]=_local0;
}
        
}
_local2=(_local2 + 1);
_local2=0;
}
}
        
if((_local1.length >= 2)){
return pokers;
} else {
}
return [];
---------------------------------------
==========================================================================E
==================================44==================================S
------------------Argv------------------
pokers,duizis,liangduis
---------------------------------------
----------------Content----------------
_local0=this.GetDuiZi(pokers);
_local1=[];
if((_local0.length >= 2)){
_local2=0;
break;
while((_local2 < _local0.length)){
_local4=undefined;
_local3=undefined;
_local3=_local0[_local2];
_local4=[];
_local5=1;
while((_local5 < _local0.length)){
_local6=undefined;
_local4=_local0[_local5];
_local6=this.CheckSameValue(_local3,_local4);
if(!(_local6)){
_local7=undefined;
_local7=_local3.concat(_local4);
_local1.push(_local7);
}
        
}
        
}
_local5=(_local5 + 1);
_local5=1;
}
}
        
}
        
_local2=(_local2 + 1);
_local2=0;
}
}
        
}
return _local1;
---------------------------------------
==========================================================================E
==================================45==================================S
------------------Argv------------------
pokers,isGui,guipai,santiaos
---------------------------------------
----------------Content----------------
_local0=this.GetGuiPai(pokers);
_local1=[];
_local2=0;
while((_local2 < pokers.length)){
_local5=undefined;
_local4=undefined;
_local3=undefined;
_local3=pokers[_local2];
if((_local0.indexOf(_local3) != -1)){
}
        
_local4=this.GetSameValue(pokers,_local3);
_local5=this.CheckPokerInList(_local1,_local3);
} else if((_local4.length >= 3) && !(_local5)){
this.PokerCombination(3,_local4,_local1);
}
        
}
_local2=(_local2 + 1);
_local2=0;
}
}
        
if(isGui){
if((_local0.length > 2)){
this.PokerCombination(3,_local0,_local1);
}
if((_local0.length == 1)){
_local2=undefined;
_local2=this.GetDuiZi(pokers,false);
_local3=0;
break;
while((_local3 < _local2.length)){
_local4=undefined;
_local4=_local2[_local3];
_local5=0;
while((_local5 < _local0.length)){
_local6=undefined;
_local6=_local0[_local5];
if((_local4.indexOf(_local6) != -1)){
}
        
} else {
}
_local4.push(_local6);
_local1[_local1.length]=_local4;
}
        
_local5=(_local5 + 1);
_local5=0;
}
}
        
}
        
_local3=(_local3 + 1);
_local3=0;
}
}
        
}
        
break;
if((_local0.length > 1)){
_local2=0;
while((_local2 < pokers.length)){
_local5=undefined;
_local4=undefined;
_local3=undefined;
_local3=pokers[_local2];
} else if((_local0.indexOf(_local3) != -1)){
}
        
} else {
}
_local4=[];
_local4.push(_local3);
_local5=_local4.concat(_local0);
_local1[_local1.length]=_local5;
}
        
_local2=(_local2 + 1);
_local2=0;
}
}
        
}}
return _local1;
---------------------------------------
==========================================================================E
==================================46==================================S
------------------Argv------------------
pokers,needCheckHuLu,guipai,santiaos
---------------------------------------
----------------Content----------------
_local0=this.GetGuiPai(pokers);
_local1=[];
if(!(_local0.length)){
_local2=0;
while((_local2 < pokers.length)){
_local4=undefined;
_local3=undefined;
_local3=pokers[_local2];
_local4=this.GetSameValue(pokers,_local3);
if((3 == _local4.length)){
_local1=_local4;
}
        
break;
}
        
_local2=(_local2 + 1);
_local2=0;
}
}
        
break;
if((_local0.length == 1)){
_local2=0;
while((_local2 < pokers.length)){
_local4=undefined;
_local3=undefined;
_local3=pokers[_local2];
} else if((_local0.indexOf(_local3) != -1)){
}
        
_local4=this.GetSameValue(pokers,_local3);
} else if((2 == _local4.length)){
_local4.push(_local0[0]);
_local1=_local4;
}
        
break;
}
        
}
_local2=(_local2 + 1);
_local2=0;
}
}
        
break;
}
if((_local0.length == 2)){
_local2=0;
break;
while((_local2 < pokers.length)){
_local3=undefined;
_local3=pokers[_local2];
if((_local0.indexOf(_local3) != -1)){
}
        
break;
}
_local1.push(_local3);
_local4=0;
} else {
while((_local4 < _local0.length)){
_local1.push(_local0[_local4]);
_local4=(_local4 + 1);
_local4=0;
}
}
        
}
        
break;
}
        
_local2=(_local2 + 1);
_local2=0;
}
}
        
break;
}
if((_local0.length >= 3)){
_local2=0;
} else {
while((_local2 < 3)){
_local1.push(_local0[_local2]);
_local2=(_local2 + 1);
_local2=0;
}
}
        
}
if((3 == _local1.length)){
_local2=undefined;
if(!(needCheckHuLu)){
_local1=_local4;
}
        
}
_local2=[];
_local3=0;
while((_local3 < pokers.length)){
if((_local1.indexOf(pokers[_local3]) != -1)){
} else {
}
_local2.push(pokers[_local3]);
_local3=(_local3 + 1);
_local3=0;
}
}
        
if((_local2.length == 2)){
_local3=undefined;
_local3=this.GetSameValue(_local2,_local2[0]);
if((_local3.length == 2)){
[];
}
        
}
        
} else {
}
_local1=_local4;
}
        
}
        
}
        
} else {
}
_local1=_local4;
}
        
}
        
} else {
}
return [];
---------------------------------------
==========================================================================E
==================================47==================================S
------------------Argv------------------
shunzi,poker,guipai,firstValue,lastValue,value
---------------------------------------
----------------Content----------------
_local0=this.GetGuiPai(poker);
if(_local0.length){
return false;
}
if(!(this.isContain(shunzi,poker))){
return false;
}
if((shunzi.length == 1)){
return true;
}
if((this.GetCardValue(shunzi[0]) == 14)&&(this.GetCardValue(shunzi[1]) <= 5)){
return true;
}
_local1=this.GetCardValue(shunzi[0]);
_local2=this.GetCardValue(poker);
_local3=(_local1 - _local2);
if((_local3 > 4)){
return false;
}
return true;
---------------------------------------
==========================================================================E
==================================48==================================S
------------------Argv------------------
shunzi,poker,temp
---------------------------------------
----------------Content----------------
_local0=this.GetCardValue(poker);
_local1=0;
while((_local1 < shunzi.length)){
_local2=undefined;
_local2=this.GetCardValue(shunzi[_local1]);
if((_local0 == _local2)){
false;
}
        
}
        
}
        
}
_local1=(_local1 + 1);
_local1=0;
}
}
        
return true;
---------------------------------------
==========================================================================E
==================================49==================================S
------------------Argv------------------
pokers,needCheckAll,newpokers,self,guipai,shunzis,retShun
---------------------------------------
----------------Content----------------
_local0=pokers.slice();
if((_local0.length < 5)){
return [];
}
_aliased7646=this;
_local0.sort(function () { __FUNC_50__ });
_local2=this.GetGuiPai(_local0);
_local3=[];
_local5=0;
break;
while((_local5 < _local0.length)){
_local7=undefined;
_local6=undefined;
_local6=_local0[_local5];
if((_local2.indexOf(_local6) != -1)){
}
        
break;
}
_local7=[];
_local7.push(_local6);
if((_local2.length >= 4)){
_local3.push(_local7);
}
_local8=(_local5 + 1);
break;
while((_local8 < _local0.length)){
_local9=undefined;
if((_local2.indexOf(_local0[_local8]) != -1)){
}
        
break;
}
_local7=[];
_local7.push(_local0[_local5]);
_local9=this.isSave(_local7,_local0[_local8]);
if(_local9){
_local7.push(_local0[_local8]);
if((_local2.length >= 3)){
_local12=undefined;
_local11=undefined;
_local10=undefined;
_local10=this.GetCardValue(_local0[_local5]);
_local11=this.GetCardValue(_local0[_local8]);
_local12=(_local10 - _local11);
if((_local12 <= 4)){
_local3.push(_local7);
}
        
}
} else {
}
}
        
}
break;
_local10=(_local8 + 1);
break;
while((_local10 < _local0.length)){
_local11=undefined;
if((_local2.indexOf(_local0[_local10]) != -1)){
}
        
break;
}
_local7=[];
_local7.push(_local0[_local5]);
_local7.push(_local0[_local8]);
_local11=this.isSave(_local7,_local0[_local10]);
if(_local11){
_local7.push(_local0[_local10]);
_local3.push(_local7);
} else {
}
        
}
break;
_local12=(_local10 + 1);
break;
while((_local12 < _local0.length)){
_local13=undefined;
if((_local2.indexOf(_local0[_local12]) != -1)){
}
        
break;
}
_local7=[];
_local7.push(_local0[_local5]);
_local7.push(_local0[_local8]);
_local7.push(_local0[_local10]);
_local13=this.isSave(_local7,_local0[_local12]);
if(_local13){
_local7.push(_local0[_local12]);
_local3.push(_local7);
} else {
}
        
}
break;
_local14=(_local12 + 1);
while((_local14 < _local0.length)){
_local15=undefined;
if((_local2.indexOf(_local0[_local14]) != -1)){
}
        
_local7=[];
_local7.push(_local0[_local5]);
_local7.push(_local0[_local8]);
_local7.push(_local0[_local10]);
_local7.push(_local0[_local12]);
_local15=this.isSave(_local7,_local0[_local14]);
} else if(_local15){
_local7.push(_local0[_local14]);
_local3.push(_local7);
}
        
}
_local14=(_local14 + 1);
_local14=(_local12 + 1);
}
}
        
}
        
_local12=(_local12 + 1);
_local12=(_local10 + 1);
}
}
        
}
        
_local10=(_local10 + 1);
_local10=(_local8 + 1);
}
}
        
}
        
_local8=(_local8 + 1);
_local8=(_local5 + 1);
}
}
        
}
        
_local5=(_local5 + 1);
_local5=0;
}
}
        
_local4=[];
if(_local2.length&&(_local2.length == 1)){
_local5=0;
while((_local5 < _local3.length)){
_local6=undefined;
_local6=_local3[_local5];
if((_local6.length == 4)){
_local6.push(_local2[0]);
_local4.push(_local6);
} else if((_local6.length == 5)){
_local4.push(_local6);
}
        
}
_local5=(_local5 + 1);
_local5=0;
}
}
        
break;
if(_local2.length&&(_local2.length == 2)){
_local5=0;
while((_local5 < _local3.length)){
_local6=undefined;
_local6=_local3[_local5];
} else if((_local6.length == 3)){
_local6.push(_local2[0]);
_local6.push(_local2[1]);
_local4.push(_local6);
} else if((_local6.length == 4)){
_local6.push(_local2[0]);
_local4.push(_local6);
} else {
}
_local4.push(_local6);
}
        
_local5=(_local5 + 1);
_local5=0;
}
}
        
break;
if(_local2.length&&(_local2.length == 3)){
_local5=0;
while((_local5 < _local3.length)){
_local6=undefined;
_local6=_local3[_local5];
} else if((_local6.length == 2)){
_local6.push(_local2[0]);
_local6.push(_local2[1]);
_local6.push(_local2[2]);
_local4.push(_local6);
} else if((_local6.length == 3)){
_local6.push(_local2[0]);
_local6.push(_local2[1]);
_local4.push(_local6);
} else if((_local6.length == 4)){
_local6.push(_local2[0]);
_local4.push(_local6);
} else {
}
_local4.push(_local6);
}
        
_local5=(_local5 + 1);
_local5=0;
}
}
        
break;
if(_local2.length&&(_local2.length == 4)){
_local5=0;
while((_local5 < _local3.length)){
_local6=undefined;
_local6=_local3[_local5];
} else if((_local6.length == 1)){
_local6.push(_local2[0]);
_local6.push(_local2[1]);
_local6.push(_local2[2]);
_local6.push(_local2[3]);
_local4.push(_local6);
} else if((_local6.length == 2)){
_local6.push(_local2[0]);
_local6.push(_local2[1]);
_local6.push(_local2[2]);
_local4.push(_local6);
} else if((_local6.length == 3)){
_local6.push(_local2[0]);
_local6.push(_local2[1]);
_local4.push(_local6);
} else if((_local6.length == 4)){
_local6.push(_local2[0]);
_local4.push(_local6);
} else {
}
_local4.push(_local6);
}
        
_local5=(_local5 + 1);
_local5=0;
}
}
        
break;
_local5=0;
while((_local5 < _local3.length)){
_local6=undefined;
_local6=_local3[_local5];
} else if((_local6.length == 5)){
_local4.push(_local6);
}
        
}
_local5=(_local5 + 1);
_local5=0;
}
}
        
return _local4;
---------------------------------------
==========================================================================E
==================================50==================================S
------------------Argv------------------
a,b
---------------------------------------
----------------Content----------------
return (_aliased7646.GetCardValue(b) - _aliased7646.GetCardValue(a));
---------------------------------------
==========================================================================E
==================================51==================================S
------------------Argv------------------
a,b
---------------------------------------
----------------Content----------------
if((a.cardValue == b.cardValue)){
return (a.cardX16 - b.cardX16);
} else {
}
return (a.cardValue - b.cardValue);
---------------------------------------
==========================================================================E
==================================52==================================S
------------------Argv------------------
pokers,guipai,shunzis,needCheckList,hasAce,changeAce,value
---------------------------------------
----------------Content----------------
if((pokers.length != 5)){
return [];
}
_local0=this.GetGuiPai(pokers);
_local1=[];
_local2=[];
_local3=false;
_local4=false;
_local6=0;
while((_local6 < pokers.length)){
_local8=undefined;
_local7=undefined;
_local7=this.GetSameValueEX(pokers,pokers[_local6]);
if((_local7.length >= 2)){
[];
}
        
}
        
}
if((_local0.indexOf(pokers[_local6]) != -1)){
}
        
_local8={};
_local8.cardValue=this.GetCardValue(pokers[_local6]);
} else if((_local8.cardValue == 14)){
_local3=true;
}
_local8.cardX16=pokers[_local6];
_local2.push(_local8);
}
        
_local6=(_local6 + 1);
_local6=0;
}
}
        
if(_local3){
_local6=0;
while((_local6 < _local2.length)){
_local7=undefined;
_local7=_local2[_local6].cardValue;
if((_local7 <= 5)){
_local4=true;
}
        
break;
}
        
_local6=(_local6 + 1);
_local6=0;
}
}
        
}
_local2.sort(this.sortFunEx);
if(_local4){
_local6=0;
while((_local6 < _local2.length)){
_local7=undefined;
_local7=_local2[_local6].cardValue;
} else if((_local7 == 14)){
_local2[_local6].cardValue=1;
}
        
break;
}
        
}
_local6=(_local6 + 1);
_local6=0;
}
}
        
_local2.sort(this.sortFunEx);
}
_local5=(_local2[(_local2.length - 1)].cardValue - _local2[0].cardValue);
if((_local5 <= 4)){
return pokers;
} else {
}
return [];
---------------------------------------
==========================================================================E
==================================53==================================S
------------------Argv------------------
tonghuas,tonghua,bRet
---------------------------------------
----------------Content----------------
_local0=false;
if(tonghuas.length){
_local1=0;
while((_local1 < tonghuas.length)){
_local3=undefined;
_local2=undefined;
_local2=this.GetCardColor(tonghuas[_local1][0]);
_local3=this.GetCardColor(tonghua[0]);
if((_local2 == _local3)){
_local0=true;
}
        
break;
}
        
}
_local1=(_local1 + 1);
_local1=0;
}
}
        
}
return _local0;
---------------------------------------
==========================================================================E
==================================54==================================S
------------------Argv------------------
tonghuas,bRet
---------------------------------------
----------------Content----------------
_local0=false;
if(tonghuas.length){
_local1=undefined;
_local1=this.GetCardColor(tonghuas[0]);
_local2=1;
while((_local2 < tonghuas.length)){
_local3=undefined;
_local3=this.GetCardColor(tonghuas[_local2]);
if((_local1 != _local3)){
false;
}
        
}
        
}
        
}
        
}
_local2=(_local2 + 1);
_local2=1;
}
}
        
}
        
}
return true;
---------------------------------------
==========================================================================E
==================================55==================================S
------------------Argv------------------
pokers,newpokers,self,guipai,tonghuas,paizu,tonghua
---------------------------------------
----------------Content----------------
_local0=pokers.slice();
if((_local0.length < 5)){
return [];
}
_aliased9447=this;
_local0.sort(function () { __FUNC_56__ });
_local2=this.GetGuiPai(_local0);
_local3=[];
_local6=0;
while((_local6 < _local0.length)){
_local9=undefined;
_local8=undefined;
_local7=undefined;
_local7=_local0[_local6];
if((_local2.indexOf(_local7) != -1)){
}
        
_local8=this.GetSameColor(_local0,_local7);
_local9=this.isSameColor(_local3,_local8);
} else if((_local8.length == 5) && !(_local9)){
_local3[_local3.length]=_local8;
} else if((_local8.length > 5) && !(_local9)){
this.PokerCombination(5,_local8,_local3);
}
        
}
_local6=(_local6 + 1);
_local6=0;
}
}
        
_local4=[];
if((_local2.length == 1)){
_local6=0;
break;
while((_local6 < _local0.length)){
_local9=undefined;
_local8=undefined;
_local7=undefined;
_local7=_local0[_local6];
if((_local2.indexOf(_local7) != -1)){
}
        
break;
_local8=this.GetSameColor(_local0,_local7);
_local9=this.isSameColor(_local4,_local8);
if((_local8.length >= 4) && !(_local9)){
this.PokerCombination(4,_local8,_local4);
_local10=0;
while((_local10 < _local4.length)){
} else if((_local4[_local10].length < 5)){
_local11=undefined;
_local11=_local4[_local10];
_local11.push(_local2[0]);
}
        
}
_local10=(_local10 + 1);
_local10=0;
}
}
        
}
        
}
_local6=(_local6 + 1);
_local6=0;
}
}
        
break;
if((_local2.length == 2)){
_local6=0;
break;
while((_local6 < _local0.length)){
_local9=undefined;
_local8=undefined;
_local7=undefined;
_local7=_local0[_local6];
if((_local2.indexOf(_local7) != -1)){
}
        
break;
_local8=this.GetSameColor(_local0,_local7);
_local9=this.isSameColor(_local4,_local8);
if((_local8.length >= 3) && !(_local9)){
this.PokerCombination(3,_local8,_local4);
_local10=0;
break;
while((_local10 < _local4.length)){
_local11=undefined;
_local11=_local4[_local10];
_local12=0;
while((_local12 < _local2.length)){
} else if((_local4[_local10].length < 5)){
_local11.push(_local2[_local12]);
}
_local12=(_local12 + 1);
_local12=0;
}
}
        
}
        
_local10=(_local10 + 1);
_local10=0;
}
}
        
}
        
}
_local6=(_local6 + 1);
_local6=0;
}
}
        
break;
if((_local2.length == 3)){
_local6=0;
break;
while((_local6 < _local0.length)){
_local9=undefined;
_local8=undefined;
_local7=undefined;
_local7=_local0[_local6];
if((_local2.indexOf(_local7) != -1)){
}
        
break;
_local8=this.GetSameColor(_local0,_local7);
_local9=this.isSameColor(_local4,_local8);
if((_local8.length >= 2) && !(_local9)){
this.PokerCombination(2,_local8,_local4);
_local10=0;
break;
while((_local10 < _local4.length)){
_local11=undefined;
_local11=_local4[_local10];
_local12=0;
while((_local12 < _local2.length)){
} else if((_local4[_local10].length < 5)){
_local11.push(_local2[_local12]);
}
_local12=(_local12 + 1);
_local12=0;
}
}
        
}
        
_local10=(_local10 + 1);
_local10=0;
}
}
        
}
        
}
_local6=(_local6 + 1);
_local6=0;
}
}
        
break;
if((_local2.length == 4)){
_local6=0;
while((_local6 < pokers.length)){
} else if((_local2.indexOf(pokers[_local6]) != -1)){
} else {
}
_local5=[pokers[0],pokers[1],pokers[2],pokers[3]];
_local5.push(pokers[_local6]);
_local4[_local4.length]=_local5;
_local6=(_local6 + 1);
_local6=0;
}
}
        
}
if(_local4.length){
_local6=0;
while((_local6 < _local4.length)){
_local3.push(_local4[_local6]);
_local6=(_local6 + 1);
_local6=0;
}
}
        
}
return _local3;
---------------------------------------
==========================================================================E
==================================56==================================S
------------------Argv------------------
a,b
---------------------------------------
----------------Content----------------
return (_aliased9447.GetCardValue(b) - _aliased9447.GetCardValue(a));
---------------------------------------
==========================================================================E
==================================57==================================S
------------------Argv------------------
pokers,isSameColor
---------------------------------------
----------------Content----------------
if((pokers.length != 5)){
return [];
}
_local0=this.CheckSameColor(pokers);
if((pokers.length == 5)&&_local0){
return pokers;
} else {
}
return [];
---------------------------------------
==========================================================================E
==================================58==================================S
------------------Argv------------------
pokers,guipai,isSameColor
---------------------------------------
----------------Content----------------
if((pokers.length != 5)){
return [];
}
_local0=this.GetGuiPai(pokers);
_local1=this.CheckSameColor(pokers);
if((pokers.length == 5)&&_local1){
_local2=undefined;
_local2=false;
_local3=0;
while((_local3 < pokers.length)){
_local5=undefined;
_local4=undefined;
_local4=pokers[_local3];
if((_local0.indexOf(_local4) != -1)){
}
        
_local5=this.GetSameValue(pokers,_local4);
} else if((_local5.length == 2)){
_local2=true;
}
        
break;
}
        
}
_local3=(_local3 + 1);
_local3=0;
}
}
        
if((_local0.length == 1)){
_local2=true;
}
if(_local2){
}
        
} else {
}
[];
}
        
}
        
} else {
}
return [];
---------------------------------------
==========================================================================E
==================================59==================================S
------------------Argv------------------
pokers,guipai,isSameColor,duizi
---------------------------------------
----------------Content----------------
if((pokers.length != 5)){
return [];
}
_local0=this.GetGuiPai(pokers);
_local1=this.CheckSameColor(pokers);
_local2=[];
if((pokers.length == 5)&&_local1){
if(!(_local0.length)){
_local3=undefined;
_local3=this.GetDuiZi(pokers);
if((_local3.length == 2)){
}
        
} else {
}
[];
}
        
}
        
break;
if((_local0.length == 1)){
_local3=0;
while((_local3 < pokers.length)){
_local4=undefined;
_local4=pokers[_local3];
} else if((_local0.indexOf(_local4) != -1)){
}
        
_local2=this.GetSameValue(pokers,_local4);
} else if((_local2.length == 2)){
}
        
break;
}
        
}
_local3=(_local3 + 1);
_local3=0;
}
}
        
if((_local2.length == 2)){
return pokers;
} else {
}
return [];
} else if((_local0.length > 1)){
return pokers;
} else {
}
}
return [];
---------------------------------------
==========================================================================E
==================================60==================================S
------------------Argv------------------
pokers,guipai,santiaos,remianList
---------------------------------------
----------------Content----------------
_local0=this.GetGuiPai(pokers);
_local1=[];
if(!(_local0.length)){
_local3=0;
while((_local3 < pokers.length)){
_local5=undefined;
_local4=undefined;
_local4=pokers[_local3];
_local5=this.GetSameValue(pokers,_local4);
if((3 == _local5.length)){
_local1=_local5;
}
        
break;
}
        
_local3=(_local3 + 1);
_local3=0;
}
}
        
break;
if((_local0.length == 1)){
_local3=0;
while((_local3 < pokers.length)){
_local5=undefined;
_local4=undefined;
_local4=pokers[_local3];
} else if((_local0.indexOf(_local4) != -1)){
}
        
_local5=this.GetSameValue(pokers,_local4);
} else if((2 == _local5.length)){
_local5.push(_local0[0]);
_local1=_local5;
}
        
break;
}
        
}
_local3=(_local3 + 1);
_local3=0;
}
}
        
break;
}
if((_local0.length > 1)){
_local3=0;
break;
while((_local3 < pokers.length)){
_local4=undefined;
_local4=pokers[_local3];
if((_local0.indexOf(_local4) != -1)){
}
        
break;
}
_local1.push(_local4);
_local5=0;
} else {
while((_local5 < _local0.length)){
_local1.push(_local0[_local5]);
_local5=(_local5 + 1);
_local5=0;
}
}
        
}
        
break;
}
        
_local3=(_local3 + 1);
_local3=0;
}
}
        
}
if((_local1.length == 0)){
return [];
}
_local2=[];
_local3=0;
while((_local3 < pokers.length)){
if((_local1.indexOf(pokers[_local3]) != -1)){
} else {
}
_local2.push(pokers[_local3]);
_local3=(_local3 + 1);
_local3=0;
}
}
        
if((_local2.length == 2)){
_local3=undefined;
_local3=this.GetSameValue(_local2,_local2[0]);
if((_local3.length == 2)){
}
        
} else {
}
[];
}
        
}
        
} else {
}
return [];
---------------------------------------
==========================================================================E
==================================61==================================S
------------------Argv------------------
pokers,duizis,santiaos,hulus
---------------------------------------
----------------Content----------------
_local0=this.GetDuiZi(pokers);
this.SortCardArrByMin(_local0);
_local1=this.GetSanTiao(pokers);
this.SortCardArrByMax(_local1);
_local2=[];
_local3=0;
break;
while((_local3 < _local0.length)){
_local4=undefined;
_local4=_local0[_local3];
_local5=0;
while((_local5 < _local1.length)){
_local6=undefined;
_local6=_local1[_local5];
if(!(this.CheckSameValue(_local4,_local6))){
_local7=undefined;
_local7=_local4.concat(_local6);
_local2[_local2.length]=_local7;
}
        
}
        
}
_local5=(_local5 + 1);
_local5=0;
}
}
        
}
        
_local3=(_local3 + 1);
_local3=0;
}
}
        
return _local2;
---------------------------------------
==========================================================================E
==================================62==================================S
------------------Argv------------------
pokers,isGui,guipai,zhadans
---------------------------------------
----------------Content----------------
_local0=this.GetGuiPai(pokers);
_local1=[];
_local2=0;
while((_local2 < pokers.length)){
_local5=undefined;
_local4=undefined;
_local3=undefined;
_local3=pokers[_local2];
if((_local0.indexOf(_local3) != -1)){
}
        
_local4=this.GetSameValue(pokers,_local3);
_local5=this.CheckPokerInList(_local1,_local3);
} else if((_local4.length > 3) && !(_local5)){
this.PokerCombination(4,_local4,_local1);
}
        
}
_local2=(_local2 + 1);
_local2=0;
}
}
        
if(isGui){
if((_local0.length >= 4)){
this.PokerCombination(4,_local0,_local1);
}
if((_local0.length == 1)){
_local2=undefined;
_local2=this.GetSanTiao(pokers,false);
_local3=0;
while((_local3 < _local2.length)){
_local4=undefined;
_local4=_local2[_local3];
if(this.isHaveGui(_local0,_local4)){
}
        
} else {
}
_local4.push(_local0[0]);
_local1[_local1.length]=_local4;
}
        
_local3=(_local3 + 1);
_local3=0;
}
}
        
}
        
break;
}
if((_local0.length > 1)){
_local2=undefined;
_local2=this.GetDuiZi(pokers,false);
_local3=0;
break;
while((_local3 < _local2.length)){
_local4=undefined;
_local4=_local2[_local3];
if(this.isHaveGui(_local0,_local4)){
}
        
break;
}
_local5=0;
} else {
while((_local5 < _local0.length)){
_local4.push(_local0[_local5]);
_local5=(_local5 + 1);
_local5=0;
}
}
        
_local1[_local1.length]=_local4;
}
        
_local3=(_local3 + 1);
_local3=0;
}
}
        
}
        
}}
return _local1;
---------------------------------------
==========================================================================E
==================================63==================================S
------------------Argv------------------
pokers,tonghuas
---------------------------------------
----------------Content----------------
_local0=this.GetTonghuaEx(pokers);
if((5 == pokers.length)&&(5 == _local0.length)){
if((this.GetShunziEx(_local0).length != 0)){
return _local0;
} else {
}
return [];
} else {
}
return [];
---------------------------------------
==========================================================================E
==================================64==================================S
------------------Argv------------------
pokers,tonghuashuns,shunzis
---------------------------------------
----------------Content----------------
_local0=[];
_local1=this.GetShunzi(pokers,true);
_local2=0;
while((_local2 < _local1.length)){
_local1[_local2].sort(function () { __FUNC_65__ });
_local2=(_local2 + 1);
_local2=0;
}
}
        
_local2=0;
while((_local2 < _local1.length)){
_local4=undefined;
_local3=undefined;
_local3=_local1[_local2];
_local4=this.CheckSameColor(_local3);
if(_local4){
_local0[_local0.length]=_local3;
}
        
}
_local2=(_local2 + 1);
_local2=0;
}
}
        
return _local0;
---------------------------------------
==========================================================================E
==================================65==================================S
------------------Argv------------------
a,b
---------------------------------------
----------------Content----------------
return (b - a);
---------------------------------------
==========================================================================E
==================================66==================================S
------------------Argv------------------
pokers,guipai,newPorks,wutongs,wutong
---------------------------------------
----------------Content----------------
_local0=this.GetGuiPai(pokers);
_local1=[];
_local4=0;
while((_local4 < pokers.length)){
if((_local0.indexOf(pokers[_local4]) == -1)){
_local1.push(pokers[_local4]);
}
_local4=(_local4 + 1);
_local4=0;
}
}
        
_local2=[];
_local4=0;
while((_local4 < pokers.length)){
_local7=undefined;
_local6=undefined;
_local5=undefined;
_local5=pokers[_local4];
if((_local0.indexOf(_local5) != -1)){
}
        
_local6=this.GetSameValue(pokers,_local5);
_local7=this.CheckPokerInList(_local2,_local5);
} else if((_local6.length == 5) && !(_local7)){
this.PokerCombination(5,_local6,_local2);
} else if((_local6.length > 5) && !(_local7)){
this.PokerCombination(5,_local6,_local2);
}
        
}
_local4=(_local4 + 1);
_local4=0;
}
}
        
if((_local0.length >= 5)){
this.PokerCombination(5,_local0,_local2);
}
if((_local0.length == 1)){
_local4=undefined;
_local4=this.GetZhaDang(pokers,false);
_local5=0;
while((_local5 < _local4.length)){
_local6=undefined;
_local6=_local4[_local5];
if(this.isHaveGui(_local0,_local6)){
}
        
} else {
}
_local6.push(_local0[0]);
_local2[_local2.length]=_local6;
}
        
_local5=(_local5 + 1);
_local5=0;
}
}
        
}
        
break;
}
if((_local0.length == 2)){
_local4=undefined;
_local4=this.GetSanTiao(pokers,false);
_local5=0;
break;
while((_local5 < _local4.length)){
_local6=undefined;
_local6=_local4[_local5];
if(this.isHaveGui(_local0,_local6)){
}
        
break;
}
_local7=0;
} else {
while((_local7 < _local0.length)){
_local6.push(_local0[_local7]);
_local7=(_local7 + 1);
_local7=0;
}
}
        
_local2[_local2.length]=_local6;
}
        
_local5=(_local5 + 1);
_local5=0;
}
}
        
}
        
break;
}
if((_local0.length == 3)){
_local4=undefined;
_local4=this.GetDuiZi(pokers,false);
_local5=0;
break;
while((_local5 < _local4.length)){
_local6=undefined;
_local6=_local4[_local5];
if(this.isHaveGui(_local0,_local6)){
}
        
break;
}
_local7=0;
} else {
while((_local7 < _local0.length)){
_local6.push(_local0[_local7]);
_local7=(_local7 + 1);
_local7=0;
}
}
        
_local2[_local2.length]=_local6;
}
        
_local5=(_local5 + 1);
_local5=0;
}
}
        
}
        
break;
if((_local0.length == 4)){
_local4=0;
while((_local4 < pokers.length)){
} else if((_local0.indexOf(pokers[_local4]) != -1)){
} else {
}
_local3=[pokers[0],pokers[1],pokers[2],pokers[3]];
_local3.push(pokers[_local4]);
_local2[_local2.length]=_local3;
_local4=(_local4 + 1);
_local4=0;
}
}
        
}
return _local2;
---------------------------------------
==========================================================================E
==================================67==================================S
------------------Argv------------------
pokers,self
---------------------------------------
----------------Content----------------
if((pokers.length == 0)){
return undefined;
}
_aliased9813=this;
pokers.sort(function () { __FUNC_68__ });
---------------------------------------
==========================================================================E
==================================68==================================S
------------------Argv------------------
a,b
---------------------------------------
----------------Content----------------
return (_aliased9813.GetCardValue(b) - _aliased9813.GetCardValue(a));
---------------------------------------
==========================================================================E
==================================69==================================S
------------------Argv------------------
pokers,self
---------------------------------------
----------------Content----------------
if((pokers.length == 0)){
return undefined;
}
_aliased2146=this;
pokers.sort(function () { __FUNC_70__ });
---------------------------------------
==========================================================================E
==================================70==================================S
------------------Argv------------------
a,b
---------------------------------------
----------------Content----------------
return (_aliased2146.GetCardValue(a) - _aliased2146.GetCardValue(b));
---------------------------------------
==========================================================================E
==================================71==================================S
------------------Argv------------------
pokers,sortType,array,self
---------------------------------------
----------------Content----------------
if(!(pokers.length)){
return undefined;
}
_local0=[];
_local1=this;
_local2=0;
while((_local2 < pokers.length)){
_local4=undefined;
_local3=undefined;
_local3=pokers[_local2];
_local4={};
_local4.cardValue=this.GetCardValue(_local3);
_local4.cardColor=this.GetCardColor(_local3);
_local4.cardX16=_local3;
_local0.push(_local4);
}
        
_local2=(_local2 + 1);
_local2=0;
}
}
        
_local0.sort(function () { __FUNC_72__ });
return _local0;
---------------------------------------
==========================================================================E
==================================72==================================S
------------------Argv------------------
a,b
---------------------------------------
----------------Content----------------
if((a.cardColor == b.cardColor)){
return (b.cardValue - a.cardValue);
} else if((_aliased2146 == 2)){
return (b.cardColor - a.cardColor);
}
if((_aliased2146 == 1)){
return (b.cardValue - a.cardValue);
}
---------------------------------------
==========================================================================E
==================================73==================================S
------------------Argv------------------
guipai,list,isHave
---------------------------------------
----------------Content----------------
_local0=false;
_local1=0;
while((_local1 < list.length)){
if((guipai.indexOf(list[_local1]) != -1)){
_local0=true;
break;
}
_local1=(_local1 + 1);
_local1=0;
}
}
        
return _local0;
---------------------------------------
==========================================================================E
==================================74==================================S
------------------Argv------------------
list1,list2
---------------------------------------
----------------Content----------------
_local0=0;
while((_local0 < list2.length)){
if((list1.indexOf(list2[_local0]) != -1)){
true;
}
        
}
_local0=(_local0 + 1);
_local0=0;
}
}
        
return false;
---------------------------------------
==========================================================================E
==================================75==================================S
------------------Argv------------------
list,guipai,bSameColor,startColor
---------------------------------------
----------------Content----------------
if((list.length == 0)){
return false;
}
_local0=this.GetGuiPai(list);
_local1=true;
_local2=-1;
_local3=0;
while((_local3 < list.length)){
if((_local0.indexOf(list[_local3]) != -1)){
} else {
}
_local2=this.GetCardColor(list[_local3]);
break;
_local3=(_local3 + 1);
_local3=0;
}
}
        
_local3=0;
while((_local3 < list.length)){
_local4=undefined;
if((_local0.indexOf(list[_local3]) != -1)){
}
        
_local4=this.GetCardColor(list[_local3]);
} else if((_local4 != _local2)){
_local1=false;
}
        
break;
}
        
}
_local3=(_local3 + 1);
_local3=0;
}
}
        
return _local1;
---------------------------------------
==========================================================================E
==================================76==================================S
------------------Argv------------------
list,guipai,nGuiLen,newList,self,nOffet
---------------------------------------
----------------Content----------------
if((list.length == 0)){
return false;
}
_local0=this.GetGuiPai(list);
_local1=_local0.length;
_local2=[];
_local5=0;
while((_local5 < list.length)){
if((_local0.indexOf(list[_local5]) != -1)){
} else {
}
_local2.push(list[_local5]);
_local5=(_local5 + 1);
_local5=0;
}
}
        
_aliased8234=this;
_local2.sort(function () { __FUNC_77__ });
_local5=0;
while((_local5 < (_local2.length - 1))){
_local7=undefined;
_local6=undefined;
_local6=_aliased8234.GetCardValue(_local2[_local5]);
_local7=_aliased8234.GetCardValue(_local2[(_local5 + 1)]);
_local4=(_local6 - _local7);
if((_local4 == 0)){
false;
}
        
}
        
}
if((_local4 == 1)){
}
        
} else if(((_local4 - 1) > _local1)){
false;
}
        
}
        
}
_local1=(_local1 - (_local4 - 1));
}
        
_local5=(_local5 + 1);
_local5=0;
}
}
        
return true;
---------------------------------------
==========================================================================E
==================================77==================================S
------------------Argv------------------
a,b
---------------------------------------
----------------Content----------------
return (_aliased8234.GetCardValue(b) - _aliased8234.GetCardValue(a));
---------------------------------------
==========================================================================E
==================================78==================================S
------------------Argv------------------
listArr,list
---------------------------------------
----------------Content----------------
if((listArr.length == 0)){
return false;
}
list.sort(function () { __FUNC_79__ });
_local0=0;
break;
while((_local0 < listArr.length)){
_local2=undefined;
_local1=undefined;
_local1=listArr[_local0];
if((_local1.length !== list.length)){
}
        
break;
_local1.sort(function () { __FUNC_80__ });
_local2=true;
_local3=0;
while((_local3 < _local1.length)){
} else if((_local1[_local3] != list[_local3])){
_local2=false;
break;
}
_local3=(_local3 + 1);
_local3=0;
}
}
        
if((_local2 == true)){
true;
}
        
}
        
}
        
}
_local0=(_local0 + 1);
_local0=0;
}
}
        
return false;
---------------------------------------
==========================================================================E
==================================79==================================S
------------------Argv------------------
a,b
---------------------------------------
----------------Content----------------
return (a - b);
---------------------------------------
==========================================================================E
==================================80==================================S
------------------Argv------------------
a,b
---------------------------------------
----------------Content----------------
return (a - b);
---------------------------------------
==========================================================================E
==================================81==================================S
------------------Argv------------------
list,tagCard,bInList
---------------------------------------
----------------Content----------------
if((list.length == 0)){
return false;
}
_local0=false;
_local1=0;
while((_local1 < list.length)){
_local3=undefined;
_local2=undefined;
_local2=list[_local1];
_local3=_local2.indexOf(tagCard);
if((_local3 >= 0)){
_local0=true;
}
        
}
_local1=(_local1 + 1);
_local1=0;
}
}
        
return _local0;
---------------------------------------
==========================================================================E
==================================82==================================S
------------------Argv------------------
list,tagCard
---------------------------------------
----------------Content----------------
if((list.length == 0)){
return false;
}
_local0=0;
while((_local0 < list.length)){
_local1=undefined;
_local1=list[_local0];
if((_local1.indexOf(tagCard) >= 0)){
true;
}
        
}
        
}
        
}
_local0=(_local0 + 1);
_local0=0;
}
}
        
return false;
---------------------------------------
==========================================================================E
==================================83==================================S
------------------Argv------------------
pokers,tagCard,continueValueList,tagCardValue,continueTime
---------------------------------------
----------------Content----------------
if((pokers.length == 0)){
return undefined;
}
_local0=[];
_local1=this.GetCardValue(tagCard);
_local2=1;
_local3=0;
while((_local3 < pokers.length)){
_local5=undefined;
_local4=undefined;
_local4=pokers[_local3];
_local5=this.GetCardValue(_local4);
if(((_local1 + _local2) == _local5)){
_local0[_local0.length]=_local4;
_local2=(_local2 + 1);
_local2=1;
}
        
}
_local3=(_local3 + 1);
_local3=0;
}
}
        
return _local0;
---------------------------------------
==========================================================================E
==================================84==================================S
------------------Argv------------------
pokers,tagCard,nextValueList,tagCardValue
---------------------------------------
----------------Content----------------
if((list.length == 0)){
return undefined;
}
_local0=[];
_local1=this.GetCardValue(tagCard);
_local2=0;
while((_local2 < pokers.length)){
_local4=undefined;
_local3=undefined;
_local3=pokers[_local2];
_local4=this.GetCardValue(_local3);
if(((_local1 + 1) == _local4)){
_local0[_local0.length]=_local3;
}
        
}
_local2=(_local2 + 1);
_local2=0;
}
}
        
return _local0;
---------------------------------------
==========================================================================E
==================================85==================================S
------------------Argv------------------
pokers,tagCard,tagCardValue
---------------------------------------
----------------Content----------------
_local0=this.GetCardValue(tagCard);
_local1=0;
while((_local1 < pokers.length)){
_local3=undefined;
_local2=undefined;
_local2=pokers[_local1];
_local3=this.GetCardValue(_local2);
if((_local0 == _local3)){
true;
}
        
}
        
}
        
}
_local1=(_local1 + 1);
_local1=0;
}
}
        
return false;
---------------------------------------
==========================================================================E
==================================86==================================S
------------------Argv------------------
pokers,tagCard,sameValueList,tagCardValue
---------------------------------------
----------------Content----------------
_local0=[];
_local1=this.GetCardValue(tagCard);
_local2=0;
while((_local2 < pokers.length)){
_local4=undefined;
_local3=undefined;
_local3=pokers[_local2];
_local4=this.GetCardValue(_local3);
if((_local1 == _local4)){
_local0[_local0.length]=_local3;
}
        
}
_local2=(_local2 + 1);
_local2=0;
}
}
        
return _local0;
---------------------------------------
==========================================================================E
==================================87==================================S
------------------Argv------------------
pokers,tagCard,guipai,sameValueList,tagCardValue
---------------------------------------
----------------Content----------------
_local0=this.GetGuiPai(pokers);
_local1=[];
_local2=this.GetCardValue(tagCard);
_local3=0;
while((_local3 < pokers.length)){
_local5=undefined;
_local4=undefined;
_local4=pokers[_local3];
if((_local0.indexOf(_local4) != -1)){
}
        
_local5=this.GetCardValue(_local4);
} else if((_local2 == _local5)){
_local1[_local1.length]=_local4;
}
        
}
_local3=(_local3 + 1);
_local3=0;
}
}
        
return _local1;
---------------------------------------
==========================================================================E
==================================88==================================S
------------------Argv------------------
pokers,tagCard,sameColorList
---------------------------------------
----------------Content----------------
_local0=[];
_local1=0;
while((_local1 < pokers.length)){
_local4=undefined;
_local3=undefined;
_local2=undefined;
_local2=pokers[_local1];
_local3=this.GetCardColor(_local2);
_local4=this.GetCardColor(tagCard);
if((_local3 == _local4)){
_local0[_local0.length]=_local2;
}
        
}
_local1=(_local1 + 1);
_local1=0;
}
}
        
return _local0;
---------------------------------------
==========================================================================E
==================================89==================================S
------------------Argv------------------
poker
---------------------------------------
----------------Content----------------
return (poker & this.LOGIC_MASK_VALUE);
---------------------------------------
==========================================================================E
==================================90==================================S
------------------Argv------------------
poker,color
---------------------------------------
----------------Content----------------
_local0=(poker & this.LOGIC_MASK_COLOR);
return _local0;
---------------------------------------
==========================================================================E
==================================91==================================S
------------------Argv------------------
pokers,guipai,array
---------------------------------------
----------------Content----------------
if(!(pokers.length)){
return [];
}
_local0=this.GetGuiPai(pokers);
_local1=[];
_local2=0;
while((_local2 < pokers.length)){
_local4=undefined;
_local3=undefined;
_local3=pokers[_local2];
if((_local0.indexOf(_local3) != -1)){
}
        
} else {
}
_local4={};
_local4.cardValue=this.GetCardValue(_local3);
_local4.cardX16=_local3;
_local1.push(_local4);
}
        
_local2=(_local2 + 1);
_local2=0;
}
}
        
_local1.sort(function () { __FUNC_92__ });
_local2=0;
while((_local2 < _local0.length)){
_local3=undefined;
_local3={};
_local3.cardValue=this.GetCardValue(_local0[_local2]);
_local3.cardX16=_local0[_local2];
_local1.unshift(_local3);
}
        
_local2=(_local2 + 1);
_local2=0;
}
}
        
return _local1;
---------------------------------------
==========================================================================E
==================================92==================================S
------------------Argv------------------
a,b
---------------------------------------
----------------Content----------------
return (b.cardValue - a.cardValue);
---------------------------------------
==========================================================================E
==================================93==================================S
------------------Argv------------------
pokers,guipai,array,newArray
---------------------------------------
----------------Content----------------
if(!(pokers.length)){
return [];
}
_local0=this.GetGuiPai(pokers);
_local1=[];
_local3=0;
while((_local3 < pokers.length)){
_local5=undefined;
_local4=undefined;
_local4=pokers[_local3];
if((_local0.indexOf(_local4) != -1)){
}
        
} else {
}
_local5={};
_local5.cardValue=this.GetCardValue(_local4);
_local5.cardX16=_local4;
_local1.push(_local5);
}
        
_local3=(_local3 + 1);
_local3=0;
}
}
        
_local1.sort(function () { __FUNC_94__ });
_local3=0;
while((_local3 < _local0.length)){
_local4=undefined;
_local4={};
_local4.cardValue=this.GetCardValue(_local0[_local3]);
_local4.cardX16=_local0[_local3];
_local1.unshift(_local4);
}
        
_local3=(_local3 + 1);
_local3=0;
}
}
        
_local2=[];
_local3=0;
while((_local3 < _local1.length)){
_local2.push(_local1[_local3].cardX16);
_local3=(_local3 + 1);
_local3=0;
}
}
        
return _local2;
---------------------------------------
==========================================================================E
==================================94==================================S
------------------Argv------------------
a,b
---------------------------------------
----------------Content----------------
return (b.cardValue - a.cardValue);
---------------------------------------
==========================================================================E
==================================95==================================S
------------------Argv------------------
aCards,bCards,bRet
---------------------------------------
----------------Content----------------
_local0=false;
_local1=0;
while((_local1 < aCards.length)){
_local2=undefined;
_local2=aCards[_local1];
if((bCards.indexOf(_local2) != -1)){
_local0=true;
}
        
break;
}
        
}
_local1=(_local1 + 1);
_local1=0;
}
}
        
return _local0;
---------------------------------------
==========================================================================E
==================================96==================================S
------------------Argv------------------
pokers,self
---------------------------------------
----------------Content----------------
_aliased7961=this;
pokers.sort(function () { __FUNC_97__ });
---------------------------------------
==========================================================================E
==================================97==================================S
------------------Argv------------------
a,b,aValue,bValue
---------------------------------------
----------------Content----------------
_local0=a[0];
_local1=b[0];
return (_aliased7961.GetCardValue(_local0) - _aliased7961.GetCardValue(_local1));
---------------------------------------
==========================================================================E
==================================98==================================S
------------------Argv------------------
pokers,self
---------------------------------------
----------------Content----------------
_aliased6725=this;
pokers.sort(function () { __FUNC_99__ });
---------------------------------------
==========================================================================E
==================================99==================================S
------------------Argv------------------
a,b,aValue,bValue
---------------------------------------
----------------Content----------------
_local0=a[0];
_local1=b[0];
return (_aliased6725.GetCardValue(_local1) - _aliased6725.GetCardValue(_local0));
---------------------------------------
==========================================================================E
==================================100==================================S
------------------Argv------------------
arr,res
---------------------------------------
----------------Content----------------
_local0=this.DeepCopy(arr);
return _local0;
---------------------------------------
==========================================================================E
